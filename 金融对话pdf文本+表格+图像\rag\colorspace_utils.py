"""
颜色空间处理工具类
提供安全的图像颜色空间转换和处理功能
"""
import io
from typing import Optional, Dict, Any, Tuple
from loguru import logger
import numpy as np

try:
    from PIL import Image
    import fitz  # PyMuPDF
    import cv2
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False
    logger.warning("颜色空间处理依赖不可用")

class ColorSpaceHandler:
    """颜色空间处理器"""
    
    @staticmethod
    def safe_pixmap_to_pil(pix) -> Optional[Image.Image]:
        """安全地将PyMuPDF Pixmap转换为PIL Image"""
        if not DEPENDENCIES_AVAILABLE:
            return None
            
        try:
            n_components = pix.n
            has_alpha = pix.alpha
            
            logger.debug(f"Pixmap信息: n={n_components}, alpha={has_alpha}, colorspace={pix.colorspace}")
            
            # 根据组件数和Alpha通道选择转换策略
            if n_components == 1:  # 灰度
                return ColorSpaceHandler._convert_grayscale_pixmap(pix)
            elif n_components == 2:  # 灰度+Alpha
                return ColorSpaceHandler._convert_grayscale_alpha_pixmap(pix)
            elif n_components == 3:  # RGB
                return ColorSpaceHandler._convert_rgb_pixmap(pix)
            elif n_components == 4 and has_alpha == 0:  # CMYK
                return ColorSpaceHandler._convert_cmyk_pixmap(pix)
            elif n_components == 4 and has_alpha == 1:  # RGB+Alpha
                return ColorSpaceHandler._convert_rgba_pixmap(pix)
            else:  # 其他情况
                return ColorSpaceHandler._convert_unknown_pixmap(pix)
                
        except Exception as e:
            logger.error(f"Pixmap转换失败: {e}")
            return ColorSpaceHandler._fallback_pixmap_conversion(pix)
    
    @staticmethod
    def _convert_grayscale_pixmap(pix) -> Optional[Image.Image]:
        """转换灰度Pixmap"""
        try:
            rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
            img_data = rgb_pix.tobytes("ppm")
            pil_image = Image.open(io.BytesIO(img_data))
            rgb_pix = None  # 释放内存
            return pil_image
        except Exception as e:
            logger.warning(f"灰度转换失败: {e}")
            return None
    
    @staticmethod
    def _convert_grayscale_alpha_pixmap(pix) -> Optional[Image.Image]:
        """转换灰度+Alpha Pixmap"""
        try:
            rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
            img_data = rgb_pix.tobytes("ppm")
            pil_image = Image.open(io.BytesIO(img_data))
            rgb_pix = None
            return pil_image
        except Exception as e:
            logger.warning(f"灰度+Alpha转换失败: {e}")
            return None
    
    @staticmethod
    def _convert_rgb_pixmap(pix) -> Optional[Image.Image]:
        """转换RGB Pixmap"""
        try:
            img_data = pix.tobytes("ppm")
            pil_image = Image.open(io.BytesIO(img_data))
            return pil_image
        except Exception as e:
            logger.warning(f"RGB转换失败: {e}")
            return None
    
    @staticmethod
    def _convert_cmyk_pixmap(pix) -> Optional[Image.Image]:
        """转换CMYK Pixmap"""
        try:
            rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
            img_data = rgb_pix.tobytes("ppm")
            pil_image = Image.open(io.BytesIO(img_data))
            rgb_pix = None
            return pil_image
        except Exception as e:
            logger.warning(f"CMYK转换失败: {e}")
            return None
    
    @staticmethod
    def _convert_rgba_pixmap(pix) -> Optional[Image.Image]:
        """转换RGB+Alpha Pixmap"""
        try:
            # 方法1: 直接转换为RGB，丢弃Alpha通道
            try:
                rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
                img_data = rgb_pix.tobytes("ppm")
                pil_image = Image.open(io.BytesIO(img_data))
                rgb_pix = None
                return pil_image
            except:
                pass

            # 方法2: 使用PNG格式保留Alpha通道，然后转换
            try:
                img_data = pix.tobytes("png")
                pil_image = Image.open(io.BytesIO(img_data))
                # 如果是RGBA，转换为RGB（白色背景）
                if pil_image.mode == 'RGBA':
                    rgb_image = Image.new('RGB', pil_image.size, (255, 255, 255))
                    rgb_image.paste(pil_image, mask=pil_image.split()[-1])  # 使用Alpha通道作为mask
                    return rgb_image
                return pil_image
            except:
                pass

            # 方法3: 直接从像素数据创建
            try:
                width, height = pix.width, pix.height
                samples = pix.samples
                if pix.n == 4:  # RGBA
                    # 转换RGBA到RGB
                    rgb_data = []
                    for i in range(0, len(samples), 4):
                        r, g, b, a = samples[i:i+4]
                        # 简单的Alpha混合到白色背景
                        alpha = a / 255.0
                        r = int(r * alpha + 255 * (1 - alpha))
                        g = int(g * alpha + 255 * (1 - alpha))
                        b = int(b * alpha + 255 * (1 - alpha))
                        rgb_data.extend([r, g, b])

                    pil_image = Image.frombytes('RGB', (width, height), bytes(rgb_data))
                    return pil_image
            except:
                pass

            logger.warning("所有RGBA转换方法都失败")
            return None

        except Exception as e:
            logger.warning(f"RGBA转换失败: {e}")
            return None
    
    @staticmethod
    def _convert_unknown_pixmap(pix) -> Optional[Image.Image]:
        """转换未知格式Pixmap"""
        try:
            # 尝试强制转换为RGB
            rgb_pix = fitz.Pixmap(fitz.csRGB, pix)
            img_data = rgb_pix.tobytes("ppm")
            pil_image = Image.open(io.BytesIO(img_data))
            rgb_pix = None
            return pil_image
        except Exception as e:
            logger.warning(f"未知格式转换失败: {e}")
            return None
    
    @staticmethod
    def _fallback_pixmap_conversion(pix) -> Optional[Image.Image]:
        """备用Pixmap转换方案"""
        try:
            # 使用PNG格式作为备用
            img_bytes = pix.tobytes("png")
            pil_image = Image.open(io.BytesIO(img_bytes))
            
            # 确保是RGB模式
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            return pil_image
        except Exception as e:
            logger.error(f"备用转换也失败: {e}")
            return None
    
    @staticmethod
    def safe_convert_to_rgb(image: Image.Image) -> Image.Image:
        """安全地转换图像为RGB模式"""
        if not DEPENDENCIES_AVAILABLE:
            return image
            
        try:
            original_mode = image.mode
            
            if original_mode == 'RGB':
                return image
            elif original_mode == 'RGBA':
                # RGBA转RGB，使用白色背景
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                rgb_image.paste(image, mask=image.split()[-1])
                return rgb_image
            elif original_mode == 'LA':
                # 灰度+Alpha转RGB
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                gray_rgb = image.convert('RGB')
                if len(image.split()) > 1:
                    rgb_image.paste(gray_rgb, mask=image.split()[-1])
                else:
                    rgb_image = gray_rgb
                return rgb_image
            elif original_mode in ['P', 'L', 'CMYK', '1']:
                # 调色板、灰度、CMYK、二值图像
                return image.convert('RGB')
            else:
                # 其他模式，强制转换
                logger.warning(f"未知图像模式 {original_mode}，强制转换为RGB")
                return image.convert('RGB')
                
        except Exception as e:
            logger.error(f"RGB转换失败: {e}")
            # 最后的备用方案
            try:
                return image.convert('RGB')
            except:
                logger.error("所有RGB转换方案都失败")
                return image
    
    @staticmethod
    def safe_opencv_convert(image: Image.Image) -> Optional[np.ndarray]:
        """安全地为OpenCV转换图像"""
        if not DEPENDENCIES_AVAILABLE:
            return None
            
        try:
            # 确保是RGB模式
            rgb_image = ColorSpaceHandler.safe_convert_to_rgb(image)
            
            # 转换为numpy数组
            img_array = np.array(rgb_image)
            
            # 检查数组形状
            if len(img_array.shape) == 3 and img_array.shape[2] == 3:
                # RGB图像，转换为灰度
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                return gray
            elif len(img_array.shape) == 3 and img_array.shape[2] == 4:
                # RGBA图像，先转RGB再转灰度
                rgb_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)
                gray = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2GRAY)
                return gray
            elif len(img_array.shape) == 2:
                # 已经是灰度
                return img_array
            else:
                # 使用PIL转换
                gray_image = rgb_image.convert('L')
                return np.array(gray_image)
                
        except Exception as e:
            logger.error(f"OpenCV转换失败: {e}")
            try:
                # 备用方案：直接用PIL转灰度
                gray_image = image.convert('L')
                return np.array(gray_image)
            except Exception as backup_error:
                logger.error(f"备用OpenCV转换也失败: {backup_error}")
                return None
    
    @staticmethod
    def safe_analyze_colors(image: Image.Image) -> Dict[str, Any]:
        """安全地分析图像颜色"""
        if not DEPENDENCIES_AVAILABLE:
            return {"error": "依赖不可用"}
            
        try:
            # 转换为RGB
            rgb_image = ColorSpaceHandler.safe_convert_to_rgb(image)
            original_mode = image.mode
            
            # 获取颜色
            try:
                colors = rgb_image.getcolors(maxcolors=256*256*256)
                if colors:
                    colors.sort(key=lambda x: x[0], reverse=True)
                    dominant_colors = colors[:5]
                    
                    formatted_colors = []
                    for count, color in dominant_colors:
                        try:
                            if isinstance(color, (tuple, list)) and len(color) >= 3:
                                formatted_colors.append({
                                    "color": tuple(color[:3]),
                                    "frequency": int(count)
                                })
                        except Exception as color_error:
                            logger.warning(f"格式化颜色失败: {color_error}")
                            continue
                    
                    return {
                        "dominant_colors": formatted_colors,
                        "total_colors": len(colors),
                        "original_mode": original_mode
                    }
                else:
                    return {
                        "dominant_colors": [],
                        "total_colors": 0,
                        "original_mode": original_mode
                    }
                    
            except Exception as getcolors_error:
                logger.warning(f"获取颜色失败: {getcolors_error}")
                return {
                    "dominant_colors": [],
                    "total_colors": 0,
                    "original_mode": original_mode,
                    "error": "颜色分析失败"
                }
                
        except Exception as e:
            logger.error(f"颜色分析失败: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def calculate_image_complexity(image: Image.Image) -> float:
        """计算图像复杂度"""
        if not DEPENDENCIES_AVAILABLE:
            return 0.0
            
        try:
            # 转换为灰度数组
            gray_array = ColorSpaceHandler.safe_opencv_convert(image)
            
            if gray_array is None:
                return 0.0
            
            # 边缘检测
            edges = cv2.Canny(gray_array, 50, 150)
            
            # 计算边缘像素比例
            edge_ratio = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            return float(edge_ratio)
            
        except Exception as e:
            logger.error(f"复杂度计算失败: {e}")
            return 0.0
    
    @staticmethod
    def validate_image(image: Image.Image) -> Tuple[bool, str]:
        """验证图像是否有效"""
        try:
            if image is None:
                return False, "图像为空"
            
            if image.size[0] == 0 or image.size[1] == 0:
                return False, "图像尺寸为0"
            
            # 尝试访问图像数据
            _ = image.mode
            _ = image.size
            
            return True, "图像有效"
            
        except Exception as e:
            return False, f"图像验证失败: {e}"
