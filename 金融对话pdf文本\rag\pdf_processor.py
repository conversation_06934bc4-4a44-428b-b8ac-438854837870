"""
PDF文档处理模块
使用LangChain框架处理PDF文件，提取文本并分块存储到Milvus知识库
"""
import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger

# LangChain相关导入
from langchain.document_loaders import PyPDFLoader, UnstructuredPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, CharacterTextSplitter
from langchain.schema import Document

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.rag_system import RAGSystem
from idconfig.config import Config

class PDFProcessor:
    """PDF文档处理器"""
    
    def __init__(self):
        self.config = Config()
        self.rag_system = RAGSystem()
        
        # 文本分割器配置
        self.chunk_size = 1000  # 每个文本块的字符数
        self.chunk_overlap = 200  # 文本块之间的重叠字符数
        
        # 初始化文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", "。", "！", "？", "；", "，", " ", ""]
        )
        
    def initialize(self):
        """初始化PDF处理器"""
        try:
            # 初始化RAG系统
            if not self.rag_system.initialize():
                logger.error("RAG系统初始化失败")
                return False
            
            logger.info("PDF处理器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"PDF处理器初始化失败: {e}")
            return False
    
    def load_pdf_with_pypdf(self, file_path: str) -> List[Document]:
        """使用PyPDFLoader加载PDF文件"""
        try:
            loader = PyPDFLoader(file_path)
            documents = loader.load()
            logger.info(f"使用PyPDFLoader成功加载PDF: {file_path}, 页数: {len(documents)}")
            return documents
        except Exception as e:
            logger.error(f"PyPDFLoader加载PDF失败: {e}")
            return []
    
    def load_pdf_with_unstructured(self, file_path: str) -> List[Document]:
        """使用UnstructuredPDFLoader加载PDF文件"""
        try:
            loader = UnstructuredPDFLoader(file_path)
            documents = loader.load()
            logger.info(f"使用UnstructuredPDFLoader成功加载PDF: {file_path}, 文档数: {len(documents)}")
            return documents
        except Exception as e:
            logger.error(f"UnstructuredPDFLoader加载PDF失败: {e}")
            return []
    
    def load_pdf(self, file_path: str, loader_type: str = "pypdf") -> List[Document]:
        """
        加载PDF文件
        
        Args:
            file_path: PDF文件路径
            loader_type: 加载器类型 ("pypdf" 或 "unstructured")
        
        Returns:
            文档列表
        """
        if not os.path.exists(file_path):
            logger.error(f"PDF文件不存在: {file_path}")
            return []
        
        if not file_path.lower().endswith('.pdf'):
            logger.error(f"文件不是PDF格式: {file_path}")
            return []
        
        logger.info(f"开始加载PDF文件: {file_path}")
        
        if loader_type == "pypdf":
            documents = self.load_pdf_with_pypdf(file_path)
        elif loader_type == "unstructured":
            documents = self.load_pdf_with_unstructured(file_path)
        else:
            logger.error(f"不支持的加载器类型: {loader_type}")
            return []
        
        # 如果第一种方法失败，尝试另一种方法
        if not documents and loader_type == "pypdf":
            logger.warning("PyPDFLoader失败，尝试使用UnstructuredPDFLoader")
            documents = self.load_pdf_with_unstructured(file_path)
        elif not documents and loader_type == "unstructured":
            logger.warning("UnstructuredPDFLoader失败，尝试使用PyPDFLoader")
            documents = self.load_pdf_with_pypdf(file_path)
        
        return documents
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """分割文档为小块"""
        try:
            if not documents:
                logger.warning("没有文档需要分割")
                return []
            
            # 使用文本分割器分割文档
            split_docs = self.text_splitter.split_documents(documents)
            
            logger.info(f"文档分割完成: {len(documents)} -> {len(split_docs)} 块")
            return split_docs
            
        except Exception as e:
            logger.error(f"文档分割失败: {e}")
            return []
    
    def extract_metadata(self, document: Document, file_path: str, chunk_index: int) -> Dict[str, Any]:
        """提取文档元数据"""
        try:
            metadata = {
                "source": file_path,
                "file_name": os.path.basename(file_path),
                "chunk_index": chunk_index,
                "content_length": len(document.page_content),
                "category": "金融文档"  # 默认分类
            }
            
            # 合并原有元数据
            if hasattr(document, 'metadata') and document.metadata:
                metadata.update(document.metadata)
            
            return metadata
            
        except Exception as e:
            logger.error(f"提取元数据失败: {e}")
            return {"source": file_path, "category": "金融文档"}
    
    def process_pdf_to_knowledge_base(self, file_path: str, category: str = "金融文档", 
                                    loader_type: str = "pypdf") -> bool:
        """
        处理PDF文件并添加到知识库
        
        Args:
            file_path: PDF文件路径
            category: 文档分类
            loader_type: 加载器类型
        
        Returns:
            处理是否成功
        """
        try:
            logger.info(f"开始处理PDF文件: {file_path}")
            
            # 1. 加载PDF文档
            documents = self.load_pdf(file_path, loader_type)
            if not documents:
                logger.error("PDF文档加载失败")
                return False
            
            # 2. 分割文档
            split_docs = self.split_documents(documents)
            if not split_docs:
                logger.error("文档分割失败")
                return False
            
            # 3. 准备知识库数据
            knowledge_list = []
            for i, doc in enumerate(split_docs):
                # 清理文本内容
                content = doc.page_content.strip()
                if len(content) < 10:  # 跳过过短的内容
                    continue
                
                # 提取元数据
                metadata = self.extract_metadata(doc, file_path, i)
                
                knowledge_item = {
                    "content": content,
                    "category": category,
                    "source": f"{os.path.basename(file_path)} - 第{i+1}块"
                }
                knowledge_list.append(knowledge_item)
            
            # 4. 批量添加到知识库
            if knowledge_list:
                success = self.rag_system.batch_add_knowledge(knowledge_list)
                if success:
                    logger.info(f"成功处理PDF文件: {file_path}, 添加了 {len(knowledge_list)} 个知识块")
                    return True
                else:
                    logger.error("批量添加知识到数据库失败")
                    return False
            else:
                logger.warning("没有有效的知识内容可添加")
                return False
                
        except Exception as e:
            logger.error(f"处理PDF文件失败: {e}")
            return False
    
    def process_pdf_directory(self, directory_path: str, category: str = "金融文档") -> Dict[str, bool]:
        """
        批量处理目录中的所有PDF文件
        
        Args:
            directory_path: 目录路径
            category: 文档分类
        
        Returns:
            处理结果字典 {文件名: 是否成功}
        """
        try:
            if not os.path.exists(directory_path):
                logger.error(f"目录不存在: {directory_path}")
                return {}
            
            # 查找所有PDF文件
            pdf_files = []
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    if file.lower().endswith('.pdf'):
                        pdf_files.append(os.path.join(root, file))
            
            if not pdf_files:
                logger.warning(f"目录中没有找到PDF文件: {directory_path}")
                return {}
            
            logger.info(f"找到 {len(pdf_files)} 个PDF文件，开始批量处理")
            
            # 批量处理
            results = {}
            for pdf_file in pdf_files:
                try:
                    success = self.process_pdf_to_knowledge_base(pdf_file, category)
                    results[os.path.basename(pdf_file)] = success
                    logger.info(f"文件 {os.path.basename(pdf_file)} 处理{'成功' if success else '失败'}")
                except Exception as e:
                    logger.error(f"处理文件 {pdf_file} 时出错: {e}")
                    results[os.path.basename(pdf_file)] = False
            
            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            logger.info(f"批量处理完成: {success_count}/{total_count} 个文件成功")
            
            return results
            
        except Exception as e:
            logger.error(f"批量处理PDF目录失败: {e}")
            return {}
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            # 这里可以添加统计信息的获取逻辑
            stats = {
                "chunk_size": self.chunk_size,
                "chunk_overlap": self.chunk_overlap,
                "text_splitter_type": "RecursiveCharacterTextSplitter"
            }
            return stats
        except Exception as e:
            logger.error(f"获取处理统计信息失败: {e}")
            return {}
