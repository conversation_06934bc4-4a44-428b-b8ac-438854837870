# 金融对话系统环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# Milvus向量数据库配置
# =============================================================================
MILVUS_HOST=localhost
MILVUS_PORT=19530

# =============================================================================
# 嵌入模型配置
# =============================================================================
# BGE-M3模型路径（相对于项目根目录）
EMBEDDING_MODEL=./bge-m3
# BGE-M3向量维度（固定1024维）
VECTOR_DIM=1024

# =============================================================================
# RAG检索配置
# =============================================================================
# 检索结果数量
TOP_K=5
# 余弦相似度阈值（范围[-1,1]，推荐0.5以上）
SIMILARITY_THRESHOLD=0.5

# =============================================================================
# FastAPI服务器配置
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/financial_chat.log

# =============================================================================
# 大模型API配置（至少配置一个）
# =============================================================================

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
MODEL_NAME=gpt-3.5-turbo

# 智谱AI配置
ZHIPU_API_KEY=your_zhipu_api_key_here
ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4
ZHIPU_MODEL=glm-4

# 通义千问配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
QWEN_MODEL=qwen-turbo

# 硅基流动配置
SILICONFLOW_API_KEY=your_siliconflow_api_key_here
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1
SILICONFLOW_MODEL=deepseek-ai/DeepSeek-R1-0528-Qwen3-8B

# 百度文心一言配置
BAIDU_API_KEY=your_baidu_api_key_here
BAIDU_SECRET_KEY=your_baidu_secret_key_here
BAIDU_BASE_URL=https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop
BAIDU_MODEL=ernie-bot-turbo

# 讯飞星火配置
XUNFEI_APP_ID=your_xunfei_app_id_here
XUNFEI_API_KEY=your_xunfei_api_key_here
XUNFEI_API_SECRET=your_xunfei_api_secret_here
XUNFEI_BASE_URL=wss://spark-api.xf-yun.com/v3.5/chat
XUNFEI_MODEL=spark-3.5

# 腾讯混元配置
TENCENT_SECRET_ID=your_tencent_secret_id_here
TENCENT_SECRET_KEY=your_tencent_secret_key_here
TENCENT_REGION=ap-beijing
TENCENT_MODEL=hunyuan-lite

# 字节豆包配置
DOUBAO_API_KEY=your_doubao_api_key_here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
DOUBAO_MODEL=ep-20241220105718-8xqkz

# Anthropic Claude配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Google Gemini配置
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com/v1beta
GOOGLE_MODEL=gemini-pro

# Moonshot AI (Kimi)配置
MOONSHOT_API_KEY=your_moonshot_api_key_here
MOONSHOT_BASE_URL=https://api.moonshot.cn/v1
MOONSHOT_MODEL=moonshot-v1-8k

# DeepSeek配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# =============================================================================
# 配置说明
# =============================================================================
# 1. 至少需要配置一个大模型API密钥
# 2. BGE-M3模型需要下载到 ./bge-m3 目录
# 3. Milvus需要使用Docker启动
# 4. 日志文件会自动创建logs目录
# 5. 相似度阈值建议设置为0.5-0.8之间
