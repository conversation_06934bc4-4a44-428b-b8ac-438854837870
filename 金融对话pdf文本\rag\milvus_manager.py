"""
Milvus向量数据库管理模块
负责管理两个知识库：专业信息知识库和历史对话存储
"""
from pymilvus import connections, Collection, CollectionSchema, FieldSchema, DataType, utility
from loguru import logger
from typing import List, Dict, Any
import sys
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from idconfig.config import Config

class MilvusManager:
    def __init__(self):
        self.config = Config()
        self.connection_alias = "default"
        self.knowledge_collection = None
        self.history_collection = None
        
    def connect(self):
        """连接到Milvus数据库"""
        try:
            connections.connect(
                alias=self.connection_alias,
                host=self.config.MILVUS_HOST,
                port=self.config.MILVUS_PORT
            )
            logger.info(f"成功连接到Milvus: {self.config.MILVUS_HOST}:{self.config.MILVUS_PORT}")
            return True
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            return False
    
    def create_knowledge_collection(self):
        """创建专业信息和术语知识库集合"""
        try:
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="category", dtype=DataType.VARCHAR, max_length=100),
                FieldSchema(name="source", dtype=DataType.VARCHAR, max_length=500),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.config.VECTOR_DIM)
            ]
            
            # 创建集合schema
            schema = CollectionSchema(
                fields=fields,
                description="金融专业信息和术语知识库"
            )
            
            # 检查集合是否存在
            if utility.has_collection(self.config.KNOWLEDGE_COLLECTION):
                logger.info(f"知识库集合 {self.config.KNOWLEDGE_COLLECTION} 已存在")
                self.knowledge_collection = Collection(self.config.KNOWLEDGE_COLLECTION)
            else:
                # 创建集合
                self.knowledge_collection = Collection(
                    name=self.config.KNOWLEDGE_COLLECTION,
                    schema=schema
                )
                logger.info(f"成功创建知识库集合: {self.config.KNOWLEDGE_COLLECTION}")
            
            # 创建索引 (适配BGE-M3归一化向量，使用余弦相似度)
            index_params = {
                "metric_type": "COSINE",  # 使用余弦相似度，适合归一化向量
                "index_type": "IVF_FLAT",
                "params": {"nlist": 256}  # 增加nlist以适应1024维向量
            }
            
            if not self.knowledge_collection.has_index():
                self.knowledge_collection.create_index(
                    field_name="embedding",
                    index_params=index_params
                )
                logger.info("知识库集合索引创建完成")
            
            return True
            
        except Exception as e:
            logger.error(f"创建知识库集合失败: {e}")
            return False
    
    def create_history_collection(self):
        """创建历史对话存储集合"""
        try:
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="session_id", dtype=DataType.VARCHAR, max_length=100),
                FieldSchema(name="user_query", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="assistant_response", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="timestamp", dtype=DataType.INT64),
                FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=self.config.VECTOR_DIM)
            ]
            
            # 创建集合schema
            schema = CollectionSchema(
                fields=fields,
                description="历史对话存储"
            )
            
            # 检查集合是否存在
            if utility.has_collection(self.config.HISTORY_COLLECTION):
                logger.info(f"历史对话集合 {self.config.HISTORY_COLLECTION} 已存在")
                self.history_collection = Collection(self.config.HISTORY_COLLECTION)
            else:
                # 创建集合
                self.history_collection = Collection(
                    name=self.config.HISTORY_COLLECTION,
                    schema=schema
                )
                logger.info(f"成功创建历史对话集合: {self.config.HISTORY_COLLECTION}")
            
            # 创建索引 (适配BGE-M3归一化向量，使用余弦相似度)
            index_params = {
                "metric_type": "COSINE",  # 使用余弦相似度，适合归一化向量
                "index_type": "IVF_FLAT",
                "params": {"nlist": 256}  # 增加nlist以适应1024维向量
            }
            
            if not self.history_collection.has_index():
                self.history_collection.create_index(
                    field_name="embedding",
                    index_params=index_params
                )
                logger.info("历史对话集合索引创建完成")
            
            return True
            
        except Exception as e:
            logger.error(f"创建历史对话集合失败: {e}")
            return False
    
    def insert_knowledge(self, data: List[Dict[str, Any]]):
        """插入知识库数据"""
        try:
            if not self.knowledge_collection:
                logger.error("知识库集合未初始化")
                return False
            
            # 准备数据
            entities = [
                [item["content"] for item in data],
                [item["category"] for item in data],
                [item["source"] for item in data],
                [item["embedding"] for item in data]
            ]
            
            # 插入数据
            self.knowledge_collection.insert(entities)
            self.knowledge_collection.flush()
            logger.info(f"成功插入 {len(data)} 条知识库数据")
            return True
            
        except Exception as e:
            logger.error(f"插入知识库数据失败: {e}")
            return False
    
    def insert_history(self, data: List[Dict[str, Any]]):
        """插入历史对话数据"""
        try:
            if not self.history_collection:
                logger.error("历史对话集合未初始化")
                return False
            
            # 准备数据
            entities = [
                [item["session_id"] for item in data],
                [item["user_query"] for item in data],
                [item["assistant_response"] for item in data],
                [item["timestamp"] for item in data],
                [item["embedding"] for item in data]
            ]
            
            # 插入数据
            self.history_collection.insert(entities)
            self.history_collection.flush()
            logger.info(f"成功插入 {len(data)} 条历史对话数据")
            return True
            
        except Exception as e:
            logger.error(f"插入历史对话数据失败: {e}")
            return False
    
    def search_knowledge(self, query_embedding: List[float], top_k: int = None):
        """搜索知识库"""
        try:
            if not self.knowledge_collection:
                logger.error("知识库集合未初始化")
                return []
            
            top_k = top_k or self.config.TOP_K
            
            # 加载集合
            self.knowledge_collection.load()
            
            # 搜索参数 (匹配索引配置)
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 16}}
            
            # 执行搜索
            results = self.knowledge_collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                output_fields=["content", "category", "source"]
            )
            
            # 处理结果
            search_results = []
            for hits in results:
                for hit in hits:
                    search_results.append({
                        "content": hit.entity.get("content"),
                        "category": hit.entity.get("category"),
                        "source": hit.entity.get("source"),
                        "score": hit.score
                    })
            
            return search_results
            
        except Exception as e:
            logger.error(f"搜索知识库失败: {e}")
            return []
    
    def search_history(self, query_embedding: List[float], top_k: int = None):
        """搜索历史对话"""
        try:
            if not self.history_collection:
                logger.error("历史对话集合未初始化")
                return []
            
            top_k = top_k or self.config.TOP_K
            
            # 加载集合
            self.history_collection.load()
            
            # 搜索参数 (匹配索引配置)
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 16}}
            
            # 执行搜索
            results = self.history_collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                output_fields=["session_id", "user_query", "assistant_response", "timestamp"]
            )
            
            # 处理结果
            search_results = []
            for hits in results:
                for hit in hits:
                    search_results.append({
                        "session_id": hit.entity.get("session_id"),
                        "user_query": hit.entity.get("user_query"),
                        "assistant_response": hit.entity.get("assistant_response"),
                        "timestamp": hit.entity.get("timestamp"),
                        "score": hit.score
                    })
            
            return search_results
            
        except Exception as e:
            logger.error(f"搜索历史对话失败: {e}")
            return []
    
    def initialize(self):
        """初始化Milvus管理器"""
        if not self.connect():
            return False
        
        if not self.create_knowledge_collection():
            return False
        
        if not self.create_history_collection():
            return False
        
        logger.info("Milvus管理器初始化完成")
        return True
