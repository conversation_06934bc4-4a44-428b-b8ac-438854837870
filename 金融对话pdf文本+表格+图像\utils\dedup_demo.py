"""
去重功能演示和测试
展示如何使用各种去重工具
"""
import os
import sys
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from utils.deduplication import (
    TextDeduplicator, 
    VectorDeduplicator, 
    PDFDeduplicator, 
    HistoryDeduplicator,
    deduplicate_texts,
    deduplicate_vectors,
    deduplicate_pdfs,
    deduplicate_conversations
)
from utils.dedup_manager import DeduplicationManager, dedup_manager
from utils.fast_dedup import (
    fast_deduplicate_texts,
    fast_deduplicate_vectors,
    fast_deduplicate_pdfs
)
from loguru import logger


def demo_text_deduplication():
    """演示文本去重功能"""
    print("\n" + "="*50)
    print("文本去重演示")
    print("="*50)
    
    # 创建测试文本
    test_texts = [
        "这是一个关于金融的文档内容",
        "这是一个关于金融的文档内容",  # 完全重复
        "这是一个关于金融的文档内容。",  # 相似但不完全相同
        "这是另一个不同的金融文档",
        "完全不同的内容，关于技术方面",
        "这是一个关于金融的文档内容",  # 又一个重复
        "金融市场分析报告",
        "金融市场分析报告",  # 重复
        "股票投资策略研究",
        "债券市场走势分析"
    ]
    
    print(f"原始文本数量: {len(test_texts)}")
    print("原始文本:")
    for i, text in enumerate(test_texts, 1):
        print(f"  {i}. {text}")
    
    # 精确去重
    print("\n--- 精确去重 ---")
    start_time = time.time()
    exact_unique = deduplicate_texts(test_texts, use_fuzzy=False)
    exact_time = time.time() - start_time
    print(f"精确去重结果: {len(test_texts)} -> {len(exact_unique)}")
    print(f"耗时: {exact_time:.4f} 秒")
    
    # 模糊去重
    print("\n--- 模糊去重 ---")
    start_time = time.time()
    fuzzy_unique = deduplicate_texts(test_texts, use_fuzzy=True)
    fuzzy_time = time.time() - start_time
    print(f"模糊去重结果: {len(test_texts)} -> {len(fuzzy_unique)}")
    print(f"耗时: {fuzzy_time:.4f} 秒")
    
    print("\n去重后文本:")
    for i, text in enumerate(fuzzy_unique, 1):
        print(f"  {i}. {text}")
    
    # 快速去重对比
    print("\n--- 快速去重对比 ---")
    large_texts = test_texts * 100  # 1000个文本
    
    start_time = time.time()
    fast_unique = fast_deduplicate_texts(large_texts)
    fast_time = time.time() - start_time
    
    print(f"快速去重 {len(large_texts)} 个文本: -> {len(fast_unique)}")
    print(f"耗时: {fast_time:.4f} 秒")
    print(f"处理速度: {len(large_texts) / fast_time:.0f} 文本/秒")


def demo_vector_deduplication():
    """演示向量去重功能"""
    print("\n" + "="*50)
    print("向量去重演示")
    print("="*50)
    
    # 创建测试向量数据
    import numpy as np
    
    test_vectors = [
        {
            "content": "金融市场分析",
            "embedding": [0.1, 0.2, 0.3, 0.4, 0.5],
            "category": "金融",
            "source": "报告1"
        },
        {
            "content": "金融市场分析",  # 相同内容
            "embedding": [0.1, 0.2, 0.3, 0.4, 0.5],  # 相同向量
            "category": "金融",
            "source": "报告2"
        },
        {
            "content": "股票投资策略",
            "embedding": [0.2, 0.3, 0.4, 0.5, 0.6],
            "category": "投资",
            "source": "报告3"
        },
        {
            "content": "债券市场研究",
            "embedding": [0.15, 0.25, 0.35, 0.45, 0.55],  # 相似向量
            "category": "债券",
            "source": "报告4"
        },
        {
            "content": "外汇交易分析",
            "embedding": [0.8, 0.9, 0.1, 0.2, 0.3],  # 不同向量
            "category": "外汇",
            "source": "报告5"
        }
    ]
    
    print(f"原始向量数据数量: {len(test_vectors)}")
    
    # 向量去重
    start_time = time.time()
    unique_vectors = deduplicate_vectors(test_vectors)
    dedup_time = time.time() - start_time
    
    print(f"去重后向量数据数量: {len(unique_vectors)}")
    print(f"耗时: {dedup_time:.4f} 秒")
    
    print("\n去重后向量数据:")
    for i, vec in enumerate(unique_vectors, 1):
        print(f"  {i}. {vec['content']} - {vec['category']}")


def demo_pdf_deduplication():
    """演示PDF去重功能"""
    print("\n" + "="*50)
    print("PDF文件去重演示")
    print("="*50)
    
    # 查找PDF文件
    pdf_dir = Path("texts_pdf")
    if pdf_dir.exists():
        pdf_files = list(pdf_dir.glob("*.pdf"))
        
        if pdf_files:
            print(f"找到 {len(pdf_files)} 个PDF文件:")
            for pdf in pdf_files:
                print(f"  - {pdf.name}")
            
            # 创建重复文件列表进行测试
            test_pdfs = [str(pdf) for pdf in pdf_files]
            test_pdfs.extend([str(pdf) for pdf in pdf_files[:2]])  # 添加重复
            
            print(f"\n测试文件列表 (包含重复): {len(test_pdfs)} 个")
            
            # PDF去重
            start_time = time.time()
            unique_pdfs = deduplicate_pdfs(test_pdfs)
            dedup_time = time.time() - start_time
            
            print(f"去重后PDF文件数量: {len(unique_pdfs)}")
            print(f"耗时: {dedup_time:.4f} 秒")
        else:
            print("未找到PDF文件，跳过PDF去重演示")
    else:
        print("PDF目录不存在，跳过PDF去重演示")


def demo_conversation_deduplication():
    """演示对话去重功能"""
    print("\n" + "="*50)
    print("对话去重演示")
    print("="*50)
    
    # 创建测试对话数据
    test_conversations = [
        {
            "session_id": "session1",
            "user_query": "什么是股票？",
            "assistant_response": "股票是公司所有权的证明，代表股东对公司的部分所有权。",
            "timestamp": time.time()
        },
        {
            "session_id": "session2",
            "user_query": "什么是股票？",  # 重复问题
            "assistant_response": "股票是公司所有权的证明，代表股东对公司的部分所有权。",  # 重复回答
            "timestamp": time.time()
        },
        {
            "session_id": "session3",
            "user_query": "如何投资股票？",
            "assistant_response": "投资股票需要开设证券账户，选择合适的股票，制定投资策略。",
            "timestamp": time.time()
        },
        {
            "session_id": "session4",
            "user_query": "债券和股票的区别？",
            "assistant_response": "债券是债权凭证，股票是所有权凭证，风险和收益特征不同。",
            "timestamp": time.time()
        }
    ]
    
    print(f"原始对话数量: {len(test_conversations)}")
    
    # 对话去重
    start_time = time.time()
    unique_conversations = deduplicate_conversations(test_conversations)
    dedup_time = time.time() - start_time
    
    print(f"去重后对话数量: {len(unique_conversations)}")
    print(f"耗时: {dedup_time:.4f} 秒")
    
    print("\n去重后对话:")
    for i, conv in enumerate(unique_conversations, 1):
        print(f"  {i}. Q: {conv['user_query']}")
        print(f"     A: {conv['assistant_response'][:50]}...")


def demo_integrated_deduplication():
    """演示集成去重管理器"""
    print("\n" + "="*50)
    print("集成去重管理器演示")
    print("="*50)
    
    # 创建测试数据
    test_texts = ["文本1", "文本1", "文本2", "文本3"] * 50
    test_vectors = [
        {"content": f"内容{i}", "embedding": [i*0.1, i*0.2, i*0.3]} 
        for i in range(20)
    ]
    # 添加重复向量
    test_vectors.extend(test_vectors[:5])
    
    print(f"测试数据: {len(test_texts)} 个文本, {len(test_vectors)} 个向量")
    
    # 使用集成管理器
    manager = DeduplicationManager()
    
    # 批量文本去重
    start_time = time.time()
    unique_texts = manager.batch_deduplicate_texts(test_texts, batch_size=50)
    text_time = time.time() - start_time
    
    # 向量去重
    start_time = time.time()
    unique_vectors = manager.vector_deduplicator.deduplicate_vectors(test_vectors)
    vector_time = time.time() - start_time
    
    print(f"文本去重: {len(test_texts)} -> {len(unique_texts)}, 耗时: {text_time:.4f}秒")
    print(f"向量去重: {len(test_vectors)} -> {len(unique_vectors)}, 耗时: {vector_time:.4f}秒")
    
    # 显示统计信息
    stats = manager.get_deduplication_stats()
    print(f"\n去重统计信息:")
    print(f"  文本缓存大小: {stats['text_cache_size']}")
    print(f"  向量缓存大小: {stats['vector_cache_size']}")
    print(f"  PDF缓存大小: {stats['pdf_cache_size']}")
    print(f"  历史缓存大小: {stats['history_cache_size']}")


def performance_comparison():
    """性能对比测试"""
    print("\n" + "="*50)
    print("性能对比测试")
    print("="*50)
    
    # 创建大量测试数据
    sizes = [100, 500, 1000, 2000]
    
    for size in sizes:
        print(f"\n--- 测试数据量: {size} ---")
        
        # 创建测试文本
        test_texts = [f"测试文本{i%50}" for i in range(size)]  # 50种不同文本重复
        
        # 标准去重
        start_time = time.time()
        standard_result = deduplicate_texts(test_texts, use_fuzzy=False)
        standard_time = time.time() - start_time
        
        # 快速去重
        start_time = time.time()
        fast_result = fast_deduplicate_texts(test_texts)
        fast_time = time.time() - start_time
        
        print(f"标准去重: {len(test_texts)} -> {len(standard_result)}, "
              f"耗时: {standard_time:.4f}秒, 速度: {size/standard_time:.0f} 文本/秒")
        print(f"快速去重: {len(test_texts)} -> {len(fast_result)}, "
              f"耗时: {fast_time:.4f}秒, 速度: {size/fast_time:.0f} 文本/秒")
        print(f"加速比: {standard_time/fast_time:.2f}x")


def main():
    """主演示函数"""
    print("金融对话系统 - 去重功能演示")
    print("="*60)
    
    try:
        # 各种去重演示
        demo_text_deduplication()
        demo_vector_deduplication()
        demo_pdf_deduplication()
        demo_conversation_deduplication()
        demo_integrated_deduplication()
        performance_comparison()
        
        print("\n" + "="*60)
        print("演示完成！")
        
        # 显示最终统计
        stats = dedup_manager.get_deduplication_stats()
        print(f"\n最终缓存统计:")
        for key, value in stats.items():
            if key != "cache_files":
                print(f"  {key}: {value}")
        
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
