"""
简单的去重功能测试
"""
import sys
import os
from pathlib import Path

# 添加父目录到Python路径
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

def test_basic_imports():
    """测试基本导入"""
    try:
        from utils.deduplication import TextDeduplicator, VectorDeduplicator
        print("✓ 基本去重模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 基本去重模块导入失败: {e}")
        return False

def test_text_deduplication():
    """测试文本去重"""
    try:
        from utils.deduplication import deduplicate_texts
        
        test_texts = [
            "这是测试文本1",
            "这是测试文本1",  # 重复
            "这是测试文本2",
            "这是测试文本3"
        ]
        
        unique_texts = deduplicate_texts(test_texts, use_fuzzy=False)
        
        print(f"✓ 文本去重测试成功: {len(test_texts)} -> {len(unique_texts)}")
        return True
    except Exception as e:
        print(f"✗ 文本去重测试失败: {e}")
        return False

def test_vector_deduplication():
    """测试向量去重"""
    try:
        from utils.deduplication import deduplicate_vectors
        
        test_vectors = [
            {"content": "内容1", "embedding": [0.1, 0.2, 0.3]},
            {"content": "内容1", "embedding": [0.1, 0.2, 0.3]},  # 重复
            {"content": "内容2", "embedding": [0.4, 0.5, 0.6]}
        ]
        
        unique_vectors = deduplicate_vectors(test_vectors)
        
        print(f"✓ 向量去重测试成功: {len(test_vectors)} -> {len(unique_vectors)}")
        return True
    except Exception as e:
        print(f"✗ 向量去重测试失败: {e}")
        return False

def test_fast_dedup():
    """测试快速去重"""
    try:
        from utils.fast_dedup import fast_deduplicate_texts
        
        test_texts = ["文本1", "文本1", "文本2"] * 100
        
        unique_texts = fast_deduplicate_texts(test_texts)
        
        print(f"✓ 快速去重测试成功: {len(test_texts)} -> {len(unique_texts)}")
        return True
    except Exception as e:
        print(f"✗ 快速去重测试失败: {e}")
        return False

def test_dedup_manager():
    """测试去重管理器"""
    try:
        from utils.dedup_manager import DeduplicationManager
        
        manager = DeduplicationManager()
        stats = manager.get_deduplication_stats()
        
        print(f"✓ 去重管理器测试成功，缓存目录: {stats['cache_directory']}")
        return True
    except Exception as e:
        print(f"✗ 去重管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("金融对话系统 - 去重功能测试")
    print("=" * 40)
    
    tests = [
        test_basic_imports,
        test_text_deduplication,
        test_vector_deduplication,
        test_fast_dedup,
        test_dedup_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！去重功能已成功添加到utils中。")
    else:
        print("⚠️  部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
