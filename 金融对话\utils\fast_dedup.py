"""
高性能去重工具
专注于处理速度优化，使用多种加速技术
"""
import hashlib
import multiprocessing as mp
import threading
import os
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import List, Dict, Any, Set, Tuple, Optional
import time
import numpy as np
from loguru import logger

# 尝试导入高性能库
try:
    import xxhash
    XXHASH_AVAILABLE = True
except ImportError:
    XXHASH_AVAILABLE = False

try:
    from numba import jit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    logger.warning("numba未安装，数值计算加速不可用")


class FastHasher:
    """高性能哈希计算器"""
    
    @staticmethod
    def fast_hash(content: str) -> str:
        """最快的哈希计算"""
        if XXHASH_AVAILABLE:
            return xxhash.xxh64(content.encode('utf-8')).hexdigest()
        else:
            return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    @staticmethod
    def batch_hash(contents: List[str], num_workers: int = None) -> List[str]:
        """批量哈希计算"""
        if not num_workers:
            num_workers = min(mp.cpu_count(), len(contents) // 100 + 1)
        
        if num_workers == 1 or len(contents) < 100:
            return [FastHasher.fast_hash(content) for content in contents]
        
        # 多线程处理
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            return list(executor.map(FastHasher.fast_hash, contents))


class FastTextDeduplicator:
    """高性能文本去重器"""
    
    def __init__(self, use_parallel: bool = True, num_workers: int = None):
        self.use_parallel = use_parallel
        self.num_workers = num_workers or min(mp.cpu_count(), 8)
        self.hash_set = set()
        
    def _normalize_text_batch(self, texts: List[str]) -> List[str]:
        """批量标准化文本"""
        import re
        
        def normalize_single(text: str) -> str:
            if not text:
                return ""
            # 去除多余空白和标点
            text = re.sub(r'\s+', ' ', text.strip())
            text = re.sub(r'[，。！？；：""''（）【】\[\]().,!?;:"\'`]', '', text)
            return text.lower()
        
        if self.use_parallel and len(texts) > 100:
            with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
                return list(executor.map(normalize_single, texts))
        else:
            return [normalize_single(text) for text in texts]
    
    def deduplicate_fast(self, texts: List[str]) -> List[str]:
        """快速去重（仅精确匹配）"""
        if not texts:
            return []
        
        logger.info(f"开始快速去重 {len(texts)} 个文本")
        start_time = time.time()
        
        # 批量标准化
        normalized_texts = self._normalize_text_batch(texts)
        
        # 批量计算哈希
        hashes = FastHasher.batch_hash(normalized_texts, self.num_workers)
        
        # 去重
        unique_texts = []
        seen_hashes = set()
        
        for i, (text, text_hash) in enumerate(zip(texts, hashes)):
            if text_hash not in seen_hashes and text_hash not in self.hash_set:
                unique_texts.append(text)
                seen_hashes.add(text_hash)
                self.hash_set.add(text_hash)
        
        end_time = time.time()
        logger.info(f"快速去重完成: {len(texts)} -> {len(unique_texts)}，"
                   f"耗时 {end_time - start_time:.2f} 秒")
        
        return unique_texts
    
    def deduplicate_streaming(self, texts: List[str], batch_size: int = 1000) -> List[str]:
        """流式去重（适合大数据量）"""
        logger.info(f"开始流式去重 {len(texts)} 个文本，批大小: {batch_size}")
        
        unique_texts = []
        total_processed = 0
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_unique = self.deduplicate_fast(batch)
            unique_texts.extend(batch_unique)
            
            total_processed += len(batch)
            if total_processed % (batch_size * 10) == 0:
                logger.info(f"已处理 {total_processed}/{len(texts)} 个文本")
        
        logger.info(f"流式去重完成: {len(texts)} -> {len(unique_texts)}")
        return unique_texts


if NUMBA_AVAILABLE:
    @jit(nopython=True, parallel=True)
    def cosine_similarity_numba(a: np.ndarray, b: np.ndarray) -> float:
        """使用Numba加速的余弦相似度计算"""
        dot_product = np.dot(a, b)
        norm_a = np.sqrt(np.sum(a * a))
        norm_b = np.sqrt(np.sum(b * b))
        return dot_product / (norm_a * norm_b)
    
    @jit(nopython=True, parallel=True)
    def batch_cosine_similarity_numba(vectors: np.ndarray, query_vector: np.ndarray) -> np.ndarray:
        """批量余弦相似度计算"""
        similarities = np.zeros(vectors.shape[0])
        for i in prange(vectors.shape[0]):
            similarities[i] = cosine_similarity_numba(vectors[i], query_vector)
        return similarities


class FastVectorDeduplicator:
    """高性能向量去重器"""
    
    def __init__(self, similarity_threshold: float = 0.98, use_numba: bool = True):
        self.similarity_threshold = similarity_threshold
        self.use_numba = use_numba and NUMBA_AVAILABLE
        self.vector_hashes = set()
        self.stored_vectors = []
        self.stored_contents = []
        
    def _vector_hash(self, content: str, embedding: List[float]) -> str:
        """计算向量哈希"""
        # 使用内容哈希和向量的前几维
        content_hash = FastHasher.fast_hash(content)
        vector_str = ','.join([f"{x:.4f}" for x in embedding[:5]])
        return FastHasher.fast_hash(content_hash + vector_str)
    
    def _is_similar_vector(self, embedding: List[float]) -> Tuple[bool, float]:
        """检查向量是否与已存储的向量相似"""
        if not self.stored_vectors:
            return False, 0.0
        
        query_vector = np.array(embedding, dtype=np.float32)
        stored_vectors = np.array(self.stored_vectors, dtype=np.float32)
        
        if self.use_numba:
            similarities = batch_cosine_similarity_numba(stored_vectors, query_vector)
        else:
            # 使用numpy的向量化操作
            norms = np.linalg.norm(stored_vectors, axis=1) * np.linalg.norm(query_vector)
            similarities = np.dot(stored_vectors, query_vector) / norms
        
        max_similarity = float(np.max(similarities))
        is_similar = max_similarity >= self.similarity_threshold
        
        return is_similar, max_similarity
    
    def deduplicate_vectors_fast(self, vector_data: List[Dict[str, Any]], 
                               max_stored_vectors: int = 1000) -> List[Dict[str, Any]]:
        """快速向量去重"""
        if not vector_data:
            return []
        
        logger.info(f"开始快速向量去重 {len(vector_data)} 个向量")
        start_time = time.time()
        
        unique_vectors = []
        duplicate_count = 0
        
        for i, data in enumerate(vector_data):
            content = data.get('content', '')
            embedding = data.get('embedding', [])
            
            if not content or not embedding:
                continue
            
            # 首先检查哈希
            vector_hash = self._vector_hash(content, embedding)
            if vector_hash in self.vector_hashes:
                duplicate_count += 1
                continue
            
            # 检查向量相似度（仅对前max_stored_vectors个向量）
            if len(self.stored_vectors) < max_stored_vectors:
                is_similar, similarity = self._is_similar_vector(embedding)
                if is_similar:
                    duplicate_count += 1
                    logger.debug(f"发现相似向量 (相似度: {similarity:.3f}): {content[:30]}...")
                    continue
            
            # 添加到结果
            unique_vectors.append(data)
            self.vector_hashes.add(vector_hash)
            
            # 存储向量用于相似度比较（限制数量以节省内存）
            if len(self.stored_vectors) < max_stored_vectors:
                self.stored_vectors.append(embedding)
                self.stored_contents.append(content)
            
            # 显示进度
            if (i + 1) % 1000 == 0:
                logger.info(f"已处理 {i + 1}/{len(vector_data)} 个向量，发现 {duplicate_count} 个重复")
        
        end_time = time.time()
        logger.info(f"快速向量去重完成: {len(vector_data)} -> {len(unique_vectors)}，"
                   f"去除 {duplicate_count} 个重复，耗时 {end_time - start_time:.2f} 秒")
        
        return unique_vectors


class FastPDFDeduplicator:
    """高性能PDF去重器"""
    
    def __init__(self, use_parallel: bool = True):
        self.use_parallel = use_parallel
        self.file_hashes = {}
        
    def _calculate_file_hash_fast(self, file_path: str) -> Optional[str]:
        """快速计算文件哈希"""
        try:
            if XXHASH_AVAILABLE:
                hash_obj = xxhash.xxh64()
            else:
                hash_obj = hashlib.md5()
            
            with open(file_path, 'rb') as f:
                # 使用更大的块大小提高速度
                for chunk in iter(lambda: f.read(65536), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None
    
    def deduplicate_pdfs_fast(self, pdf_paths: List[str]) -> List[str]:
        """快速PDF去重"""
        if not pdf_paths:
            return []
        
        logger.info(f"开始快速PDF去重 {len(pdf_paths)} 个文件")
        start_time = time.time()
        
        # 过滤存在的文件
        existing_files = [path for path in pdf_paths if os.path.exists(path)]
        if len(existing_files) != len(pdf_paths):
            logger.warning(f"发现 {len(pdf_paths) - len(existing_files)} 个不存在的文件")
        
        if self.use_parallel and len(existing_files) > 4:
            # 并行计算哈希
            with ThreadPoolExecutor(max_workers=min(mp.cpu_count(), 8)) as executor:
                hash_results = list(executor.map(self._calculate_file_hash_fast, existing_files))
        else:
            hash_results = [self._calculate_file_hash_fast(path) for path in existing_files]
        
        # 去重
        unique_files = []
        seen_hashes = set()
        duplicate_count = 0
        
        for file_path, file_hash in zip(existing_files, hash_results):
            if file_hash is None:
                continue
            
            if file_hash in seen_hashes or file_hash in self.file_hashes.values():
                duplicate_count += 1
                continue
            
            unique_files.append(file_path)
            seen_hashes.add(file_hash)
            self.file_hashes[file_path] = file_hash
        
        end_time = time.time()
        logger.info(f"快速PDF去重完成: {len(pdf_paths)} -> {len(unique_files)}，"
                   f"去除 {duplicate_count} 个重复，耗时 {end_time - start_time:.2f} 秒")
        
        return unique_files


# 便捷函数
def fast_deduplicate_texts(texts: List[str], use_parallel: bool = True) -> List[str]:
    """快速文本去重"""
    deduplicator = FastTextDeduplicator(use_parallel=use_parallel)
    return deduplicator.deduplicate_fast(texts)


def fast_deduplicate_vectors(vector_data: List[Dict[str, Any]], 
                           similarity_threshold: float = 0.98) -> List[Dict[str, Any]]:
    """快速向量去重"""
    deduplicator = FastVectorDeduplicator(similarity_threshold=similarity_threshold)
    return deduplicator.deduplicate_vectors_fast(vector_data)


def fast_deduplicate_pdfs(pdf_paths: List[str]) -> List[str]:
    """快速PDF去重"""
    deduplicator = FastPDFDeduplicator()
    return deduplicator.deduplicate_pdfs_fast(pdf_paths)


# 示例使用
if __name__ == "__main__":
    import os
    
    # 测试快速文本去重
    test_texts = ["文本1", "文本1", "文本2"] * 1000  # 3000个文本
    
    start_time = time.time()
    unique_texts = fast_deduplicate_texts(test_texts)
    end_time = time.time()
    
    print(f"快速文本去重: {len(test_texts)} -> {len(unique_texts)}")
    print(f"耗时: {end_time - start_time:.2f} 秒")
    print(f"处理速度: {len(test_texts) / (end_time - start_time):.0f} 文本/秒")
