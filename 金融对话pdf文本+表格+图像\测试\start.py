"""
快速启动脚本
提供简化的启动方式
"""
import os
import sys
import subprocess
from pathlib import Path

# 切换到项目根目录
project_root = Path(__file__).parent.parent
os.chdir(project_root)

def check_requirements():
    """检查依赖是否安装"""
    try:
        import fastapi
        import pymilvus
        import sentence_transformers
        import openai
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r idconfig/requirements.txt")
        return False

def check_env_file():
    """检查环境配置文件"""
    env_file = Path(".env")
    if not env_file.exists():
        print("✗ 未找到.env配置文件")
        print("请复制.env.example为.env并配置相关参数")
        return False
    
    print("✓ 找到.env配置文件")
    return True

def start_system():
    """启动系统"""
    print("=" * 50)
    print("金融对话系统启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        return False
    
    # 检查配置文件
    if not check_env_file():
        return False
    
    print("\n选择启动模式:")
    print("1. 初始化系统（首次运行）")
    print("2. 启动服务器")
    print("3. 测试系统")
    print("4. 初始化并启动服务器")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        print("\n正在初始化系统...")
        subprocess.run([sys.executable, "main.py", "--mode", "init", "--add-sample"])
    
    elif choice == "2":
        print("\n正在启动服务器...")
        subprocess.run([sys.executable, "main.py", "--mode", "server"])
    
    elif choice == "3":
        print("\n正在测试系统...")
        subprocess.run([sys.executable, "main.py", "--mode", "test"])
    
    elif choice == "4":
        print("\n正在初始化系统...")
        result = subprocess.run([sys.executable, "main.py", "--mode", "init", "--add-sample"])
        if result.returncode == 0:
            print("\n初始化完成，正在启动服务器...")
            subprocess.run([sys.executable, "main.py", "--mode", "server"])
    
    else:
        print("无效选择")
        return False
    
    return True

if __name__ == "__main__":
    start_system()
