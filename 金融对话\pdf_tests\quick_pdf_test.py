"""
PDF功能快速测试脚本
快速验证PDF处理功能是否正常工作
"""
import sys
import os
import tempfile
from pathlib import Path
from loguru import logger

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

def create_test_pdf():
    """创建一个简单的测试PDF文件"""
    try:
        # 尝试使用reportlab创建PDF
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # 创建临时PDF文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()
        
        # 创建PDF内容
        c = canvas.Canvas(temp_file.name, pagesize=letter)
        c.drawString(100, 750, "Financial Knowledge Test Document")
        c.drawString(100, 720, "This is a test document for PDF processing.")
        c.drawString(100, 690, "")
        c.drawString(100, 660, "Financial Terms:")
        c.drawString(100, 630, "1. Stock (股票): A type of security that represents ownership in a corporation.")
        c.drawString(100, 600, "2. Bond (债券): A debt security issued by corporations or governments.")
        c.drawString(100, 570, "3. Fund (基金): A pool of money from many investors to purchase securities.")
        c.drawString(100, 540, "4. P/E Ratio (市盈率): Price-to-earnings ratio, a valuation metric.")
        c.drawString(100, 510, "5. ROE (净资产收益率): Return on equity, measures profitability.")
        c.drawString(100, 480, "")
        c.drawString(100, 450, "This document contains basic financial knowledge for testing purposes.")
        c.save()
        
        logger.info(f"✓ 测试PDF文件创建成功: {temp_file.name}")
        return temp_file.name
        
    except ImportError:
        logger.warning("⚠ reportlab未安装，使用文本文件模拟")
        # 创建一个文本文件作为替代
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt', mode='w', encoding='utf-8')
        temp_file.write("""Financial Knowledge Test Document

This is a test document for PDF processing.

Financial Terms:
1. Stock (股票): A type of security that represents ownership in a corporation.
2. Bond (债券): A debt security issued by corporations or governments.
3. Fund (基金): A pool of money from many investors to purchase securities.
4. P/E Ratio (市盈率): Price-to-earnings ratio, a valuation metric.
5. ROE (净资产收益率): Return on equity, measures profitability.

This document contains basic financial knowledge for testing purposes.
""")
        temp_file.close()
        logger.info(f"✓ 测试文本文件创建成功: {temp_file.name}")
        return temp_file.name
    
    except Exception as e:
        logger.error(f"✗ 创建测试文件失败: {e}")
        return None

def test_pdf_imports():
    """测试PDF相关模块导入"""
    logger.info("测试PDF模块导入...")
    
    try:
        from rag.pdf_processor import PDFProcessor
        from rag.pdf_manager import PDFManager
        logger.info("✓ PDF模块导入成功")
        return True
    except ImportError as e:
        logger.error(f"✗ PDF模块导入失败: {e}")
        return False

def test_pdf_processor():
    """测试PDF处理器"""
    logger.info("测试PDF处理器...")
    
    try:
        from rag.pdf_processor import PDFProcessor
        
        processor = PDFProcessor()
        logger.info("✓ PDF处理器创建成功")
        
        # 测试文本分割器
        from langchain.schema import Document
        test_doc = Document(page_content="这是一个测试文档。" * 100)
        split_docs = processor.split_documents([test_doc])
        logger.info(f"✓ 文本分割测试成功: 1个文档 -> {len(split_docs)}个块")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ PDF处理器测试失败: {e}")
        return False

def test_pdf_manager():
    """测试PDF管理器"""
    logger.info("测试PDF管理器...")
    
    try:
        from rag.pdf_manager import PDFManager
        
        manager = PDFManager()
        logger.info("✓ PDF管理器创建成功")
        
        # 测试统计功能
        stats = manager.get_statistics()
        logger.info(f"✓ 统计信息获取成功: {stats}")
        
        # 测试列表功能
        pdf_list = manager.list_pdfs()
        logger.info(f"✓ PDF列表获取成功: 找到 {len(pdf_list)} 个文件")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ PDF管理器测试失败: {e}")
        return False

def test_full_workflow():
    """测试完整的PDF处理工作流"""
    logger.info("测试完整PDF处理工作流...")
    
    # 创建测试文件
    test_file = create_test_pdf()
    if not test_file:
        logger.error("✗ 无法创建测试文件")
        return False
    
    try:
        from rag.pdf_manager import PDFManager
        
        # 初始化管理器
        manager = PDFManager()
        
        # 尝试初始化（可能因为Milvus连接失败）
        try:
            if manager.initialize():
                logger.info("✓ PDF管理器初始化成功")
                
                # 如果是PDF文件，尝试处理
                if test_file.endswith('.pdf'):
                    result = manager.upload_and_process_pdf(
                        test_file,
                        category="测试文档",
                        description="PDF功能测试文档"
                    )
                    
                    if result["success"]:
                        logger.info("✓ PDF处理工作流测试成功")
                        
                        # 测试搜索
                        search_results = manager.search_pdfs("股票", top_k=3)
                        logger.info(f"✓ 搜索测试成功: 找到 {len(search_results)} 条结果")
                        
                        return True
                    else:
                        logger.warning(f"⚠ PDF处理失败: {result['message']}")
                        return False
                else:
                    logger.info("✓ 文本文件创建成功（PDF功能需要真实PDF文件）")
                    return True
            else:
                logger.warning("⚠ PDF管理器初始化失败（可能是Milvus连接问题）")
                return False
                
        except Exception as e:
            logger.warning(f"⚠ 工作流测试异常: {e}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 工作流测试失败: {e}")
        return False
        
    finally:
        # 清理测试文件
        if test_file and os.path.exists(test_file):
            os.unlink(test_file)
            logger.info("✓ 测试文件已清理")

def main():
    """主函数"""
    logger.info("开始PDF功能快速测试")
    logger.info("=" * 50)
    
    tests = [
        ("PDF模块导入", test_pdf_imports),
        ("PDF处理器", test_pdf_processor),
        ("PDF管理器", test_pdf_manager),
        ("完整工作流", test_full_workflow),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    logger.info("\n" + "=" * 50)
    logger.info("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！PDF功能可以正常使用。")
        logger.info("\n下一步:")
        logger.info("1. 启动Milvus数据库")
        logger.info("2. 运行 python examples/pdf_demo.py 查看完整演示")
        logger.info("3. 启动API服务器测试Web接口")
    elif passed > 0:
        logger.warning("⚠ 部分测试通过，基础功能可用。")
        logger.info("请检查Milvus连接和依赖安装。")
    else:
        logger.error("❌ 所有测试失败，请检查依赖安装。")
        logger.info("运行: python scripts/install_pdf_dependencies.py")

if __name__ == "__main__":
    main()
