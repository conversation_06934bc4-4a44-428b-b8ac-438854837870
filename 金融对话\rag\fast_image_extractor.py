"""
快速图像提取器
优化PDF图像提取和存储性能
"""
import os
import sys
import json
import time
import hashlib
import io
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from loguru import logger
import numpy as np

# 添加父目录到Python路径
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

try:
    import fitz
    from PIL import Image
    import cv2
    FAST_EXTRACTION_AVAILABLE = True
except ImportError:
    FAST_EXTRACTION_AVAILABLE = False
    logger.warning("快速提取功能需要安装: PyMuPDF, Pillow, opencv-python")

class FastImageExtractor:
    """快速图像提取器"""
    
    def __init__(self):
        self.extraction_enabled = FAST_EXTRACTION_AVAILABLE
        
        # 性能优化配置
        self.batch_size = 10  # 批处理大小
        self.max_workers = min(4, mp.cpu_count())  # 最大工作线程数
        self.image_cache = {}  # 图像缓存
        self.skip_small_images = True  # 跳过小图像
        self.min_image_size = (50, 50)  # 最小图像尺寸
        self.max_image_size = (2000, 2000)  # 最大图像尺寸
        
        # 存储配置
        self.image_storage_dir = Path("data/images")
        self.image_storage_dir.mkdir(exist_ok=True)
        self.index_file = Path("data/fast_image_index.json")
        
        # 质量过滤配置（简化版）
        self.enable_quality_filter = True
        self.min_quality_threshold = 0.2  # 降低阈值以提高速度
        
        logger.info(f"快速图像提取器初始化: 工作线程={self.max_workers}, 批处理={self.batch_size}")
    
    def extract_images_fast(self, pdf_path: str, file_hash: str = None) -> List[Dict[str, Any]]:
        """快速提取PDF图像"""
        if not self.extraction_enabled:
            logger.warning("快速提取功能不可用")
            return []
        
        start_time = time.time()
        
        if file_hash is None:
            file_hash = self._calculate_file_hash(pdf_path)
        
        try:
            # 检查缓存
            if file_hash in self.image_cache:
                logger.info(f"使用缓存的图像数据: {file_hash}")
                return self.image_cache[file_hash]
            
            # 快速扫描PDF获取图像信息
            image_refs = self._fast_scan_pdf_images(pdf_path)
            logger.info(f"扫描到 {len(image_refs)} 个图像引用")
            
            if not image_refs:
                return []
            
            # 批量并行处理图像
            extracted_images = self._batch_process_images(pdf_path, image_refs, file_hash)
            
            # 缓存结果
            self.image_cache[file_hash] = extracted_images
            
            # 保存索引
            self._save_fast_index(file_hash, pdf_path, extracted_images)
            
            elapsed_time = time.time() - start_time
            logger.info(f"快速提取完成: {len(extracted_images)} 个图像, 耗时 {elapsed_time:.2f}秒")
            
            return extracted_images
            
        except Exception as e:
            logger.error(f"快速图像提取失败: {e}")
            return []
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """快速计算文件哈希（只读取文件头部）"""
        try:
            with open(file_path, 'rb') as f:
                # 只读取前1MB用于哈希计算，提高速度
                chunk = f.read(1024 * 1024)
                return hashlib.md5(chunk).hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {e}")
            return hashlib.md5(file_path.encode()).hexdigest()
    
    def _fast_scan_pdf_images(self, pdf_path: str) -> List[Tuple[int, int, Dict]]:
        """快速扫描PDF中的图像引用"""
        image_refs = []
        
        try:
            doc = fitz.open(pdf_path)
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    # 快速预检查图像尺寸
                    xref = img[0]
                    try:
                        # 获取图像基本信息而不加载像素数据
                        img_dict = doc.extract_image(xref)
                        width = img_dict.get("width", 0)
                        height = img_dict.get("height", 0)
                        
                        # 快速尺寸过滤
                        if (width >= self.min_image_size[0] and 
                            height >= self.min_image_size[1] and
                            width <= self.max_image_size[0] and 
                            height <= self.max_image_size[1]):
                            
                            image_refs.append((page_num, img_index, {
                                'xref': xref,
                                'width': width,
                                'height': height,
                                'ext': img_dict.get("ext", "png"),
                                'colorspace': img_dict.get("colorspace", 3)
                            }))
                    except:
                        # 如果快速检查失败，仍然添加到处理列表
                        image_refs.append((page_num, img_index, {'xref': xref}))
            
            doc.close()
            return image_refs
            
        except Exception as e:
            logger.error(f"PDF扫描失败: {e}")
            return []
    
    def _batch_process_images(self, pdf_path: str, image_refs: List[Tuple], file_hash: str) -> List[Dict[str, Any]]:
        """批量并行处理图像"""
        extracted_images = []
        
        # 分批处理
        for i in range(0, len(image_refs), self.batch_size):
            batch = image_refs[i:i + self.batch_size]
            
            # 使用线程池并行处理批次
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                
                for page_num, img_index, img_info in batch:
                    future = executor.submit(
                        self._process_single_image,
                        pdf_path, page_num, img_index, img_info, file_hash
                    )
                    futures.append(future)
                
                # 收集结果
                for future in futures:
                    try:
                        result = future.result(timeout=30)  # 30秒超时
                        if result:
                            extracted_images.append(result)
                    except Exception as e:
                        logger.warning(f"图像处理超时或失败: {e}")
        
        return extracted_images
    
    def _process_single_image(self, pdf_path: str, page_num: int, img_index: int, 
                            img_info: Dict, file_hash: str) -> Optional[Dict[str, Any]]:
        """处理单个图像（优化版）"""
        try:
            doc = fitz.open(pdf_path)
            xref = img_info['xref']
            
            # 快速提取图像数据
            img_dict = doc.extract_image(xref)
            img_data = img_dict["image"]
            img_ext = img_dict["ext"]
            
            # 直接从字节数据创建PIL图像
            pil_image = Image.open(io.BytesIO(img_data))
            
            # 快速质量检查（简化版）
            if self.enable_quality_filter:
                if not self._quick_quality_check(pil_image):
                    doc.close()
                    return None
            
            # 生成图像ID和路径
            image_id = f"{file_hash}_p{page_num+1}_i{img_index+1}"
            image_filename = f"{image_id}.{img_ext}"
            image_path = self.image_storage_dir / image_filename
            
            # 快速保存（不进行额外处理）
            if img_ext.lower() in ['jpg', 'jpeg']:
                pil_image.save(image_path, "JPEG", quality=85, optimize=True)
            else:
                pil_image.save(image_path, "PNG", optimize=True)
            
            # 构建图像信息（简化版）
            image_info = {
                "image_id": image_id,
                "file_hash": file_hash,
                "page_number": page_num + 1,
                "image_index": img_index + 1,
                "image_path": str(image_path),
                "width": pil_image.width,
                "height": pil_image.height,
                "format": img_ext,
                "file_size": len(img_data),
                "extraction_time": time.time()
            }
            
            doc.close()
            return image_info
            
        except Exception as e:
            logger.warning(f"处理图像失败 (页{page_num+1}, 图{img_index+1}): {e}")
            return None
    
    def _quick_quality_check(self, image: Image.Image) -> bool:
        """快速质量检查（简化版）"""
        try:
            # 只检查基本尺寸和格式
            width, height = image.size
            
            # 尺寸检查
            if width < self.min_image_size[0] or height < self.min_image_size[1]:
                return False
            
            # 简单的像素密度检查
            if width * height < 2500:  # 50x50像素
                return False
            
            # 检查图像是否为纯色（可能是装饰性图像）
            if image.mode in ['RGB', 'RGBA']:
                # 转换为numpy数组进行快速检查
                img_array = np.array(image)
                if len(img_array.shape) >= 3:
                    # 检查颜色变化
                    std_dev = np.std(img_array)
                    if std_dev < 10:  # 颜色变化太小
                        return False
            
            return True
            
        except Exception:
            return True  # 检查失败时默认通过
    
    def _save_fast_index(self, file_hash: str, pdf_path: str, images: List[Dict[str, Any]]):
        """保存快速索引"""
        try:
            # 加载现有索引
            if self.index_file.exists():
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
            else:
                index_data = {}
            
            # 更新索引
            index_data[file_hash] = {
                "pdf_path": pdf_path,
                "pdf_name": os.path.basename(pdf_path),
                "extraction_time": time.time(),
                "image_count": len(images),
                "images": images
            }
            
            # 保存索引
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存快速索引失败: {e}")
    
    def get_extraction_stats(self) -> Dict[str, Any]:
        """获取提取统计信息"""
        try:
            if not self.index_file.exists():
                return {"total_pdfs": 0, "total_images": 0}
            
            with open(self.index_file, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
            
            total_pdfs = len(index_data)
            total_images = sum(len(pdf_info.get("images", [])) for pdf_info in index_data.values())
            
            return {
                "total_pdfs": total_pdfs,
                "total_images": total_images,
                "cache_size": len(self.image_cache),
                "storage_dir": str(self.image_storage_dir),
                "index_file": str(self.index_file)
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {"error": str(e)}
    
    def clear_cache(self):
        """清空缓存"""
        self.image_cache.clear()
        logger.info("图像缓存已清空")
    
    def optimize_storage(self):
        """优化存储空间"""
        try:
            # 清理重复图像
            image_files = list(self.image_storage_dir.glob("*"))
            file_sizes = {}
            duplicates = []
            
            for img_file in image_files:
                size = img_file.stat().st_size
                if size in file_sizes:
                    duplicates.append(img_file)
                else:
                    file_sizes[size] = img_file
            
            # 删除重复文件
            for dup_file in duplicates:
                dup_file.unlink()
            
            logger.info(f"存储优化完成，删除了 {len(duplicates)} 个重复文件")
            
        except Exception as e:
            logger.error(f"存储优化失败: {e}")


