"""
高效去重处理模块
提供多种去重策略，优化处理速度和内存使用
"""
import hashlib
import json
import os
import pickle
import time
from collections import defaultdict
from typing import List, Dict, Any, Set, Tuple, Optional, Union
from pathlib import Path
import numpy as np
from loguru import logger

# 尝试导入高性能库
try:
    import xxhash
    XXHASH_AVAILABLE = True
except ImportError:
    XXHASH_AVAILABLE = False
    logger.warning("xxhash未安装，将使用标准hashlib，性能可能较慢")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("sklearn未安装，高级文本相似度功能不可用")


class ContentDeduplicator:
    """通用内容去重器基类"""
    
    def __init__(self, cache_dir: str = "data/dedup_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.hash_cache_file = self.cache_dir / "hash_cache.pkl"
        self.hash_cache = self._load_hash_cache()
        
    def _load_hash_cache(self) -> Dict[str, str]:
        """加载哈希缓存"""
        try:
            if self.hash_cache_file.exists():
                with open(self.hash_cache_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.warning(f"加载哈希缓存失败: {e}")
        return {}
    
    def _save_hash_cache(self):
        """保存哈希缓存"""
        try:
            with open(self.hash_cache_file, 'wb') as f:
                pickle.dump(self.hash_cache, f)
        except Exception as e:
            logger.error(f"保存哈希缓存失败: {e}")
    
    def _fast_hash(self, content: str) -> str:
        """快速哈希计算"""
        if XXHASH_AVAILABLE:
            return xxhash.xxh64(content.encode('utf-8')).hexdigest()
        else:
            return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _secure_hash(self, content: str) -> str:
        """安全哈希计算（用于重要数据）"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()


class TextDeduplicator(ContentDeduplicator):
    """文本去重器"""
    
    def __init__(self, cache_dir: str = "data/dedup_cache", similarity_threshold: float = 0.95):
        super().__init__(cache_dir)
        self.similarity_threshold = similarity_threshold
        self.text_hashes = set()
        self.text_cache_file = self.cache_dir / "text_hashes.pkl"
        self._load_text_hashes()
        
        # TF-IDF向量化器（用于语义相似度检测）
        if SKLEARN_AVAILABLE:
            self.vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words=None,  # 中文不使用英文停用词
                ngram_range=(1, 2),
                min_df=1
            )
            self.text_vectors = []
            self.text_contents = []
        
    def _load_text_hashes(self):
        """加载文本哈希集合"""
        try:
            if self.text_cache_file.exists():
                with open(self.text_cache_file, 'rb') as f:
                    self.text_hashes = pickle.load(f)
        except Exception as e:
            logger.warning(f"加载文本哈希失败: {e}")
            self.text_hashes = set()
    
    def _save_text_hashes(self):
        """保存文本哈希集合"""
        try:
            with open(self.text_cache_file, 'wb') as f:
                pickle.dump(self.text_hashes, f)
        except Exception as e:
            logger.error(f"保存文本哈希失败: {e}")
    
    def _normalize_text(self, text: str) -> str:
        """标准化文本（去除空白字符、标点等）"""
        import re
        # 去除多余空白
        text = re.sub(r'\s+', ' ', text.strip())
        # 去除常见标点符号
        text = re.sub(r'[，。！？；：""''（）【】\[\]().,!?;:"\'`]', '', text)
        return text.lower()
    
    def is_duplicate_exact(self, text: str) -> bool:
        """精确重复检测"""
        text_hash = self._fast_hash(self._normalize_text(text))
        return text_hash in self.text_hashes
    
    def is_duplicate_fuzzy(self, text: str, check_similarity: bool = True) -> Tuple[bool, float]:
        """模糊重复检测"""
        if not SKLEARN_AVAILABLE or not check_similarity:
            return self.is_duplicate_exact(text), 1.0 if self.is_duplicate_exact(text) else 0.0
        
        normalized_text = self._normalize_text(text)
        
        # 首先检查精确重复
        if self.is_duplicate_exact(text):
            return True, 1.0
        
        # 如果没有历史文本，不是重复
        if not self.text_contents:
            return False, 0.0
        
        try:
            # 计算TF-IDF向量
            all_texts = self.text_contents + [normalized_text]
            tfidf_matrix = self.vectorizer.fit_transform(all_texts)
            
            # 计算新文本与所有历史文本的相似度
            new_vector = tfidf_matrix[-1]
            similarities = cosine_similarity(new_vector, tfidf_matrix[:-1]).flatten()
            
            max_similarity = float(np.max(similarities)) if len(similarities) > 0 else 0.0
            is_duplicate = max_similarity >= self.similarity_threshold
            
            return is_duplicate, max_similarity
            
        except Exception as e:
            logger.warning(f"模糊重复检测失败: {e}")
            return self.is_duplicate_exact(text), 1.0 if self.is_duplicate_exact(text) else 0.0
    
    def add_text(self, text: str):
        """添加文本到去重集合"""
        normalized_text = self._normalize_text(text)
        text_hash = self._fast_hash(normalized_text)
        self.text_hashes.add(text_hash)
        
        if SKLEARN_AVAILABLE:
            self.text_contents.append(normalized_text)
            # 限制内存使用，只保留最近的1000个文本
            if len(self.text_contents) > 1000:
                self.text_contents = self.text_contents[-1000:]
    
    def deduplicate_texts(self, texts: List[str], use_fuzzy: bool = False) -> List[str]:
        """批量去重文本"""
        unique_texts = []
        processed_count = 0
        duplicate_count = 0
        
        logger.info(f"开始去重处理 {len(texts)} 个文本")
        start_time = time.time()
        
        for text in texts:
            if not text or not text.strip():
                continue
                
            processed_count += 1
            
            if use_fuzzy:
                is_dup, similarity = self.is_duplicate_fuzzy(text)
                if is_dup:
                    duplicate_count += 1
                    logger.debug(f"发现重复文本 (相似度: {similarity:.3f}): {text[:50]}...")
                    continue
            else:
                if self.is_duplicate_exact(text):
                    duplicate_count += 1
                    logger.debug(f"发现重复文本: {text[:50]}...")
                    continue
            
            unique_texts.append(text)
            self.add_text(text)
            
            # 每处理100个文本显示进度
            if processed_count % 100 == 0:
                logger.info(f"已处理 {processed_count}/{len(texts)} 个文本，发现 {duplicate_count} 个重复")
        
        end_time = time.time()
        logger.info(f"去重完成: 处理 {processed_count} 个文本，去除 {duplicate_count} 个重复，"
                   f"保留 {len(unique_texts)} 个唯一文本，耗时 {end_time - start_time:.2f} 秒")
        
        # 保存缓存
        self._save_text_hashes()
        
        return unique_texts
    
    def clear_cache(self):
        """清空缓存"""
        self.text_hashes.clear()
        if SKLEARN_AVAILABLE:
            self.text_contents.clear()
        self._save_text_hashes()
        logger.info("文本去重缓存已清空")


class VectorDeduplicator(ContentDeduplicator):
    """向量去重器（用于向量数据库去重）"""
    
    def __init__(self, cache_dir: str = "data/dedup_cache", 
                 vector_similarity_threshold: float = 0.98):
        super().__init__(cache_dir)
        self.vector_similarity_threshold = vector_similarity_threshold
        self.vector_hashes = set()
        self.vector_cache_file = self.cache_dir / "vector_hashes.pkl"
        self._load_vector_hashes()
    
    def _load_vector_hashes(self):
        """加载向量哈希集合"""
        try:
            if self.vector_cache_file.exists():
                with open(self.vector_cache_file, 'rb') as f:
                    self.vector_hashes = pickle.load(f)
        except Exception as e:
            logger.warning(f"加载向量哈希失败: {e}")
            self.vector_hashes = set()
    
    def _save_vector_hashes(self):
        """保存向量哈希集合"""
        try:
            with open(self.vector_cache_file, 'wb') as f:
                pickle.dump(self.vector_hashes, f)
        except Exception as e:
            logger.error(f"保存向量哈希失败: {e}")
    
    def _vector_hash(self, content: str, embedding: List[float]) -> str:
        """计算内容和向量的组合哈希"""
        # 组合内容哈希和向量哈希
        content_hash = self._fast_hash(content)
        vector_str = ','.join([f"{x:.6f}" for x in embedding[:10]])  # 只使用前10维避免过长
        vector_hash = self._fast_hash(vector_str)
        return self._fast_hash(content_hash + vector_hash)
    
    def is_duplicate_vector(self, content: str, embedding: List[float]) -> bool:
        """检查向量数据是否重复"""
        vector_hash = self._vector_hash(content, embedding)
        return vector_hash in self.vector_hashes
    
    def add_vector(self, content: str, embedding: List[float]):
        """添加向量数据到去重集合"""
        vector_hash = self._vector_hash(content, embedding)
        self.vector_hashes.add(vector_hash)
    
    def deduplicate_vectors(self, vector_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量去重向量数据"""
        unique_vectors = []
        duplicate_count = 0
        
        logger.info(f"开始去重处理 {len(vector_data)} 个向量数据")
        start_time = time.time()
        
        for i, data in enumerate(vector_data):
            content = data.get('content', '')
            embedding = data.get('embedding', [])
            
            if not content or not embedding:
                continue
            
            if self.is_duplicate_vector(content, embedding):
                duplicate_count += 1
                logger.debug(f"发现重复向量数据: {content[:50]}...")
                continue
            
            unique_vectors.append(data)
            self.add_vector(content, embedding)
            
            # 每处理100个向量显示进度
            if (i + 1) % 100 == 0:
                logger.info(f"已处理 {i + 1}/{len(vector_data)} 个向量，发现 {duplicate_count} 个重复")
        
        end_time = time.time()
        logger.info(f"向量去重完成: 处理 {len(vector_data)} 个向量，去除 {duplicate_count} 个重复，"
                   f"保留 {len(unique_vectors)} 个唯一向量，耗时 {end_time - start_time:.2f} 秒")
        
        # 保存缓存
        self._save_vector_hashes()
        
        return unique_vectors
    
    def clear_cache(self):
        """清空缓存"""
        self.vector_hashes.clear()
        self._save_vector_hashes()
        logger.info("向量去重缓存已清空")


class PDFDeduplicator(ContentDeduplicator):
    """PDF文件去重器"""
    
    def __init__(self, cache_dir: str = "data/dedup_cache"):
        super().__init__(cache_dir)
        self.pdf_hashes = {}  # 文件路径 -> 哈希值
        self.pdf_cache_file = self.cache_dir / "pdf_hashes.pkl"
        self._load_pdf_hashes()
    
    def _load_pdf_hashes(self):
        """加载PDF哈希映射"""
        try:
            if self.pdf_cache_file.exists():
                with open(self.pdf_cache_file, 'rb') as f:
                    self.pdf_hashes = pickle.load(f)
        except Exception as e:
            logger.warning(f"加载PDF哈希失败: {e}")
            self.pdf_hashes = {}
    
    def _save_pdf_hashes(self):
        """保存PDF哈希映射"""
        try:
            with open(self.pdf_cache_file, 'wb') as f:
                pickle.dump(self.pdf_hashes, f)
        except Exception as e:
            logger.error(f"保存PDF哈希失败: {e}")

    def _calculate_file_hash(self, file_path: str) -> Optional[str]:
        """计算文件哈希值"""
        try:
            if not os.path.exists(file_path):
                return None

            hash_obj = hashlib.sha256()
            with open(file_path, 'rb') as f:
                # 分块读取文件以节省内存
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None

    def is_duplicate_pdf(self, file_path: str) -> Tuple[bool, Optional[str]]:
        """检查PDF文件是否重复"""
        file_path = os.path.abspath(file_path)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            return False, None

        # 计算文件哈希
        file_hash = self._calculate_file_hash(file_path)
        if not file_hash:
            return False, None

        # 检查是否已存在相同哈希的文件
        for existing_path, existing_hash in self.pdf_hashes.items():
            if existing_hash == file_hash and existing_path != file_path:
                logger.info(f"发现重复PDF文件: {file_path} 与 {existing_path} 相同")
                return True, existing_path

        return False, None

    def add_pdf(self, file_path: str) -> bool:
        """添加PDF文件到去重集合"""
        file_path = os.path.abspath(file_path)

        if not os.path.exists(file_path):
            logger.error(f"PDF文件不存在: {file_path}")
            return False

        file_hash = self._calculate_file_hash(file_path)
        if not file_hash:
            return False

        self.pdf_hashes[file_path] = file_hash
        self._save_pdf_hashes()
        return True

    def deduplicate_pdf_list(self, pdf_paths: List[str]) -> List[str]:
        """批量去重PDF文件列表"""
        unique_pdfs = []
        duplicate_count = 0

        logger.info(f"开始去重处理 {len(pdf_paths)} 个PDF文件")
        start_time = time.time()

        for pdf_path in pdf_paths:
            is_dup, existing_path = self.is_duplicate_pdf(pdf_path)
            if is_dup:
                duplicate_count += 1
                logger.info(f"跳过重复PDF: {pdf_path} (与 {existing_path} 相同)")
                continue

            unique_pdfs.append(pdf_path)
            self.add_pdf(pdf_path)

        end_time = time.time()
        logger.info(f"PDF去重完成: 处理 {len(pdf_paths)} 个文件，去除 {duplicate_count} 个重复，"
                   f"保留 {len(unique_pdfs)} 个唯一文件，耗时 {end_time - start_time:.2f} 秒")

        return unique_pdfs

    def clear_cache(self):
        """清空缓存"""
        self.pdf_hashes.clear()
        self._save_pdf_hashes()
        logger.info("PDF去重缓存已清空")


class HistoryDeduplicator(ContentDeduplicator):
    """历史对话去重器"""

    def __init__(self, cache_dir: str = "data/dedup_cache",
                 time_window_hours: int = 24):
        super().__init__(cache_dir)
        self.time_window_hours = time_window_hours
        self.history_hashes = {}  # 哈希 -> 时间戳
        self.history_cache_file = self.cache_dir / "history_hashes.pkl"
        self._load_history_hashes()

    def _load_history_hashes(self):
        """加载历史对话哈希映射"""
        try:
            if self.history_cache_file.exists():
                with open(self.history_cache_file, 'rb') as f:
                    self.history_hashes = pickle.load(f)
                # 清理过期的哈希
                self._cleanup_expired_hashes()
        except Exception as e:
            logger.warning(f"加载历史对话哈希失败: {e}")
            self.history_hashes = {}

    def _save_history_hashes(self):
        """保存历史对话哈希映射"""
        try:
            with open(self.history_cache_file, 'wb') as f:
                pickle.dump(self.history_hashes, f)
        except Exception as e:
            logger.error(f"保存历史对话哈希失败: {e}")

    def _cleanup_expired_hashes(self):
        """清理过期的哈希记录"""
        current_time = time.time()
        expired_hashes = []

        for hash_val, timestamp in self.history_hashes.items():
            if current_time - timestamp > self.time_window_hours * 3600:
                expired_hashes.append(hash_val)

        for hash_val in expired_hashes:
            del self.history_hashes[hash_val]

        if expired_hashes:
            logger.info(f"清理了 {len(expired_hashes)} 个过期的历史对话哈希")

    def _create_conversation_hash(self, user_query: str, assistant_response: str) -> str:
        """创建对话哈希"""
        # 标准化文本
        normalized_query = user_query.strip().lower()
        normalized_response = assistant_response.strip().lower()

        # 组合查询和回答
        conversation = f"{normalized_query}|{normalized_response}"
        return self._fast_hash(conversation)

    def is_duplicate_conversation(self, user_query: str, assistant_response: str) -> bool:
        """检查对话是否重复"""
        # 清理过期哈希
        self._cleanup_expired_hashes()

        conv_hash = self._create_conversation_hash(user_query, assistant_response)
        return conv_hash in self.history_hashes

    def add_conversation(self, user_query: str, assistant_response: str):
        """添加对话到去重集合"""
        conv_hash = self._create_conversation_hash(user_query, assistant_response)
        self.history_hashes[conv_hash] = time.time()
        self._save_history_hashes()

    def deduplicate_conversations(self, conversations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量去重对话数据"""
        unique_conversations = []
        duplicate_count = 0

        logger.info(f"开始去重处理 {len(conversations)} 个对话")
        start_time = time.time()

        for conv in conversations:
            user_query = conv.get('user_query', '')
            assistant_response = conv.get('assistant_response', '')

            if not user_query or not assistant_response:
                continue

            if self.is_duplicate_conversation(user_query, assistant_response):
                duplicate_count += 1
                logger.debug(f"发现重复对话: {user_query[:30]}...")
                continue

            unique_conversations.append(conv)
            self.add_conversation(user_query, assistant_response)

        end_time = time.time()
        logger.info(f"对话去重完成: 处理 {len(conversations)} 个对话，去除 {duplicate_count} 个重复，"
                   f"保留 {len(unique_conversations)} 个唯一对话，耗时 {end_time - start_time:.2f} 秒")

        return unique_conversations

    def clear_cache(self):
        """清空缓存"""
        self.history_hashes.clear()
        self._save_history_hashes()
        logger.info("历史对话去重缓存已清空")


# 便捷函数
def deduplicate_texts(texts: List[str], use_fuzzy: bool = False,
                     similarity_threshold: float = 0.95) -> List[str]:
    """便捷函数：去重文本列表"""
    deduplicator = TextDeduplicator(similarity_threshold=similarity_threshold)
    return deduplicator.deduplicate_texts(texts, use_fuzzy=use_fuzzy)


def deduplicate_vectors(vector_data: List[Dict[str, Any]],
                       similarity_threshold: float = 0.98) -> List[Dict[str, Any]]:
    """便捷函数：去重向量数据"""
    deduplicator = VectorDeduplicator(vector_similarity_threshold=similarity_threshold)
    return deduplicator.deduplicate_vectors(vector_data)


def deduplicate_pdfs(pdf_paths: List[str]) -> List[str]:
    """便捷函数：去重PDF文件列表"""
    deduplicator = PDFDeduplicator()
    return deduplicator.deduplicate_pdf_list(pdf_paths)


def deduplicate_conversations(conversations: List[Dict[str, Any]],
                            time_window_hours: int = 24) -> List[Dict[str, Any]]:
    """便捷函数：去重对话数据"""
    deduplicator = HistoryDeduplicator(time_window_hours=time_window_hours)
    return deduplicator.deduplicate_conversations(conversations)


# 示例使用
if __name__ == "__main__":
    # 测试文本去重
    test_texts = [
        "这是一个测试文本",
        "这是一个测试文本",  # 重复
        "这是另一个测试文本",
        "这是一个测试文本。",  # 相似但不完全相同
        "完全不同的文本内容"
    ]

    print("原始文本数量:", len(test_texts))
    unique_texts = deduplicate_texts(test_texts, use_fuzzy=True)
    print("去重后文本数量:", len(unique_texts))
    print("去重后文本:", unique_texts)
