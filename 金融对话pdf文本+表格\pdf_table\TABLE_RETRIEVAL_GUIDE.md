# PDF表格检索优化技术指南

## 概述

本指南详细介绍了金融对话系统中的PDF表格检索优化技术，该技术专门针对金融文档中的表格数据进行智能提取、索引和检索。

## 🎯 核心功能

### 1. 智能表格提取
- **多引擎支持**: 使用pdfplumber和PyMuPDF双引擎提取
- **表格识别**: 自动识别PDF中的表格结构
- **数据清洗**: 自动清理和格式化表格数据
- **类型推断**: 智能推断列的数据类型（数值、日期、文本）

### 2. 表格分类与索引
- **自动分类**: 根据内容自动分类表格类型
  - 财务报表
  - 市场数据
  - 业绩指标
  - 风险数据
- **特征提取**: 提取表格的关键特征
- **索引构建**: 建立高效的搜索索引

### 3. 优化检索算法
- **语义搜索**: 基于内容语义的相似度匹配
- **多维度匹配**: 结合列名、数据内容、表格类型
- **上下文优化**: 根据历史查询和知识库优化结果
- **相关性排序**: 智能排序搜索结果

## 🚀 快速开始

### 1. 安装依赖

```bash
# 自动安装所有依赖
python install_table_dependencies.py

# 或手动安装
pip install pandas pdfplumber pymupdf scikit-learn openpyxl tabulate
```

### 2. 基础使用

```python
from rag.table_retrieval_optimizer import TableRetrievalOptimizer

# 初始化优化器
optimizer = TableRetrievalOptimizer()
optimizer.initialize()

# 提取并索引表格
success = optimizer.extract_and_index_tables("document.pdf", "file_hash")

# 搜索表格
results = optimizer.search_tables("营收数据", top_k=5)
```

### 3. API使用

```bash
# 启动服务器
python main.py --mode server

# 搜索表格
curl "http://localhost:8000/pdf/tables/search?query=财务指标&top_k=5"

# 提取表格
curl -X POST "http://localhost:8000/pdf/tables/extract/file_hash"

# 获取统计信息
curl "http://localhost:8000/pdf/tables/statistics"
```

## 📊 技术架构

### 1. 表格提取层
```
PDF文档 → pdfplumber/PyMuPDF → 表格数据 → 数据清洗 → 结构化数据
```

### 2. 特征分析层
```
结构化数据 → 特征提取 → 类型识别 → 关键词提取 → 索引构建
```

### 3. 检索优化层
```
用户查询 → 查询分析 → 多维匹配 → 相关性计算 → 结果排序
```

## 🔧 配置说明

### 表格提取配置

```python
# 在pdf_processor.py中配置
self.table_chunk_size = 2000  # 表格内容块大小
self.enable_table_extraction = True  # 启用表格提取
```

### 搜索优化配置

```python
# 在table_retrieval_optimizer.py中配置
self.tfidf_vectorizer = TfidfVectorizer(
    max_features=1000,
    ngram_range=(1, 2)
)
```

### 金融关键词配置

```python
self.financial_keywords = {
    "财务指标": ["营收", "利润", "资产", "负债"],
    "市场数据": ["股价", "市值", "PE", "PB"],
    "业务数据": ["销售额", "订单", "客户"],
    "风险指标": ["风险", "违约", "信用"]
}
```

## 📈 性能优化

### 1. 提取性能优化
- **并行处理**: 多页面并行提取表格
- **缓存机制**: 缓存已提取的表格数据
- **增量更新**: 只处理新增或修改的文档

### 2. 搜索性能优化
- **索引优化**: 使用倒排索引加速搜索
- **预计算**: 预计算常用查询的结果
- **分页加载**: 大结果集分页返回

### 3. 内存优化
- **流式处理**: 大文件流式处理
- **数据压缩**: 压缩存储表格数据
- **垃圾回收**: 及时释放不用的对象

## 🎨 使用示例

### 1. 基础表格搜索

```python
# 搜索财务相关表格
results = optimizer.search_tables("营收利润", top_k=5)

for result in results:
    print(f"文件: {result['file_name']}")
    print(f"页码: {result['page_number']}")
    print(f"类型: {result['table_type']}")
    print(f"相似度: {result['similarity']:.3f}")
```

### 2. 类型筛选搜索

```python
# 只搜索财务报表类型的表格
results = optimizer.search_tables(
    query="资产负债",
    table_type="财务报表",
    top_k=3
)
```

### 3. 优化搜索

```python
# 使用上下文信息优化搜索
context = {
    "history": [{"user_query": "公司财务状况"}],
    "knowledge": [{"content": "财务分析关键指标"}]
}

results = optimizer.optimize_table_search("财务指标", context)
```

### 4. 表格分析

```python
# 分析PDF中的表格结构
analysis = processor.analyze_table_structure("document.pdf")

print(f"总表格数: {analysis['total_tables']}")
print(f"表格类型分布: {analysis['table_types']}")
print(f"列分析: {analysis['column_analysis']}")
```

## 🔍 高级功能

### 1. 表格内容搜索

```python
# 在特定PDF的表格中搜索
matching_tables = processor.search_tables_in_pdf(
    "document.pdf", 
    "营收增长"
)
```

### 2. 表格特征分析

```python
# 获取表格详细特征
features = optimizer._extract_table_features(dataframe)

print(f"数值列: {features['numeric_columns']}")
print(f"关键指标: {features['key_metrics']}")
print(f"包含货币: {features['has_currency']}")
```

### 3. 自定义分类器

```python
# 自定义表格类型分类
def custom_classifier(df, search_text):
    if "投资组合" in search_text:
        return "投资数据"
    return "其他类型"

# 替换默认分类器
optimizer._classify_table_type = custom_classifier
```

## 📊 API接口详解

### 1. 表格搜索API

```http
GET /pdf/tables/search
Parameters:
  - query: 搜索查询 (必需)
  - table_type: 表格类型筛选 (可选)
  - top_k: 返回结果数量 (默认5)

Response:
{
  "results": [...],
  "query": "财务指标",
  "total_results": 3,
  "search_time_ms": 45.2
}
```

### 2. 表格提取API

```http
POST /pdf/tables/extract/{file_hash}

Response:
{
  "message": "表格提取和索引成功",
  "file_hash": "abc123"
}
```

### 3. 统计信息API

```http
GET /pdf/tables/statistics

Response:
{
  "total_files": 10,
  "total_tables": 45,
  "table_types": {"财务报表": 15, "市场数据": 10},
  "feature_distribution": {"has_numeric_data": 40}
}
```

## 🛠️ 故障排除

### 1. 表格提取失败
- **检查PDF格式**: 确保PDF包含可提取的表格
- **尝试不同引擎**: pdfplumber失败时自动尝试PyMuPDF
- **检查依赖**: 确保安装了所有必需的包

### 2. 搜索结果为空
- **检查索引**: 确保表格已正确索引
- **调整查询**: 尝试不同的关键词
- **检查阈值**: 降低相似度阈值

### 3. 性能问题
- **优化文件大小**: 处理前压缩PDF文件
- **分批处理**: 大量文档分批处理
- **清理缓存**: 定期清理临时文件

## 📝 最佳实践

### 1. 文档准备
- 使用高质量的PDF文档
- 确保表格结构清晰
- 避免扫描版PDF

### 2. 索引管理
- 定期更新索引
- 清理无效索引
- 备份索引数据

### 3. 查询优化
- 使用具体的金融术语
- 结合表格类型筛选
- 利用上下文信息

### 4. 结果处理
- 验证搜索结果的准确性
- 根据相似度筛选结果
- 提供用户反馈机制

## 🔮 未来扩展

### 1. 多模态支持
- 图表识别和提取
- 文本与图表关联
- 多媒体内容索引

### 2. 智能分析
- 表格数据趋势分析
- 异常值检测
- 预测性分析

### 3. 可视化增强
- 表格数据可视化
- 交互式图表生成
- 报告自动生成

## 📞 技术支持

如有问题或建议，请：
1. 查看日志文件获取详细错误信息
2. 运行演示脚本验证功能
3. 检查依赖包版本兼容性
4. 参考API文档和示例代码
