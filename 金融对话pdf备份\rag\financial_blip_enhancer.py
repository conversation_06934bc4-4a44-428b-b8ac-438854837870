"""
金融领域BLIP图像描述增强器
专门用于增强BLIP模型在金融图表和文档图像上的描述能力
"""
import os
import sys
import re
import json
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger

try:
    from PIL import Image
    import torch
    from transformers import BlipProcessor, BlipForConditionalGeneration
    import cv2
    BLIP_AVAILABLE = True
except ImportError:
    BLIP_AVAILABLE = False
    logger.warning("BLIP增强功能需要安装: torch, transformers, opencv-python, pillow")

class FinancialBLIPEnhancer:
    """金融领域BLIP图像描述增强器"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # 金融领域专业词汇映射
        self.financial_vocabulary = {
            # 图表类型
            "chart": "图表", "graph": "图表", "plot": "图表",
            "bar chart": "柱状图", "line chart": "折线图", "pie chart": "饼图",
            "candlestick": "K线图", "scatter plot": "散点图",
            
            # 财务概念
            "revenue": "营收", "income": "收入", "profit": "利润", "loss": "亏损",
            "assets": "资产", "liabilities": "负债", "equity": "权益",
            "cash flow": "现金流", "balance sheet": "资产负债表",
            "financial statement": "财务报表", "annual report": "年报",
            
            # 市场术语
            "stock": "股票", "market": "市场", "trading": "交易",
            "volume": "成交量", "price": "价格", "trend": "趋势",
            "growth": "增长", "decline": "下降", "volatility": "波动性",
            
            # 业务指标
            "performance": "业绩", "metrics": "指标", "analysis": "分析",
            "comparison": "对比", "distribution": "分布", "share": "份额",
            "percentage": "百分比", "ratio": "比率", "margin": "利润率"
        }
        
        # 金融图表模式识别
        self.chart_patterns = {
            "上升趋势": ["increasing", "rising", "upward", "growth", "positive"],
            "下降趋势": ["decreasing", "falling", "downward", "decline", "negative"],
            "波动": ["fluctuating", "volatile", "varying", "unstable"],
            "稳定": ["stable", "steady", "consistent", "flat"],
            "周期性": ["cyclical", "seasonal", "periodic", "recurring"]
        }
        
        # 金融颜色语义
        self.financial_colors = {
            "red": "红色(通常表示下跌或亏损)",
            "green": "绿色(通常表示上涨或盈利)",
            "blue": "蓝色(通常表示中性或基准)",
            "yellow": "黄色(通常表示警告或关注)",
            "orange": "橙色(通常表示中等风险)"
        }
        
        # 金融数据特征
        self.data_features = {
            "数值范围": ["thousands", "millions", "billions", "percentage"],
            "时间序列": ["quarterly", "annual", "monthly", "daily", "yearly"],
            "比较基准": ["year-over-year", "quarter-over-quarter", "baseline", "benchmark"]
        }
    
    def enhance_description(self, original_description: str, image: Image.Image, 
                          image_type: str = None) -> Dict[str, Any]:
        """
        增强BLIP生成的图像描述
        
        Args:
            original_description: BLIP原始描述
            image: PIL图像对象
            image_type: 图像类型（可选）
            
        Returns:
            增强后的描述信息
        """
        try:
            # 分析图像特征
            image_features = self._analyze_image_features(image)
            
            # 检测金融元素
            financial_elements = self._detect_financial_elements(image, original_description)
            
            # 增强描述
            enhanced_description = self._generate_enhanced_description(
                original_description, image_features, financial_elements, image_type
            )
            
            # 生成结构化信息
            structured_info = self._extract_structured_info(
                enhanced_description, image_features, financial_elements
            )
            
            return {
                "original_description": original_description,
                "enhanced_description": enhanced_description,
                "financial_elements": financial_elements,
                "image_features": image_features,
                "structured_info": structured_info,
                "confidence_score": self._calculate_confidence_score(financial_elements)
            }
            
        except Exception as e:
            logger.error(f"描述增强失败: {e}")
            return {
                "original_description": original_description,
                "enhanced_description": original_description,
                "error": str(e)
            }
    
    def _analyze_image_features(self, image: Image.Image) -> Dict[str, Any]:
        """分析图像特征"""
        try:
            features = {}
            
            # 基本信息
            features["size"] = {"width": image.width, "height": image.height}
            features["aspect_ratio"] = image.width / image.height
            
            # 颜色分析
            if image.mode == 'RGB':
                img_array = np.array(image)
                features["dominant_colors"] = self._get_dominant_colors(img_array)
                features["color_distribution"] = self._analyze_color_distribution(img_array)
            
            # 结构分析
            gray_image = image.convert('L')
            gray_array = np.array(gray_image)
            
            # 检测线条和形状
            features["has_lines"] = self._detect_lines(gray_array)
            features["has_bars"] = self._detect_bars(gray_array)
            features["has_circles"] = self._detect_circles(gray_array)
            
            # 文本区域检测
            features["text_regions"] = self._detect_text_regions(gray_array)
            
            # 网格检测
            features["has_grid"] = self._detect_grid_pattern(gray_array)
            
            return features
            
        except Exception as e:
            logger.error(f"图像特征分析失败: {e}")
            return {}
    
    def _get_dominant_colors(self, img_array: np.ndarray, k: int = 5) -> List[str]:
        """获取主要颜色"""
        try:
            # 简化颜色检测
            pixels = img_array.reshape(-1, 3)
            
            # 计算颜色直方图
            colors = []
            for pixel in pixels[::100]:  # 采样以提高性能
                r, g, b = pixel
                if r > 150 and g < 100 and b < 100:
                    colors.append("red")
                elif g > 150 and r < 100 and b < 100:
                    colors.append("green")
                elif b > 150 and r < 100 and g < 100:
                    colors.append("blue")
                elif r > 200 and g > 200 and b < 100:
                    colors.append("yellow")
                elif r > 200 and g > 100 and b < 100:
                    colors.append("orange")
            
            # 返回最常见的颜色
            if colors:
                from collections import Counter
                return [color for color, _ in Counter(colors).most_common(3)]
            return []
            
        except Exception:
            return []
    
    def _analyze_color_distribution(self, img_array: np.ndarray) -> Dict[str, float]:
        """分析颜色分布"""
        try:
            total_pixels = img_array.shape[0] * img_array.shape[1]
            
            # 计算各颜色通道的分布
            r_mean = np.mean(img_array[:, :, 0]) / 255.0
            g_mean = np.mean(img_array[:, :, 1]) / 255.0
            b_mean = np.mean(img_array[:, :, 2]) / 255.0
            
            return {
                "red_intensity": r_mean,
                "green_intensity": g_mean,
                "blue_intensity": b_mean,
                "brightness": (r_mean + g_mean + b_mean) / 3
            }
            
        except Exception:
            return {}
    
    def _detect_lines(self, gray_array: np.ndarray) -> bool:
        """检测线条（用于识别折线图）"""
        try:
            edges = cv2.Canny(gray_array, 50, 150)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
                                   minLineLength=30, maxLineGap=10)
            return lines is not None and len(lines) > 5
        except Exception:
            return False
    
    def _detect_bars(self, gray_array: np.ndarray) -> bool:
        """检测矩形条（用于识别柱状图）"""
        try:
            # 使用形态学操作检测矩形
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            morph = cv2.morphologyEx(gray_array, cv2.MORPH_CLOSE, kernel)
            
            contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 检查是否有多个矩形轮廓
            rect_count = 0
            for contour in contours:
                if cv2.contourArea(contour) > 100:
                    approx = cv2.approxPolyDP(contour, 0.02 * cv2.arcLength(contour, True), True)
                    if len(approx) == 4:  # 矩形
                        rect_count += 1
            
            return rect_count > 3
        except Exception:
            return False
    
    def _detect_circles(self, gray_array: np.ndarray) -> bool:
        """检测圆形（用于识别饼图）"""
        try:
            circles = cv2.HoughCircles(gray_array, cv2.HOUGH_GRADIENT, 1, 20,
                                     param1=50, param2=30, minRadius=10, maxRadius=100)
            return circles is not None and len(circles[0]) > 0
        except Exception:
            return False
    
    def _detect_text_regions(self, gray_array: np.ndarray) -> int:
        """检测文本区域数量"""
        try:
            # 简单的文本区域检测
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            morph = cv2.morphologyEx(gray_array, cv2.MORPH_CLOSE, kernel)
            
            contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            text_regions = 0
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                if w > 10 and h > 5 and w/h > 2:  # 文本特征：宽度大于高度
                    text_regions += 1
            
            return text_regions
        except Exception:
            return 0
    
    def _detect_grid_pattern(self, gray_array: np.ndarray) -> bool:
        """检测网格模式"""
        try:
            # 检测水平和垂直线
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
            
            horizontal_lines = cv2.morphologyEx(gray_array, cv2.MORPH_OPEN, horizontal_kernel)
            vertical_lines = cv2.morphologyEx(gray_array, cv2.MORPH_OPEN, vertical_kernel)
            
            # 如果同时检测到水平和垂直线，可能是网格
            h_score = np.sum(horizontal_lines > 0)
            v_score = np.sum(vertical_lines > 0)
            
            return h_score > 100 and v_score > 100
        except Exception:
            return False
    
    def _detect_financial_elements(self, image: Image.Image, description: str) -> Dict[str, Any]:
        """检测金融元素"""
        try:
            elements = {
                "chart_type": "未知",
                "financial_keywords": [],
                "trend_indicators": [],
                "color_semantics": [],
                "data_patterns": []
            }
            
            description_lower = description.lower()
            
            # 检测图表类型
            if any(keyword in description_lower for keyword in ["bar", "column"]):
                elements["chart_type"] = "柱状图"
            elif any(keyword in description_lower for keyword in ["line", "trend"]):
                elements["chart_type"] = "折线图"
            elif any(keyword in description_lower for keyword in ["pie", "circle"]):
                elements["chart_type"] = "饼图"
            elif any(keyword in description_lower for keyword in ["scatter", "dot"]):
                elements["chart_type"] = "散点图"
            
            # 检测金融关键词
            for eng_word, cn_word in self.financial_vocabulary.items():
                if eng_word in description_lower:
                    elements["financial_keywords"].append(cn_word)
            
            # 检测趋势指标
            for trend, indicators in self.chart_patterns.items():
                if any(indicator in description_lower for indicator in indicators):
                    elements["trend_indicators"].append(trend)
            
            # 检测颜色语义
            for color, meaning in self.financial_colors.items():
                if color in description_lower:
                    elements["color_semantics"].append(meaning)
            
            return elements

        except Exception as e:
            logger.error(f"金融元素检测失败: {e}")
            return {}

    def _generate_enhanced_description(self, original_description: str,
                                     image_features: Dict[str, Any],
                                     financial_elements: Dict[str, Any],
                                     image_type: str = None) -> str:
        """生成增强的描述"""
        try:
            enhanced_parts = []

            # 基础描述翻译和增强
            base_description = self._translate_and_enhance_base(original_description)
            enhanced_parts.append(base_description)

            # 添加图表类型信息
            chart_type = financial_elements.get("chart_type", "未知")
            if chart_type != "未知":
                enhanced_parts.append(f"这是一个{chart_type}")

            # 添加金融语义
            financial_keywords = financial_elements.get("financial_keywords", [])
            if financial_keywords:
                keywords_str = "、".join(financial_keywords[:3])  # 限制关键词数量
                enhanced_parts.append(f"涉及{keywords_str}等金融概念")

            # 添加趋势信息
            trends = financial_elements.get("trend_indicators", [])
            if trends:
                trend_str = "、".join(trends[:2])
                enhanced_parts.append(f"显示{trend_str}的特征")

            # 添加颜色语义
            color_semantics = financial_elements.get("color_semantics", [])
            if color_semantics:
                enhanced_parts.append(f"使用了{color_semantics[0]}")

            # 添加结构特征
            structure_info = self._describe_structure_features(image_features)
            if structure_info:
                enhanced_parts.append(structure_info)

            # 组合描述
            enhanced_description = "，".join(enhanced_parts) + "。"

            return enhanced_description

        except Exception as e:
            logger.error(f"生成增强描述失败: {e}")
            return original_description

    def _translate_and_enhance_base(self, description: str) -> str:
        """翻译和增强基础描述"""
        try:
            # 简单的英文到中文翻译映射
            translation_map = {
                "a chart": "一个图表",
                "a graph": "一个图表",
                "a bar chart": "一个柱状图",
                "a line chart": "一个折线图",
                "a pie chart": "一个饼图",
                "showing": "显示",
                "with": "包含",
                "data": "数据",
                "numbers": "数字",
                "values": "数值",
                "different": "不同的",
                "various": "各种",
                "multiple": "多个"
            }

            enhanced = description.lower()
            for eng, chn in translation_map.items():
                enhanced = enhanced.replace(eng, chn)

            # 如果翻译效果不好，保留原文并添加中文说明
            if enhanced == description.lower():
                return f"图像内容：{description}"

            return enhanced

        except Exception:
            return description

    def _describe_structure_features(self, features: Dict[str, Any]) -> str:
        """描述结构特征"""
        try:
            descriptions = []

            # 网格特征
            if features.get("has_grid", False):
                descriptions.append("具有网格结构")

            # 形状特征
            if features.get("has_bars", False):
                descriptions.append("包含柱状元素")
            elif features.get("has_lines", False):
                descriptions.append("包含线性元素")
            elif features.get("has_circles", False):
                descriptions.append("包含圆形元素")

            # 文本特征
            text_regions = features.get("text_regions", 0)
            if text_regions > 5:
                descriptions.append("包含丰富的文本标注")
            elif text_regions > 0:
                descriptions.append("包含文本标注")

            return "，".join(descriptions) if descriptions else ""

        except Exception:
            return ""

    def _extract_structured_info(self, description: str,
                                image_features: Dict[str, Any],
                                financial_elements: Dict[str, Any]) -> Dict[str, Any]:
        """提取结构化信息"""
        try:
            structured = {
                "图表类型": financial_elements.get("chart_type", "未知"),
                "金融概念": financial_elements.get("financial_keywords", []),
                "趋势特征": financial_elements.get("trend_indicators", []),
                "颜色语义": financial_elements.get("color_semantics", []),
                "结构特征": {
                    "有网格": image_features.get("has_grid", False),
                    "有柱状元素": image_features.get("has_bars", False),
                    "有线性元素": image_features.get("has_lines", False),
                    "有圆形元素": image_features.get("has_circles", False),
                    "文本区域数": image_features.get("text_regions", 0)
                },
                "图像属性": {
                    "宽高比": round(image_features.get("aspect_ratio", 1.0), 2),
                    "主要颜色": image_features.get("dominant_colors", [])
                }
            }

            return structured

        except Exception as e:
            logger.error(f"提取结构化信息失败: {e}")
            return {}

    def _calculate_confidence_score(self, financial_elements: Dict[str, Any]) -> float:
        """计算置信度分数"""
        try:
            score = 0.0

            # 图表类型识别加分
            if financial_elements.get("chart_type", "未知") != "未知":
                score += 0.3

            # 金融关键词加分
            keywords_count = len(financial_elements.get("financial_keywords", []))
            score += min(keywords_count * 0.1, 0.3)

            # 趋势指标加分
            trends_count = len(financial_elements.get("trend_indicators", []))
            score += min(trends_count * 0.1, 0.2)

            # 颜色语义加分
            colors_count = len(financial_elements.get("color_semantics", []))
            score += min(colors_count * 0.1, 0.2)

            return min(score, 1.0)

        except Exception:
            return 0.0

    def generate_financial_prompts(self, image_type: str = None) -> List[str]:
        """生成金融领域特定的BLIP提示词"""
        try:
            base_prompts = [
                "a financial chart showing",
                "a business graph displaying",
                "a financial report with",
                "a market analysis chart",
                "a revenue chart showing",
                "a profit and loss graph"
            ]

            specific_prompts = []

            if image_type:
                if "柱状图" in image_type or "bar" in image_type.lower():
                    specific_prompts.extend([
                        "a bar chart showing financial data",
                        "a column chart displaying revenue",
                        "a financial bar graph with quarterly results"
                    ])
                elif "折线图" in image_type or "line" in image_type.lower():
                    specific_prompts.extend([
                        "a line chart showing financial trends",
                        "a trend graph displaying market performance",
                        "a financial line chart with time series data"
                    ])
                elif "饼图" in image_type or "pie" in image_type.lower():
                    specific_prompts.extend([
                        "a pie chart showing financial distribution",
                        "a financial pie chart displaying market share",
                        "a circular chart showing budget allocation"
                    ])

            return base_prompts + specific_prompts

        except Exception as e:
            logger.error(f"生成金融提示词失败: {e}")
            return ["a financial chart"]

    def enhance_with_context(self, description: str, context_info: Dict[str, Any]) -> str:
        """基于上下文信息增强描述"""
        try:
            enhanced = description

            # 添加文档上下文
            if "document_title" in context_info:
                title = context_info["document_title"]
                if any(keyword in title.lower() for keyword in ["财报", "年报", "季报", "financial", "annual"]):
                    enhanced += f"，来源于{title}"

            # 添加页面上下文
            if "page_number" in context_info:
                enhanced += f"，位于第{context_info['page_number']}页"

            # 添加相关文本上下文
            if "surrounding_text" in context_info:
                text = context_info["surrounding_text"]
                if text and len(text) > 10:
                    # 提取关键信息
                    if "营收" in text or "revenue" in text.lower():
                        enhanced += "，与营收数据相关"
                    elif "利润" in text or "profit" in text.lower():
                        enhanced += "，与利润分析相关"
                    elif "市场" in text or "market" in text.lower():
                        enhanced += "，与市场分析相关"

            return enhanced

        except Exception as e:
            logger.error(f"上下文增强失败: {e}")
            return description
