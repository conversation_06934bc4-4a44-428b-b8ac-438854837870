<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="DjangoUrlArgumentsInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPep8Inspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="E231" />
          <option value="W292" />
          <option value="E501" />
          <option value="E402" />
          <option value="E302" />
          <option value="E305" />
          <option value="E731" />
          <option value="E301" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyShadowingBuiltinsInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredNames">
        <list>
          <option value="sum" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>