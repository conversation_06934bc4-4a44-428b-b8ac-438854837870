"""
配置文件 - 金融对话系统
"""
import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Milvus配置
    MILVUS_HOST = os.getenv("MILVUS_HOST", "localhost")
    MILVUS_PORT = os.getenv("MILVUS_PORT", "19530")
    
    # 知识库集合名称
    KNOWLEDGE_COLLECTION = "financial_knowledge"  # 专业信息和术语知识库
    HISTORY_COLLECTION = "conversation_history"   # 历史对话存储
    
    # 向量维度 (BGE-M3模型为1024维)
    VECTOR_DIM = 1024
    
    # 大模型API配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    MODEL_NAME = os.getenv("MODEL_NAME", "gpt-3.5-turbo")
    
    # 其他在线大模型配置
    # 智谱AI
    ZHIPU_API_KEY = os.getenv("ZHIPU_API_KEY", "")
    ZHIPU_BASE_URL = os.getenv("ZHIPU_BASE_URL", "https://open.bigmodel.cn/api/paas/v4")
    ZHIPU_MODEL = os.getenv("ZHIPU_MODEL", "glm-4")

    # 通义千问
    QWEN_API_KEY = os.getenv("QWEN_API_KEY", "")
    QWEN_BASE_URL = os.getenv("QWEN_BASE_URL", "https://dashscope.aliyuncs.com/api/v1")
    QWEN_MODEL = os.getenv("QWEN_MODEL", "qwen-turbo")

    # 硅基流动
    SILICONFLOW_API_KEY = os.getenv("SILICONFLOW_API_KEY", "")
    SILICONFLOW_BASE_URL = os.getenv("SILICONFLOW_BASE_URL", "https://api.siliconflow.cn/v1")
    SILICONFLOW_MODEL = os.getenv("SILICONFLOW_MODEL", "Qwen/Qwen2.5-7B-Instruct")

    # 百度文心一言
    BAIDU_API_KEY = os.getenv("BAIDU_API_KEY", "")
    BAIDU_SECRET_KEY = os.getenv("BAIDU_SECRET_KEY", "")
    BAIDU_BASE_URL = os.getenv("BAIDU_BASE_URL", "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop")
    BAIDU_MODEL = os.getenv("BAIDU_MODEL", "ernie-bot-turbo")

    # 讯飞星火
    XUNFEI_APP_ID = os.getenv("XUNFEI_APP_ID", "")
    XUNFEI_API_KEY = os.getenv("XUNFEI_API_KEY", "")
    XUNFEI_API_SECRET = os.getenv("XUNFEI_API_SECRET", "")
    XUNFEI_BASE_URL = os.getenv("XUNFEI_BASE_URL", "wss://spark-api.xf-yun.com/v3.5/chat")
    XUNFEI_MODEL = os.getenv("XUNFEI_MODEL", "spark-3.5")

    # 腾讯混元
    TENCENT_SECRET_ID = os.getenv("TENCENT_SECRET_ID", "")
    TENCENT_SECRET_KEY = os.getenv("TENCENT_SECRET_KEY", "")
    TENCENT_REGION = os.getenv("TENCENT_REGION", "ap-beijing")
    TENCENT_MODEL = os.getenv("TENCENT_MODEL", "hunyuan-lite")

    # 字节豆包
    DOUBAO_API_KEY = os.getenv("DOUBAO_API_KEY", "")
    DOUBAO_BASE_URL = os.getenv("DOUBAO_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
    DOUBAO_MODEL = os.getenv("DOUBAO_MODEL", "ep-20241220105718-8xqkz")

    # Anthropic Claude
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "")
    ANTHROPIC_BASE_URL = os.getenv("ANTHROPIC_BASE_URL", "https://api.anthropic.com")
    ANTHROPIC_MODEL = os.getenv("ANTHROPIC_MODEL", "claude-3-sonnet-20240229")

    # Google Gemini
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")
    GOOGLE_BASE_URL = os.getenv("GOOGLE_BASE_URL", "https://generativelanguage.googleapis.com/v1beta")
    GOOGLE_MODEL = os.getenv("GOOGLE_MODEL", "gemini-pro")

    # Moonshot AI (Kimi)
    MOONSHOT_API_KEY = os.getenv("MOONSHOT_API_KEY", "")
    MOONSHOT_BASE_URL = os.getenv("MOONSHOT_BASE_URL", "https://api.moonshot.cn/v1")
    MOONSHOT_MODEL = os.getenv("MOONSHOT_MODEL", "moonshot-v1-8k")

    # DeepSeek
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")
    DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")
    
    # 嵌入模型配置
    EMBEDDING_MODEL = r"D:\桌面\工作\bge-m3"
    
    # RAG配置
    TOP_K = 5  # 检索的相关文档数量
    SIMILARITY_THRESHOLD = 0.5  # 余弦相似度阈值 (范围[-1,1], BGE-M3推荐0.5以上)
    
    # FastAPI配置
    API_HOST = "0.0.0.0"
    API_PORT = 8000
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/financial_chat.log"
