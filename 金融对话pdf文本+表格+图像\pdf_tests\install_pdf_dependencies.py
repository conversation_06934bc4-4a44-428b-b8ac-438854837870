"""
PDF功能依赖安装脚本
自动安装PDF处理所需的依赖包
"""
import subprocess
import sys
from loguru import logger

def install_package(package):
    """安装单个包"""
    try:
        logger.info(f"正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, check=True)
        logger.info(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ {package} 安装失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    logger.info("开始安装PDF功能依赖包...")
    
    # PDF功能所需的依赖包
    pdf_dependencies = [
        "langchain>=0.1.0",
        "langchain-community>=0.0.10", 
        "langchain-text-splitters>=0.0.1",
        "pypdf>=3.17.0",
        "pymupdf>=1.23.0",
        "pdfplumber>=0.10.0",
        "python-docx>=1.1.0",
        "openpyxl>=3.1.0"
    ]
    
    success_count = 0
    total_count = len(pdf_dependencies)
    
    for package in pdf_dependencies:
        if install_package(package):
            success_count += 1
    
    logger.info(f"\n安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        logger.info("🎉 所有PDF功能依赖安装成功！")
        logger.info("现在可以使用PDF处理功能了。")
    else:
        logger.warning("⚠ 部分依赖安装失败，可能影响PDF功能。")
        logger.info("请手动安装失败的依赖包。")
    
    # 验证安装
    logger.info("\n验证安装...")
    test_imports = [
        ("langchain", "LangChain框架"),
        ("langchain_community", "LangChain社区组件"),
        ("langchain_text_splitters", "LangChain文本分割器"),
        ("pypdf", "PyPDF"),
        ("fitz", "PyMuPDF"),
        ("pdfplumber", "PDFPlumber"),
        ("docx", "python-docx"),
        ("openpyxl", "OpenPyXL")
    ]
    
    for module, name in test_imports:
        try:
            __import__(module)
            logger.info(f"✓ {name} 导入成功")
        except ImportError:
            logger.error(f"✗ {name} 导入失败")

if __name__ == "__main__":
    main()
