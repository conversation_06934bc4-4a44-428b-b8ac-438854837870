# 金融对话系统

基于FastAPI、RAG和大模型的智能金融对话系统，支持专业金融知识检索和对话历史管理。

## 系统特性

- 🤖 **多模型支持**: 支持OpenAI、智谱AI、通义千问、硅基流动、百度文心一言、Moonshot AI、DeepSeek、字节豆包、Anthropic Claude、Google Gemini等多种在线大模型
- 📚 **RAG检索**: 基于Milvus向量数据库的知识检索系统，使用BGE-M3嵌入模型
- 💾 **双知识库**: 专业信息知识库 + 历史对话存储
- 🚀 **FastAPI接口**: 高性能RESTful API服务
- 📊 **模块化设计**: 各功能模块独立，易于维护和扩展
- 🔍 **智能检索**: 基于余弦相似度的语义搜索
- 📝 **对话历史**: 自动保存和检索历史对话上下文

## 系统架构

```
金融对话/
├── main.py                          # 主程序入口
├── .env.example                     # 环境变量配置模板
├── idconfig/                        # 配置模块
│   ├── config.py                    # 配置管理
│   ├── requirements.txt             # 依赖包列表
│   ├── MODEL_CONFIG.md              # 模型配置说明
│   └── README.md                    # 说明文档
├── rag/                             # RAG检索模块
│   ├── milvus_manager.py            # Milvus数据库管理
│   └── rag_system.py                # RAG检索系统
├── llm/                             # 大模型服务模块
│   └── llm_service.py               # 大模型调用服务
├── api/                             # Web API模块
│   └── api_server.py                # FastAPI服务器
├── 检查向量模型/                     # 模型管理工具
│   ├── check_bge_m3.py              # BGE-M3模型检查
│   └── download_bge_m3.py           # BGE-M3模型下载
└── 测试/                           # 测试和工具脚本
    ├── test_client.py               # 测试客户端
    ├── setup_env.py                 # 环境设置
    └── start.py                     # 启动脚本
```

## 快速开始

### 1. 环境准备

```bash
# 克隆或下载项目
cd 金融对话

# 安装Python依赖
pip install -r idconfig/requirements.txt
```

### 2. 下载BGE-M3嵌入模型

```bash
# 方法1: 使用git下载（推荐）
git clone https://huggingface.co/BAAI/bge-m3 ./bge-m3

# 方法2: 使用Python脚本下载
python 检查向量模型/download_bge_m3.py

# 验证模型下载
python 检查向量模型/check_bge_m3.py
```

### 3. 配置环境变量

创建 `.env` 文件并配置相关参数：

```env
# Milvus数据库配置
MILVUS_HOST=localhost
MILVUS_PORT=19530

# 嵌入模型配置
EMBEDDING_MODEL=./bge-m3
VECTOR_DIM=1024

# OpenAI配置（至少配置一个大模型API）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
MODEL_NAME=gpt-3.5-turbo

# 其他大模型API配置（可选）
ZHIPU_API_KEY=your_zhipu_api_key_here
QWEN_API_KEY=your_qwen_api_key_here
SILICONFLOW_API_KEY=your_siliconflow_api_key_here
BAIDU_API_KEY=your_baidu_api_key_here
BAIDU_SECRET_KEY=your_baidu_secret_key_here
MOONSHOT_API_KEY=your_moonshot_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DOUBAO_API_KEY=your_doubao_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# 系统配置
TOP_K=5
SIMILARITY_THRESHOLD=0.5
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO
```

### 4. 启动Milvus向量数据库

使用Docker启动Milvus：

```bash
# 下载Milvus docker-compose文件
wget https://github.com/milvus-io/milvus/releases/download/v2.3.4/milvus-standalone-docker-compose.yml -O docker-compose.yml

# 启动Milvus
docker-compose up -d

# 验证Milvus启动
docker-compose ps
```

### 5. 系统初始化和测试

```bash
# 测试系统配置
python main.py --mode test

# 初始化系统并添加示例数据
python main.py --mode init --add-sample
```

### 6. 启动服务

```bash
# 启动API服务器
python main.py --mode server

# 或者使用测试客户端
python 测试/test_client.py
```

服务启动后，访问 http://localhost:8000 查看API文档。

## API接口

### 对话接口

```bash
POST /chat
```

请求示例：
```json
{
    "message": "什么是股票？",
    "session_id": "optional_session_id",
    "model_provider": "openai",
    "model": "optional_specific_model",
    "temperature": 0.7
}
```

其他模型示例：
```json
// 硅基流动
{
    "message": "什么是股票？",
    "model_provider": "siliconflow",
    "model": "Qwen/Qwen2.5-7B-Instruct",
    "temperature": 0.7
}

// 百度文心一言
{
    "message": "什么是股票？",
    "model_provider": "baidu",
    "temperature": 0.7
}

// Moonshot AI (Kimi)
{
    "message": "什么是股票？",
    "model_provider": "moonshot",
    "temperature": 0.7
}

// DeepSeek
{
    "message": "什么是股票？",
    "model_provider": "deepseek",
    "temperature": 0.7
}

// Anthropic Claude
{
    "message": "什么是股票？",
    "model_provider": "claude",
    "temperature": 0.7
}
```

### 知识管理

```bash
# 添加单条知识
POST /knowledge/add

# 批量添加知识
POST /knowledge/batch_add

# 搜索知识库
GET /knowledge/search?query=股票&top_k=5
```

### 历史对话

```bash
# 搜索历史对话
GET /history/search?query=股票&top_k=5
```

### 系统状态

```bash
# 健康检查
GET /health

# 获取可用模型
GET /models

# 获取硅基流动模型列表
GET /models/siliconflow

# 获取所有模型详细信息
GET /models/info
```

## 使用示例

### Python客户端示例

```python
import requests

# 发送对话请求
response = requests.post("http://localhost:8000/chat", json={
    "message": "请解释一下什么是市盈率？",
    "model_provider": "openai"
})

result = response.json()
print(f"回复: {result['response']}")
print(f"会话ID: {result['session_id']}")
```

### 添加知识示例

```python
# 添加单条知识
requests.post("http://localhost:8000/knowledge/add", json={
    "content": "通胀是指货币供应量增加导致的物价普遍上涨现象",
    "category": "宏观经济",
    "source": "经济学教材"
})

# 批量添加知识
requests.post("http://localhost:8000/knowledge/batch_add", json={
    "knowledge_list": [
        {
            "content": "GDP是国内生产总值的简称",
            "category": "宏观经济",
            "source": "统计局"
        },
        {
            "content": "CPI是消费者价格指数",
            "category": "经济指标",
            "source": "统计局"
        }
    ]
})
```

## 系统测试

```bash
# 测试系统功能
python main.py --mode test
```

## 配置说明

### Milvus配置
- `MILVUS_HOST`: Milvus服务器地址
- `MILVUS_PORT`: Milvus服务器端口

### 嵌入模型配置
- `EMBEDDING_MODEL`: 嵌入模型路径（默认"./bge-m3"）
- `VECTOR_DIM`: 向量维度（BGE-M3为1024维）

### 大模型配置
- `OPENAI_API_KEY`: OpenAI API密钥
- `ZHIPU_API_KEY`: 智谱AI API密钥
- `QWEN_API_KEY`: 通义千问API密钥

### 系统配置
- `TOP_K`: 检索结果数量（默认5）
- `SIMILARITY_THRESHOLD`: 余弦相似度阈值（默认0.5，范围[-1,1]）

### BGE-M3模型配置

本系统使用BGE-M3作为默认嵌入模型，这是一个高性能的多语言嵌入模型：

1. **下载BGE-M3模型**：
   ```bash
   # 使用git下载
   git clone https://huggingface.co/BAAI/bge-m3 ./bge-m3

   # 或使用huggingface_hub
   python -c "from huggingface_hub import snapshot_download; snapshot_download('BAAI/bge-m3', local_dir='./bge-m3')"
   ```

2. **验证模型配置**：
   ```bash
   python check_bge_m3.py
   ```

3. **模型特点**：
   - 向量维度: 1024
   - 支持中英文
   - 归一化向量输出
   - 适合余弦相似度计算

## 故障排除

### 常见问题

1. **Milvus连接失败**
   - 检查Milvus服务是否启动
   - 确认端口配置正确

2. **大模型API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常

3. **向量维度不匹配**
   - 检查嵌入模型配置
   - 确认VECTOR_DIM设置正确

### 日志查看

系统日志保存在 `logs/financial_chat.log`，可以查看详细的运行信息和错误信息。

## 项目结构详解

### 核心模块

1. **main.py** - 系统主入口
   - 提供多种运行模式：server、test、init
   - 统一的日志配置和环境检查
   - 系统初始化和示例数据添加

2. **idconfig/config.py** - 配置管理
   - 支持环境变量和.env文件配置
   - 多种大模型API配置
   - 向量数据库和RAG参数配置

3. **rag/rag_system.py** - RAG检索系统
   - BGE-M3嵌入模型集成
   - 文本向量化和相似度搜索
   - 知识库和历史对话管理

4. **rag/milvus_manager.py** - Milvus数据库管理
   - 向量数据库连接和集合管理
   - 支持余弦相似度搜索
   - 双集合设计：知识库+历史对话

5. **llm/llm_service.py** - 大模型服务
   - 支持10+种主流大模型API
   - 统一的消息格式和错误处理
   - 金融领域专业提示词模板

6. **api/api_server.py** - Web API服务
   - RESTful API接口
   - 自动API文档生成
   - CORS支持和错误处理

### 工具模块

- **检查向量模型/** - BGE-M3模型管理工具
- **测试/** - 系统测试和客户端工具

## 技术特点

### 向量检索技术
- **嵌入模型**: BGE-M3 (1024维，支持中英文)
- **相似度计算**: 余弦相似度
- **检索策略**: Top-K + 阈值过滤
- **向量数据库**: Milvus 2.3+

### 大模型集成
- **统一接口**: 支持多种API格式
- **错误处理**: 自动重试和降级
- **提示工程**: 金融领域专业提示词
- **上下文管理**: 知识库+历史对话融合

### 系统架构
- **模块化设计**: 松耦合，易扩展
- **异步处理**: FastAPI异步支持
- **配置驱动**: 环境变量配置
- **日志系统**: 结构化日志记录

## 扩展开发

### 添加新的大模型支持

在 `llm/llm_service.py` 中添加新的模型调用方法：

```python
def call_new_model(self, messages, temperature=0.7):
    """调用新模型API"""
    try:
        # 实现新模型的API调用逻辑
        # 1. 配置API客户端
        # 2. 格式化消息
        # 3. 发送请求
        # 4. 处理响应
        return response_text
    except Exception as e:
        logger.error(f"调用新模型失败: {e}")
        return "抱歉，处理您的请求时出现了错误。"

# 在generate_response方法中添加新模型的调用逻辑
def generate_response(self, messages, model_provider="openai", **kwargs):
    if model_provider == "new_model":
        return self.call_new_model(messages, **kwargs)
    # ... 其他模型逻辑
```

### 自定义嵌入模型

1. 在 `idconfig/config.py` 中修改配置：

```python
# 自定义嵌入模型
EMBEDDING_MODEL = "your_custom_embedding_model"
VECTOR_DIM = your_model_dimension  # 根据模型调整维度
```

2. 确保模型兼容sentence-transformers接口

### 扩展知识库功能

```python
# 在rag_system.py中添加新的知识管理方法
def add_structured_knowledge(self, data_dict):
    """添加结构化知识"""
    # 实现结构化数据处理逻辑
    pass

def update_knowledge(self, knowledge_id, new_content):
    """更新已有知识"""
    # 实现知识更新逻辑
    pass
```

## 性能优化建议

### 向量检索优化
- 调整Milvus索引参数（nlist, nprobe）
- 使用GPU加速向量计算
- 实现向量缓存机制

### 大模型调用优化
- 实现请求池和连接复用
- 添加响应缓存
- 支持流式响应

### 系统性能优化
- 使用Redis缓存热点数据
- 实现异步任务队列
- 添加监控和指标收集

## 部署指南

### Docker部署

```dockerfile
# Dockerfile示例
FROM python:3.9-slim

WORKDIR /app
COPY . .

RUN pip install -r idconfig/requirements.txt

EXPOSE 8000
CMD ["python", "main.py", "--mode", "server"]
```

### 生产环境配置

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn api.api_server:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 许可证

MIT License

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 联系方式

如有问题或建议，欢迎通过以下方式联系：
- 提交 Issue
- 发送 Pull Request
- 邮件联系（如有）

## 更新日志

### v1.0.0
- 初始版本发布
- 支持多种大模型API
- 实现RAG检索系统
- 提供完整的Web API接口
