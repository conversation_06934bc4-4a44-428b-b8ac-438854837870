"""
多模态图像检索演示
展示如何使用CLIP和多模态大模型对PDF文档中的图像进行智能检索
"""
import os
import sys
import time
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.multimodal_retrieval import MultimodalImageRetriever
from rag.pdf_manager import PDFManager
from loguru import logger

def demo_model_initialization():
    """演示模型初始化"""
    print("=" * 60)
    print("多模态模型初始化演示")
    print("=" * 60)
    
    # 检查依赖
    try:
        import torch
        import clip
        from transformers import BlipProcessor, BlipForConditionalGeneration
        print("✅ 所有依赖已安装")
        
        # 检查GPU支持
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"🎮 GPU支持: {gpu_count} 个GPU ({gpu_name})")
        else:
            print("💻 运行在CPU模式")
            
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: python install_multimodal_dependencies.py")
        return False
    
    # 初始化检索器
    print("\n🔄 初始化多模态检索器...")
    retriever = MultimodalImageRetriever()
    
    start_time = time.time()
    success = retriever.initialize()
    init_time = time.time() - start_time
    
    if success:
        print(f"✅ 初始化成功 (耗时: {init_time:.2f}秒)")
        print(f"   CLIP模型: {'已加载' if retriever.clip_model else '未加载'}")
        print(f"   BLIP模型: {'已加载' if retriever.blip_model else '未加载'}")
        print(f"   运行设备: {retriever.device}")
        return True
    else:
        print("❌ 初始化失败")
        return False

def demo_image_extraction():
    """演示图像提取功能"""
    print("\n" + "=" * 60)
    print("PDF图像提取演示")
    print("=" * 60)
    
    # 初始化检索器
    retriever = MultimodalImageRetriever()
    if not retriever.initialize():
        print("❌ 检索器初始化失败")
        return
    
    # 查找示例PDF文件
    pdf_dir = Path("texts_pdf")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到示例PDF文件")
        print("请将PDF文件放在 texts_pdf/ 目录下")
        return
    
    # 使用第一个PDF文件进行演示
    pdf_file = pdf_files[0]
    print(f"📄 处理文件: {pdf_file.name}")
    
    # 生成文件哈希
    import hashlib
    file_hash = hashlib.md5(str(pdf_file).encode()).hexdigest()[:16]
    
    # 提取图像
    print("\n🖼️  提取图像中...")
    start_time = time.time()
    images = retriever.extract_images_from_pdf(str(pdf_file), file_hash)
    extract_time = time.time() - start_time
    
    if images:
        print(f"✅ 成功提取 {len(images)} 个图像 (耗时: {extract_time:.2f}秒)")
        
        for i, image_info in enumerate(images[:3], 1):  # 只显示前3个图像
            print(f"\n📊 图像 {i}:")
            print(f"   ID: {image_info['image_id']}")
            print(f"   页码: {image_info['page_number']}")
            print(f"   尺寸: {image_info['width']}x{image_info['height']}")
            print(f"   类型: {image_info['image_type']}")
            print(f"   描述: {image_info['description'][:100]}...")
    else:
        print("❌ 未找到图像")

def demo_image_indexing():
    """演示图像索引功能"""
    print("\n" + "=" * 60)
    print("图像索引演示")
    print("=" * 60)
    
    # 初始化检索器
    retriever = MultimodalImageRetriever()
    if not retriever.initialize():
        print("❌ 检索器初始化失败")
        return
    
    # 查找示例PDF文件
    pdf_dir = Path("texts_pdf")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到示例PDF文件")
        return
    
    # 为每个PDF文件建立索引
    for pdf_file in pdf_files[:2]:  # 只处理前2个文件
        print(f"\n📄 索引文件: {pdf_file.name}")
        
        # 生成文件哈希
        import hashlib
        file_hash = hashlib.md5(str(pdf_file).encode()).hexdigest()[:16]
        
        # 建立索引
        start_time = time.time()
        success = retriever.index_images(str(pdf_file), file_hash)
        index_time = time.time() - start_time
        
        if success:
            print(f"✅ 索引成功 (耗时: {index_time:.2f}秒)")
        else:
            print(f"❌ 索引失败")
    
    # 显示索引统计
    stats = retriever.get_image_statistics()
    print(f"\n📈 索引统计:")
    print(f"   总文件数: {stats.get('total_files', 0)}")
    print(f"   总图像数: {stats.get('total_images', 0)}")
    print(f"   图像类型: {stats.get('image_types', {})}")
    print(f"   尺寸分布: {stats.get('size_distribution', {})}")

def demo_text_to_image_search():
    """演示文本到图像搜索"""
    print("\n" + "=" * 60)
    print("文本到图像搜索演示")
    print("=" * 60)
    
    # 初始化检索器
    retriever = MultimodalImageRetriever()
    if not retriever.initialize():
        print("❌ 检索器初始化失败")
        return
    
    # 测试查询
    test_queries = [
        "财务图表",
        "柱状图",
        "收入趋势",
        "市场数据",
        "profit chart",
        "financial statement"
    ]
    
    for query in test_queries:
        print(f"\n🔍 搜索: '{query}'")
        
        start_time = time.time()
        results = retriever.search_images_by_text(query, top_k=3)
        search_time = (time.time() - start_time) * 1000
        
        print(f"⏱️  搜索耗时: {search_time:.2f}ms")
        print(f"📊 找到 {len(results)} 个相关图像")
        
        for i, result in enumerate(results, 1):
            print(f"\n   结果 {i}:")
            print(f"   文件: {result.get('pdf_name', '未知')}")
            print(f"   页码: {result.get('page_number', 0)}")
            print(f"   类型: {result.get('image_type', '未知')}")
            print(f"   相似度: {result.get('similarity', 0):.3f}")
            print(f"   文本得分: {result.get('text_score', 0):.3f}")
            print(f"   综合得分: {result.get('final_score', 0):.3f}")

def demo_image_analysis():
    """演示图像分析功能"""
    print("\n" + "=" * 60)
    print("图像分析演示")
    print("=" * 60)
    
    # 初始化检索器
    retriever = MultimodalImageRetriever()
    if not retriever.initialize():
        print("❌ 检索器初始化失败")
        return
    
    # 获取第一个图像进行分析
    if not retriever.image_index:
        print("❌ 没有可分析的图像，请先运行索引演示")
        return
    
    # 找到第一个图像
    first_file = next(iter(retriever.image_index.values()))
    if not first_file.get("images"):
        print("❌ 没有找到图像")
        return
    
    first_image = first_file["images"][0]
    image_id = first_image["image_id"]
    
    print(f"🔍 分析图像: {image_id}")
    
    # 执行分析
    start_time = time.time()
    analysis = retriever.analyze_image_content(image_id)
    analysis_time = time.time() - start_time
    
    if "error" not in analysis:
        print(f"✅ 分析完成 (耗时: {analysis_time:.2f}秒)")
        
        print(f"\n📊 基本信息:")
        basic_info = analysis.get("basic_info", {})
        for key, value in basic_info.items():
            print(f"   {key}: {value}")
        
        print(f"\n📝 描述: {analysis.get('description', '无')}")
        print(f"🏷️  类型: {analysis.get('image_type', '无')}")
        print(f"🎨 复杂度: {analysis.get('complexity_score', 0):.3f}")
        
        financial_relevance = analysis.get("financial_relevance", {})
        print(f"💰 金融相关性:")
        print(f"   得分: {financial_relevance.get('score', 0):.3f}")
        print(f"   置信度: {financial_relevance.get('confidence', 'low')}")
        print(f"   类别: {financial_relevance.get('categories', [])}")
        
    else:
        print(f"❌ 分析失败: {analysis['error']}")

def demo_complete_workflow():
    """演示完整的工作流程"""
    print("\n" + "=" * 60)
    print("完整工作流程演示")
    print("=" * 60)
    
    # 1. 初始化系统
    print("1️⃣ 初始化多模态检索器...")
    retriever = MultimodalImageRetriever()
    if not retriever.initialize():
        print("❌ 检索器初始化失败")
        return
    
    # 2. 查找PDF文件
    pdf_dir = Path("texts_pdf")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到示例PDF文件")
        return
    
    pdf_file = pdf_files[0]
    print(f"2️⃣ 处理文件: {pdf_file.name}")
    
    # 3. 提取并索引图像
    print("3️⃣ 提取并索引图像...")
    import hashlib
    file_hash = hashlib.md5(str(pdf_file).encode()).hexdigest()[:16]
    
    success = retriever.index_images(str(pdf_file), file_hash)
    if not success:
        print("❌ 图像索引失败")
        return
    
    print("✅ 图像索引成功")
    
    # 4. 执行多种搜索测试
    print("4️⃣ 执行搜索测试...")
    
    # 文本搜索
    text_results = retriever.search_images_by_text("财务图表", top_k=2)
    print(f"   文本搜索找到 {len(text_results)} 个结果")
    
    # 5. 分析第一个图像
    if text_results:
        print("5️⃣ 分析图像内容...")
        image_id = text_results[0]["image_id"]
        analysis = retriever.analyze_image_content(image_id)
        
        if "error" not in analysis:
            print(f"   图像类型: {analysis.get('image_type', '未知')}")
            print(f"   金融相关性: {analysis.get('financial_relevance', {}).get('score', 0):.3f}")
    
    # 6. 显示统计信息
    print("6️⃣ 显示统计信息...")
    stats = retriever.get_image_statistics()
    print(f"   总图像数: {stats.get('total_images', 0)}")
    print(f"   图像类型: {len(stats.get('image_types', {}))}")
    
    print("\n✅ 完整工作流程演示完成！")

def main():
    """主函数"""
    print("🚀 多模态图像检索演示")
    print("本演示将展示CLIP和多模态大模型的图像检索功能")
    
    try:
        # 检查依赖
        try:
            import torch
            import clip
            from transformers import BlipProcessor
            print("✅ 依赖检查通过")
        except ImportError as e:
            print(f"❌ 缺少依赖: {e}")
            print("请运行: python install_multimodal_dependencies.py")
            return
        
        # 运行演示
        if not demo_model_initialization():
            return
        
        demo_image_extraction()
        demo_image_indexing()
        demo_text_to_image_search()
        demo_image_analysis()
        demo_complete_workflow()
        
        print("\n🎉 所有演示完成！")
        print("\n💡 接下来可以:")
        print("   1. 启动API服务器: python main.py --mode server")
        print("   2. 访问多模态API: http://localhost:8000/docs")
        print("   3. 使用API进行图像搜索和分析")
        
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")
        print(f"❌ 演示失败: {e}")

if __name__ == "__main__":
    main()
