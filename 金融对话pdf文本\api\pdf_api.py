"""
PDF处理API端点
提供PDF文件上传、处理、管理和检索的API接口
"""
import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Query
from pydantic import BaseModel
from loguru import logger

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.pdf_manager import PDFManager

# 创建路由器
router = APIRouter(prefix="/pdf", tags=["PDF管理"])

# 全局PDF管理器实例
pdf_manager = None

def get_pdf_manager():
    """获取PDF管理器实例"""
    global pdf_manager
    if pdf_manager is None:
        pdf_manager = PDFManager()
        if not pdf_manager.initialize():
            raise HTTPException(status_code=500, detail="PDF管理器初始化失败")
    return pdf_manager

# Pydantic模型
class PDFUploadResponse(BaseModel):
    success: bool
    message: str
    file_hash: Optional[str] = None

class PDFProcessResponse(BaseModel):
    success: bool
    message: str

class PDFInfo(BaseModel):
    file_hash: str
    name: str
    category: str
    description: str
    tags: List[str]
    upload_time: str
    processed: bool
    file_size: int
    chunk_count: int

class PDFListResponse(BaseModel):
    pdfs: List[PDFInfo]
    total_count: int

class SearchResult(BaseModel):
    content: str
    category: str
    source: str
    score: float
    file_info: Dict[str, Any]

class SearchResponse(BaseModel):
    results: List[SearchResult]
    query: str
    total_results: int

class StatisticsResponse(BaseModel):
    total_files: int
    processed_files: int
    unprocessed_files: int
    total_size_bytes: int
    total_size_mb: float
    category_distribution: Dict[str, int]
    storage_directory: str

@router.post("/upload", response_model=PDFUploadResponse)
async def upload_pdf(
    file: UploadFile = File(...),
    category: str = Form("金融文档"),
    description: str = Form(""),
    tags: str = Form("")  # 逗号分隔的标签字符串
):
    """
    上传PDF文件
    """
    try:
        # 检查文件类型
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")
        
        # 创建临时文件
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        temp_file_path = temp_dir / file.filename
        
        # 保存上传的文件
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 处理标签
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
        
        # 上传到PDF管理器
        manager = get_pdf_manager()
        result = manager.upload_pdf(
            str(temp_file_path),
            category=category,
            description=description,
            tags=tag_list
        )
        
        # 清理临时文件
        if temp_file_path.exists():
            temp_file_path.unlink()
        
        return PDFUploadResponse(**result)
        
    except Exception as e:
        logger.error(f"上传PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.post("/process/{file_hash}", response_model=PDFProcessResponse)
async def process_pdf(file_hash: str):
    """
    处理已上传的PDF文件
    """
    try:
        manager = get_pdf_manager()
        result = manager.process_uploaded_pdf(file_hash)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return PDFProcessResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.post("/upload-and-process", response_model=PDFProcessResponse)
async def upload_and_process_pdf(
    file: UploadFile = File(...),
    category: str = Form("金融文档"),
    description: str = Form(""),
    tags: str = Form("")
):
    """
    上传并处理PDF文件（一步完成）
    """
    try:
        # 检查文件类型
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")
        
        # 创建临时文件
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        temp_file_path = temp_dir / file.filename
        
        # 保存上传的文件
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 处理标签
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
        
        # 上传并处理
        manager = get_pdf_manager()
        result = manager.upload_and_process_pdf(
            str(temp_file_path),
            category=category,
            description=description,
            tags=tag_list
        )
        
        # 清理临时文件
        if temp_file_path.exists():
            temp_file_path.unlink()
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return PDFProcessResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传并处理PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")

@router.get("/list", response_model=PDFListResponse)
async def list_pdfs(
    category: Optional[str] = Query(None, description="筛选分类"),
    processed_only: bool = Query(False, description="只显示已处理的文件")
):
    """
    列出PDF文件
    """
    try:
        manager = get_pdf_manager()
        pdfs = manager.list_pdfs(category=category, processed_only=processed_only)
        
        pdf_infos = [PDFInfo(**pdf) for pdf in pdfs]
        
        return PDFListResponse(
            pdfs=pdf_infos,
            total_count=len(pdf_infos)
        )
        
    except Exception as e:
        logger.error(f"列出PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")

@router.delete("/delete/{file_hash}")
async def delete_pdf(file_hash: str):
    """
    删除PDF文件
    """
    try:
        manager = get_pdf_manager()
        result = manager.delete_pdf(file_hash)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return {"message": result["message"]}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/search", response_model=SearchResponse)
async def search_pdfs(
    query: str = Query(..., description="搜索查询"),
    top_k: int = Query(5, description="返回结果数量", ge=1, le=20)
):
    """
    在PDF知识库中搜索
    """
    try:
        manager = get_pdf_manager()
        results = manager.search_pdfs(query, top_k)
        
        search_results = [SearchResult(**result) for result in results]
        
        return SearchResponse(
            results=search_results,
            query=query,
            total_results=len(search_results)
        )
        
    except Exception as e:
        logger.error(f"搜索PDF知识库失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/statistics", response_model=StatisticsResponse)
async def get_statistics():
    """
    获取PDF管理统计信息
    """
    try:
        manager = get_pdf_manager()
        stats = manager.get_statistics()
        
        return StatisticsResponse(**stats)
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.post("/batch-process-directory")
async def batch_process_directory(
    directory_path: str = Form(...),
    category: str = Form("金融文档")
):
    """
    批量处理目录中的PDF文件
    """
    try:
        if not os.path.exists(directory_path):
            raise HTTPException(status_code=400, detail="目录不存在")
        
        manager = get_pdf_manager()
        
        # 批量上传和处理目录中的PDF文件
        pdf_files = []
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))
        
        if not pdf_files:
            raise HTTPException(status_code=400, detail="目录中没有PDF文件")
        
        results = {}
        for pdf_file in pdf_files:
            try:
                result = manager.upload_and_process_pdf(pdf_file, category)
                results[os.path.basename(pdf_file)] = result
            except Exception as e:
                logger.error(f"处理文件 {pdf_file} 失败: {e}")
                results[os.path.basename(pdf_file)] = {
                    "success": False,
                    "message": str(e)
                }
        
        # 统计结果
        success_count = sum(1 for r in results.values() if r.get("success", False))
        total_count = len(results)
        
        return {
            "message": f"批量处理完成: {success_count}/{total_count} 个文件成功",
            "results": results,
            "success_count": success_count,
            "total_count": total_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量处理目录失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量处理失败: {str(e)}")

# 健康检查端点
@router.get("/health")
async def health_check():
    """
    PDF服务健康检查
    """
    try:
        manager = get_pdf_manager()
        stats = manager.get_statistics()
        
        return {
            "status": "healthy",
            "pdf_manager": "initialized",
            "total_files": stats.get("total_files", 0)
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
