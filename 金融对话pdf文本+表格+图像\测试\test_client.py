"""
测试客户端
用于测试金融对话系统的API接口
"""
import requests
import json
import time

class FinancialChatClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
    
    def check_health(self):
        """检查系统健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ 系统状态: {data['status']}")
                print(f"✓ 可用模型: {data['available_models']}")
                return True
            else:
                print(f"✗ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def chat(self, message, model_provider="openai", model=None):
        """发送对话消息"""
        try:
            data = {
                "message": message,
                "model_provider": model_provider
            }

            if model:
                data["model"] = model

            if self.session_id:
                data["session_id"] = self.session_id
            
            response = requests.post(f"{self.base_url}/chat", json=data)
            
            if response.status_code == 200:
                result = response.json()
                self.session_id = result["session_id"]
                return result
            else:
                print(f"✗ 对话失败: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"✗ 对话请求失败: {e}")
            return None
    
    def add_knowledge(self, content, category, source=""):
        """添加知识"""
        try:
            data = {
                "content": content,
                "category": category,
                "source": source
            }
            
            response = requests.post(f"{self.base_url}/knowledge/add", json=data)
            
            if response.status_code == 200:
                print("✓ 知识添加成功")
                return True
            else:
                print(f"✗ 知识添加失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"✗ 添加知识失败: {e}")
            return False
    
    def search_knowledge(self, query, top_k=5):
        """搜索知识库"""
        try:
            params = {"query": query, "top_k": top_k}
            response = requests.get(f"{self.base_url}/knowledge/search", params=params)

            if response.status_code == 200:
                result = response.json()
                return result["results"]
            else:
                print(f"✗ 知识搜索失败: {response.status_code}")
                return []

        except Exception as e:
            print(f"✗ 搜索知识失败: {e}")
            return []

    def get_siliconflow_models(self):
        """获取硅基流动模型列表"""
        try:
            response = requests.get(f"{self.base_url}/models/siliconflow")

            if response.status_code == 200:
                result = response.json()
                return result["siliconflow_models"]
            else:
                print(f"✗ 获取硅基流动模型失败: {response.status_code}")
                return []

        except Exception as e:
            print(f"✗ 获取硅基流动模型失败: {e}")
            return []

    def get_model_info(self):
        """获取所有模型的详细信息"""
        try:
            response = requests.get(f"{self.base_url}/models/info")

            if response.status_code == 200:
                result = response.json()
                return result["model_info"]
            else:
                print(f"✗ 获取模型信息失败: {response.status_code}")
                return {}

        except Exception as e:
            print(f"✗ 获取模型信息失败: {e}")
            return {}

def run_interactive_test():
    """运行交互式测试"""
    client = FinancialChatClient()
    
    print("=" * 50)
    print("金融对话系统测试客户端")
    print("=" * 50)
    
    # 检查系统状态
    if not client.check_health():
        print("系统不可用，请检查服务是否启动")
        return
    
    print("\n可用命令:")
    print("  chat <消息>                    - 发送对话消息")
    print("  chat <消息> --model <提供商>    - 使用指定模型提供商")
    print("  chat <消息> --sf <模型名>       - 使用硅基流动指定模型")
    print("  add <内容>                     - 添加知识")
    print("  search <查询>                  - 搜索知识")
    print("  models                        - 查看可用模型")
    print("  model-info                    - 查看所有模型详细信息")
    print("  sf-models                     - 查看硅基流动模型")
    print("  test                          - 运行自动测试")
    print("  quit                          - 退出")
    print("\n支持的模型提供商:")
    print("  openai, zhipu, qwen, siliconflow, baidu, moonshot,")
    print("  deepseek, doubao, claude, gemini")
    
    while True:
        try:
            command = input("\n> ").strip()
            
            if not command:
                continue
            
            if command == "quit":
                break
            
            elif command.startswith("chat "):
                # 解析命令参数
                parts = command.split()
                if len(parts) < 2:
                    print("请提供消息内容")
                    continue

                model_provider = "openai"
                model = None

                # 检查是否指定了模型提供商
                if "--model" in parts:
                    model_idx = parts.index("--model")
                    if model_idx + 1 < len(parts):
                        model_provider = parts[model_idx + 1]
                        # 移除模型参数
                        parts = parts[:model_idx] + parts[model_idx + 2:]

                # 检查是否指定了硅基流动模型
                elif "--sf" in parts:
                    model_idx = parts.index("--sf")
                    if model_idx + 1 < len(parts):
                        model_provider = "siliconflow"
                        model = parts[model_idx + 1]
                        # 移除模型参数
                        parts = parts[:model_idx] + parts[model_idx + 2:]

                # 重新组合消息
                message = " ".join(parts[1:])

                print(f"\n用户: {message}")
                if model:
                    print(f"使用模型: {model_provider} - {model}")
                else:
                    print(f"使用模型: {model_provider}")

                result = client.chat(message, model_provider, model)
                if result:
                    print(f"助手: {result['response']}")
                    print(f"会话ID: {result['session_id']}")
                    print(f"使用上下文: 知识{result['context_used']['knowledge_count']}条, 历史{result['context_used']['history_count']}条")
            
            elif command.startswith("add "):
                content = command[4:]
                category = input("分类: ").strip() or "general"
                source = input("来源: ").strip()
                client.add_knowledge(content, category, source)
            
            elif command.startswith("search "):
                query = command[7:]
                results = client.search_knowledge(query)
                print(f"\n找到 {len(results)} 条相关知识:")
                for i, item in enumerate(results, 1):
                    print(f"{i}. {item['content'][:100]}...")
                    print(f"   分类: {item['category']}, 相似度: {item['score']:.3f}")
            
            elif command == "models":
                print("\n可用模型提供商:")
                result = client.check_health()
                if result:
                    print("请使用 'chat <消息> --model <提供商>' 来指定模型")
                    print("或使用 'model-info' 查看详细信息")

            elif command == "model-info":
                print("\n所有模型详细信息:")
                model_info = client.get_model_info()
                if model_info:
                    for provider, info in model_info.items():
                        status = "✓ 可用" if info["available"] else "✗ 不可用"
                        print(f"\n{provider.upper()} - {info['name']} {status}")
                        print(f"  描述: {info['description']}")
                        if 'default_model' in info:
                            print(f"  默认模型: {info['default_model']}")
                        print(f"  支持模型: {', '.join(info['models'][:3])}{'...' if len(info['models']) > 3 else ''}")
                else:
                    print("无法获取模型信息")

            elif command == "sf-models":
                print("\n硅基流动可用模型:")
                models = client.get_siliconflow_models()
                if models:
                    for i, model in enumerate(models, 1):
                        print(f"{i:2d}. {model}")
                    print("\n使用方法: chat <消息> --sf <模型名>")
                else:
                    print("无法获取硅基流动模型列表")

            elif command == "test":
                run_auto_test(client)

            else:
                print("未知命令，请重试")
                
        except KeyboardInterrupt:
            print("\n\n再见!")
            break
        except Exception as e:
            print(f"错误: {e}")

def run_auto_test(client):
    """运行自动测试"""
    print("\n" + "=" * 30)
    print("运行自动测试")
    print("=" * 30)
    
    # 测试对话
    test_questions = [
        "什么是股票？",
        "请解释市盈率的含义",
        "债券和股票有什么区别？",
        "什么是基金？"
    ]
    
    print("\n1. 测试对话功能:")
    for question in test_questions:
        print(f"\n问题: {question}")
        result = client.chat(question)
        if result:
            print(f"回复: {result['response'][:100]}...")
        time.sleep(1)
    
    # 测试知识搜索
    print("\n2. 测试知识搜索:")
    search_queries = ["股票", "债券", "基金"]
    for query in search_queries:
        print(f"\n搜索: {query}")
        results = client.search_knowledge(query, 3)
        print(f"找到 {len(results)} 条结果")
        for item in results[:2]:
            print(f"  - {item['content'][:50]}... (相似度: {item['score']:.3f})")
    
    print("\n测试完成!")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "auto":
        # 自动测试模式
        client = FinancialChatClient()
        if client.check_health():
            run_auto_test(client)
    else:
        # 交互模式
        run_interactive_test()

if __name__ == "__main__":
    main()
