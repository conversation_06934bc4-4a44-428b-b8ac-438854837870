"""
PDF表格检索优化演示
展示如何使用表格检索优化功能处理金融文档中的表格数据
"""
import os
import sys
import time
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.pdf_processor import PDFProcessor
from rag.table_retrieval_optimizer import TableRetrievalOptimizer
from rag.pdf_manager import PDFManager
from loguru import logger

def demo_table_extraction():
    """演示表格提取功能"""
    print("=" * 60)
    print("PDF表格提取演示")
    print("=" * 60)
    
    # 初始化处理器
    processor = PDFProcessor()
    if not processor.initialize():
        print("❌ PDF处理器初始化失败")
        return
    
    # 查找示例PDF文件
    pdf_dir = Path("texts_pdf")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到示例PDF文件")
        print("请将PDF文件放在 texts_pdf/ 目录下")
        return
    
    # 使用第一个PDF文件进行演示
    pdf_file = pdf_files[0]
    print(f"📄 处理文件: {pdf_file.name}")
    
    # 提取表格
    print("\n🔍 提取表格中...")
    tables = processor.extract_table_only(str(pdf_file))
    
    if tables:
        print(f"✅ 成功提取 {len(tables)} 个表格")
        
        for i, table_info in enumerate(tables[:3], 1):  # 只显示前3个表格
            print(f"\n📊 表格 {i}:")
            print(f"   页码: {table_info['page_number']}")
            print(f"   类型: {table_info.get('type', '未知')}")
            print(f"   内容预览: {table_info['content'][:200]}...")
    else:
        print("❌ 未找到表格")

def demo_table_indexing():
    """演示表格索引功能"""
    print("\n" + "=" * 60)
    print("表格索引演示")
    print("=" * 60)
    
    # 初始化优化器
    optimizer = TableRetrievalOptimizer()
    if not optimizer.initialize():
        print("❌ 表格优化器初始化失败")
        return
    
    # 查找示例PDF文件
    pdf_dir = Path("texts_pdf")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到示例PDF文件")
        return
    
    # 为每个PDF文件建立索引
    for pdf_file in pdf_files[:2]:  # 只处理前2个文件
        print(f"\n📄 索引文件: {pdf_file.name}")
        
        # 生成文件哈希（简化版）
        import hashlib
        file_hash = hashlib.md5(str(pdf_file).encode()).hexdigest()[:16]
        
        # 提取并索引表格
        success = optimizer.extract_and_index_tables(str(pdf_file), file_hash)
        
        if success:
            print(f"✅ 索引成功")
        else:
            print(f"❌ 索引失败")
    
    # 显示索引统计
    stats = optimizer.get_table_statistics()
    print(f"\n📈 索引统计:")
    print(f"   总文件数: {stats.get('total_files', 0)}")
    print(f"   总表格数: {stats.get('total_tables', 0)}")
    print(f"   表格类型: {stats.get('table_types', {})}")

def demo_table_search():
    """演示表格搜索功能"""
    print("\n" + "=" * 60)
    print("表格搜索演示")
    print("=" * 60)
    
    # 初始化优化器
    optimizer = TableRetrievalOptimizer()
    if not optimizer.initialize():
        print("❌ 表格优化器初始化失败")
        return
    
    # 测试查询
    test_queries = [
        "营收",
        "利润",
        "资产负债",
        "现金流",
        "市场数据"
    ]
    
    for query in test_queries:
        print(f"\n🔍 搜索: '{query}'")
        
        start_time = time.time()
        results = optimizer.search_tables(query, top_k=3)
        search_time = (time.time() - start_time) * 1000
        
        print(f"⏱️  搜索耗时: {search_time:.2f}ms")
        print(f"📊 找到 {len(results)} 个相关表格")
        
        for i, result in enumerate(results, 1):
            print(f"\n   结果 {i}:")
            print(f"   文件: {result.get('file_name', '未知')}")
            print(f"   页码: {result.get('page_number', 0)}")
            print(f"   类型: {result.get('table_type', '未知')}")
            print(f"   相似度: {result.get('similarity', 0):.3f}")
            print(f"   匹配内容: {result.get('matched_content', [])[:2]}")

def demo_optimized_search():
    """演示优化搜索功能"""
    print("\n" + "=" * 60)
    print("优化搜索演示")
    print("=" * 60)
    
    # 初始化优化器
    optimizer = TableRetrievalOptimizer()
    if not optimizer.initialize():
        print("❌ 表格优化器初始化失败")
        return
    
    # 模拟上下文信息
    context = {
        "history": [
            {
                "user_query": "公司财务状况如何？",
                "assistant_response": "根据财务报表显示..."
            }
        ],
        "knowledge": [
            {
                "content": "财务分析需要关注营收、利润、资产负债等关键指标",
                "category": "财务分析"
            }
        ]
    }
    
    query = "财务指标"
    print(f"🔍 优化搜索: '{query}'")
    
    # 执行优化搜索
    results = optimizer.optimize_table_search(query, context)
    
    print(f"📊 找到 {len(results)} 个优化结果")
    
    for i, result in enumerate(results, 1):
        print(f"\n   结果 {i}:")
        print(f"   文件: {result.get('file_name', '未知')}")
        print(f"   摘要: {result.get('summary', '无摘要')}")
        print(f"   相关性解释: {result.get('relevance_explanation', '无解释')}")
        print(f"   建议查询: {result.get('suggested_queries', [])}")

def demo_complete_workflow():
    """演示完整的工作流程"""
    print("\n" + "=" * 60)
    print("完整工作流程演示")
    print("=" * 60)
    
    # 1. 初始化管理器
    print("1️⃣ 初始化PDF管理器...")
    manager = PDFManager()
    if not manager.initialize():
        print("❌ PDF管理器初始化失败")
        return
    
    # 2. 初始化表格优化器
    print("2️⃣ 初始化表格优化器...")
    optimizer = TableRetrievalOptimizer()
    if not optimizer.initialize():
        print("❌ 表格优化器初始化失败")
        return
    
    # 3. 查找PDF文件
    pdf_dir = Path("texts_pdf")
    pdf_files = list(pdf_dir.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 未找到示例PDF文件")
        return
    
    pdf_file = pdf_files[0]
    print(f"3️⃣ 处理文件: {pdf_file.name}")
    
    # 4. 上传并处理PDF
    print("4️⃣ 上传并处理PDF...")
    result = manager.upload_and_process_pdf(
        str(pdf_file),
        category="金融报告",
        description="演示用财务报告",
        tags=["演示", "财务", "报告"]
    )
    
    if not result["success"]:
        print(f"❌ PDF处理失败: {result['message']}")
        return
    
    file_hash = result["file_hash"]
    print(f"✅ PDF处理成功，文件哈希: {file_hash}")
    
    # 5. 提取并索引表格
    print("5️⃣ 提取并索引表格...")
    table_success = optimizer.extract_and_index_tables(str(pdf_file), file_hash)
    
    if table_success:
        print("✅ 表格索引成功")
    else:
        print("❌ 表格索引失败")
    
    # 6. 执行搜索测试
    print("6️⃣ 执行搜索测试...")
    test_query = "财务数据"
    results = optimizer.optimize_table_search(test_query)
    
    print(f"🔍 搜索 '{test_query}' 找到 {len(results)} 个结果")
    
    # 7. 显示统计信息
    print("7️⃣ 显示统计信息...")
    pdf_stats = manager.get_statistics()
    table_stats = optimizer.get_table_statistics()
    
    print(f"📊 PDF统计: {pdf_stats.get('total_files', 0)} 个文件")
    print(f"📊 表格统计: {table_stats.get('total_tables', 0)} 个表格")
    
    print("\n✅ 完整工作流程演示完成！")

def main():
    """主函数"""
    print("🚀 PDF表格检索优化演示")
    print("本演示将展示表格提取、索引、搜索和优化功能")
    
    try:
        # 检查依赖
        try:
            import pandas as pd
            import pdfplumber
            print("✅ 依赖检查通过")
        except ImportError as e:
            print(f"❌ 缺少依赖: {e}")
            print("请安装: pip install pandas pdfplumber pymupdf scikit-learn")
            return
        
        # 运行演示
        demo_table_extraction()
        demo_table_indexing()
        demo_table_search()
        demo_optimized_search()
        demo_complete_workflow()
        
        print("\n🎉 所有演示完成！")
        
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")
        print(f"❌ 演示失败: {e}")

if __name__ == "__main__":
    main()
