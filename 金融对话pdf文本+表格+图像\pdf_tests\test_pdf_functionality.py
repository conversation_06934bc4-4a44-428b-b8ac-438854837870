"""
PDF功能测试脚本
测试PDF处理、管理和检索功能
"""
import sys
import os
import tempfile
from pathlib import Path
from loguru import logger

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

def test_imports():
    """测试模块导入"""
    logger.info("测试模块导入...")
    
    try:
        from rag.pdf_processor import PDFProcessor
        from rag.pdf_manager import PDFManager
        from rag.rag_system import RAGSystem
        logger.info("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        logger.error(f"✗ 模块导入失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    logger.info("测试依赖包...")
    
    dependencies = [
        "langchain",
        "langchain_community", 
        "langchain_text_splitters",
        "pypdf",
        "sentence_transformers",
        "pymilvus"
    ]
    
    missing_deps = []
    for dep in dependencies:
        try:
            __import__(dep)
            logger.info(f"✓ {dep} 可用")
        except ImportError:
            logger.error(f"✗ {dep} 缺失")
            missing_deps.append(dep)
    
    if missing_deps:
        logger.error(f"缺失依赖: {missing_deps}")
        logger.info("请运行: pip install -r idconfig/requirements.txt")
        return False
    
    logger.info("✓ 所有依赖包可用")
    return True

def test_rag_system_initialization():
    """测试RAG系统初始化"""
    logger.info("测试RAG系统初始化...")
    
    try:
        from rag.rag_system import RAGSystem
        
        rag_system = RAGSystem()
        
        # 测试初始化（可能会因为Milvus连接失败）
        try:
            success = rag_system.initialize()
            if success:
                logger.info("✓ RAG系统初始化成功")
                return True
            else:
                logger.warning("⚠ RAG系统初始化失败（可能是Milvus连接问题）")
                return False
        except Exception as e:
            logger.warning(f"⚠ RAG系统初始化异常: {e}")
            return False
            
    except Exception as e:
        logger.error(f"✗ RAG系统测试失败: {e}")
        return False

def test_pdf_processor_creation():
    """测试PDF处理器创建"""
    logger.info("测试PDF处理器创建...")
    
    try:
        from rag.pdf_processor import PDFProcessor
        
        processor = PDFProcessor()
        logger.info("✓ PDF处理器创建成功")
        
        # 测试配置
        logger.info(f"  - 文本块大小: {processor.chunk_size}")
        logger.info(f"  - 重叠大小: {processor.chunk_overlap}")
        logger.info(f"  - 分割器类型: {type(processor.text_splitter).__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ PDF处理器创建失败: {e}")
        return False

def test_pdf_manager_creation():
    """测试PDF管理器创建"""
    logger.info("测试PDF管理器创建...")
    
    try:
        from rag.pdf_manager import PDFManager
        
        manager = PDFManager()
        logger.info("✓ PDF管理器创建成功")
        
        # 检查存储目录
        logger.info(f"  - PDF存储目录: {manager.pdf_storage_dir}")
        logger.info(f"  - 元数据文件: {manager.metadata_file}")
        
        # 检查目录是否创建
        if manager.pdf_storage_dir.exists():
            logger.info("  - 存储目录已创建")
        else:
            logger.warning("  - 存储目录未创建")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ PDF管理器创建失败: {e}")
        return False

def test_text_splitter():
    """测试文本分割器"""
    logger.info("测试文本分割器...")
    
    try:
        from rag.pdf_processor import PDFProcessor
        
        processor = PDFProcessor()
        
        # 测试文本
        test_text = """
        这是一个测试文档。它包含多个段落和句子。
        
        第二段落开始了。这里有更多的内容需要处理。我们需要确保文本分割器能够正确工作。
        
        第三段落包含了一些金融术语。股票是一种有价证券。债券是另一种投资工具。
        基金可以帮助分散投资风险。
        
        最后一段包含了更多信息。市盈率是重要的估值指标。净资产收益率反映公司盈利能力。
        """
        
        # 创建模拟文档
        from langchain.schema import Document
        doc = Document(page_content=test_text)
        
        # 分割文档
        split_docs = processor.split_documents([doc])
        
        logger.info(f"✓ 文本分割成功: 1个文档 -> {len(split_docs)}个块")
        
        for i, split_doc in enumerate(split_docs):
            logger.info(f"  块 {i+1}: {len(split_doc.page_content)} 字符")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 文本分割器测试失败: {e}")
        return False

def create_test_pdf():
    """创建测试PDF文件"""
    logger.info("创建测试PDF文件...")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        
        # 创建临时PDF文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()
        
        # 创建PDF内容
        c = canvas.Canvas(temp_file.name, pagesize=letter)
        
        # 添加文本内容
        c.drawString(100, 750, "Financial Document Test")
        c.drawString(100, 720, "This is a test financial document.")
        c.drawString(100, 690, "It contains information about stocks, bonds, and funds.")
        c.drawString(100, 660, "Stock is a type of security that represents ownership.")
        c.drawString(100, 630, "Bond is a debt security issued by corporations or governments.")
        c.drawString(100, 600, "Fund is a pool of money from many investors.")
        
        c.save()
        
        logger.info(f"✓ 测试PDF文件创建成功: {temp_file.name}")
        return temp_file.name
        
    except ImportError:
        logger.warning("⚠ reportlab未安装，无法创建测试PDF")
        return None
    except Exception as e:
        logger.error(f"✗ 创建测试PDF失败: {e}")
        return None

def test_pdf_loading():
    """测试PDF加载功能"""
    logger.info("测试PDF加载功能...")
    
    # 创建测试PDF
    test_pdf = create_test_pdf()
    if not test_pdf:
        logger.warning("⚠ 跳过PDF加载测试（无测试文件）")
        return False
    
    try:
        from rag.pdf_processor import PDFProcessor
        
        processor = PDFProcessor()
        
        # 测试PyPDF加载
        logger.info("测试PyPDF加载器...")
        docs_pypdf = processor.load_pdf_with_pypdf(test_pdf)
        logger.info(f"  PyPDF加载结果: {len(docs_pypdf)} 个文档")
        
        # 测试Unstructured加载
        logger.info("测试Unstructured加载器...")
        try:
            docs_unstructured = processor.load_pdf_with_unstructured(test_pdf)
            logger.info(f"  Unstructured加载结果: {len(docs_unstructured)} 个文档")
        except Exception as e:
            logger.warning(f"  Unstructured加载失败: {e}")
        
        # 清理测试文件
        os.unlink(test_pdf)
        
        if docs_pypdf:
            logger.info("✓ PDF加载测试成功")
            return True
        else:
            logger.error("✗ PDF加载测试失败")
            return False
            
    except Exception as e:
        logger.error(f"✗ PDF加载测试异常: {e}")
        # 清理测试文件
        if test_pdf and os.path.exists(test_pdf):
            os.unlink(test_pdf)
        return False

def test_api_imports():
    """测试API模块导入"""
    logger.info("测试API模块导入...")
    
    try:
        from api.pdf_api import router
        from fastapi import APIRouter
        
        if isinstance(router, APIRouter):
            logger.info("✓ PDF API路由器导入成功")
            return True
        else:
            logger.error("✗ PDF API路由器类型错误")
            return False
            
    except Exception as e:
        logger.error(f"✗ API模块导入失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    logger.info("开始PDF功能测试")
    logger.info("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("依赖包检查", test_dependencies),
        ("RAG系统初始化", test_rag_system_initialization),
        ("PDF处理器创建", test_pdf_processor_creation),
        ("PDF管理器创建", test_pdf_manager_creation),
        ("文本分割器", test_text_splitter),
        ("PDF加载功能", test_pdf_loading),
        ("API模块导入", test_api_imports),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            logger.error(f"测试 {test_name} 出现异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    logger.info("\n" + "=" * 50)
    logger.info("测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ 通过" if result else "✗ 失败"
        logger.info(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！PDF功能可以正常使用。")
    elif passed > total // 2:
        logger.warning("⚠ 部分测试失败，但核心功能可用。")
    else:
        logger.error("❌ 多个测试失败，请检查配置和依赖。")
    
    return results

if __name__ == "__main__":
    run_all_tests()
