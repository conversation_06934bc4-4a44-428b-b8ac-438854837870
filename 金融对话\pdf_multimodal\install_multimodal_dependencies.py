"""
安装多模态图像检索依赖
自动安装CLIP和多模态大模型所需的Python包
"""
import subprocess
import sys
import os
import platform
from pathlib import Path

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}...")
        if description:
            print(f"   用途: {description}")
        
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        
        print(f"✅ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def check_gpu_support():
    """检查GPU支持"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            return True, gpu_count, gpu_name
        else:
            return False, 0, "No GPU"
    except:
        return False, 0, "PyTorch not installed"

def install_pytorch():
    """安装PyTorch"""
    print("🔥 安装PyTorch...")
    
    # 检查系统和CUDA支持
    system = platform.system().lower()
    
    try:
        # 尝试检测CUDA版本
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
        has_cuda = result.returncode == 0
    except:
        has_cuda = False
    
    if has_cuda:
        print("🎮 检测到NVIDIA GPU，安装CUDA版本的PyTorch...")
        torch_package = "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    else:
        print("💻 未检测到CUDA，安装CPU版本的PyTorch...")
        torch_package = "torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    
    return install_package(torch_package, "深度学习框架，用于运行CLIP和多模态模型")

def main():
    """主函数"""
    print("🚀 多模态图像检索依赖安装程序")
    print("=" * 60)
    
    # 定义需要安装的包
    packages = [
        {
            "name": "torch",
            "import_name": "torch",
            "description": "PyTorch深度学习框架",
            "custom_install": install_pytorch
        },
        {
            "name": "transformers>=4.21.0",
            "import_name": "transformers",
            "description": "HuggingFace Transformers，用于BLIP和CLIP模型"
        },
        {
            "name": "clip-by-openai",
            "import_name": "clip",
            "description": "OpenAI CLIP模型，用于图像-文本匹配"
        },
        {
            "name": "pillow>=9.0.0",
            "import_name": "PIL",
            "description": "Python图像处理库"
        },
        {
            "name": "opencv-python>=4.5.0",
            "import_name": "cv2",
            "description": "计算机视觉库，用于图像处理"
        },
        {
            "name": "numpy>=1.21.0",
            "import_name": "numpy",
            "description": "数值计算库"
        },
        {
            "name": "scikit-learn>=1.0.0",
            "import_name": "sklearn",
            "description": "机器学习库，用于相似度计算"
        },
        {
            "name": "pymupdf>=1.23.0",
            "import_name": "fitz",
            "description": "PDF处理库，用于提取图像"
        },
        {
            "name": "pytesseract>=0.3.10",
            "import_name": "pytesseract",
            "description": "OCR文字识别库（可选）"
        },
        {
            "name": "matplotlib>=3.5.0",
            "import_name": "matplotlib",
            "description": "数据可视化库（可选）"
        }
    ]
    
    # 检查已安装的包
    print("🔍 检查已安装的包...")
    installed_packages = []
    missing_packages = []
    
    for package in packages:
        if check_package(package["import_name"]):
            print(f"✅ {package['name'].split('>=')[0]} 已安装")
            installed_packages.append(package)
        else:
            print(f"❌ {package['name'].split('>=')[0]} 未安装")
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n🎉 所有依赖都已安装！")
        # 检查GPU支持
        has_gpu, gpu_count, gpu_name = check_gpu_support()
        if has_gpu:
            print(f"🎮 GPU支持: {gpu_count} 个GPU ({gpu_name})")
        else:
            print("💻 运行在CPU模式")
        return
    
    print(f"\n📋 需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"   - {package['name']}: {package['description']}")
    
    # 询问是否继续安装
    response = input("\n❓ 是否继续安装？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("❌ 安装已取消")
        return
    
    # 升级pip
    print("\n🔧 升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip升级成功")
    except:
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装缺失的包
    print("\n📦 开始安装依赖包...")
    success_count = 0
    
    for package in missing_packages:
        try:
            if "custom_install" in package:
                # 使用自定义安装方法
                if package["custom_install"]():
                    success_count += 1
            else:
                # 使用标准安装方法
                if install_package(package["name"], package["description"]):
                    success_count += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"❌ 安装 {package['name']} 时出错: {e}")
    
    # 安装结果总结
    print("=" * 60)
    print("📊 安装结果总结:")
    print(f"   成功安装: {success_count}/{len(missing_packages)} 个包")
    print(f"   已有包数: {len(installed_packages)} 个")
    
    if success_count == len(missing_packages):
        print("\n🎉 所有依赖安装完成！")
        
        # 检查GPU支持
        has_gpu, gpu_count, gpu_name = check_gpu_support()
        if has_gpu:
            print(f"🎮 GPU支持: {gpu_count} 个GPU ({gpu_name})")
            print("   建议使用GPU加速多模态模型推理")
        else:
            print("💻 运行在CPU模式")
            print("   注意：CPU模式下模型推理速度较慢")
        
        print("\n📝 接下来可以:")
        print("   1. 运行多模态演示: python examples/multimodal_demo.py")
        print("   2. 启动API服务器: python main.py --mode server")
        print("   3. 使用多模态API进行开发")
        
        # 下载模型提示
        print("\n💡 首次使用时会自动下载模型:")
        print("   - CLIP模型 (~600MB)")
        print("   - BLIP模型 (~1GB)")
        print("   请确保网络连接良好")
        
    else:
        print(f"\n⚠️  有 {len(missing_packages) - success_count} 个包安装失败")
        print("请检查网络连接或手动安装失败的包")
    
    # 验证安装
    print("\n🔍 验证安装...")
    verification_failed = []
    
    for package in missing_packages:
        if check_package(package["import_name"]):
            print(f"✅ {package['name'].split('>=')[0]} 验证成功")
        else:
            print(f"❌ {package['name'].split('>=')[0]} 验证失败")
            verification_failed.append(package)
    
    if verification_failed:
        print(f"\n⚠️  {len(verification_failed)} 个包验证失败:")
        for package in verification_failed:
            print(f"   - {package['name']}")
        print("\n💡 解决方案:")
        print("   1. 检查Python环境是否正确")
        print("   2. 尝试手动安装: pip install <package_name>")
        print("   3. 检查是否有权限问题")
        print("   4. 对于PyTorch，可能需要重启Python环境")
    else:
        print("\n✅ 所有包验证通过！")

def create_requirements_file():
    """创建requirements文件"""
    requirements_content = """# 多模态图像检索依赖
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0
transformers>=4.21.0
clip-by-openai
pillow>=9.0.0
opencv-python>=4.5.0
numpy>=1.21.0
scikit-learn>=1.0.0
pymupdf>=1.23.0

# 可选依赖
pytesseract>=0.3.10  # OCR文字识别
matplotlib>=3.5.0    # 数据可视化
seaborn>=0.11.0      # 统计可视化
plotly>=5.0.0        # 交互式图表

# GPU加速（如果有NVIDIA GPU）
# torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
"""
    
    requirements_file = Path("requirements_multimodal.txt")
    with open(requirements_file, 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    print(f"📄 已创建依赖文件: {requirements_file}")
    print("   可以使用: pip install -r requirements_multimodal.txt")

if __name__ == "__main__":
    try:
        main()
        
        # 询问是否创建requirements文件
        response = input("\n❓ 是否创建requirements_multimodal.txt文件？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            create_requirements_file()
        
    except KeyboardInterrupt:
        print("\n❌ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出错: {e}")
        print("请检查Python环境和网络连接")
