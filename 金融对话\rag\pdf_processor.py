"""
PDF文档处理模块
使用LangChain框架处理PDF文件，提取文本并分块存储到Milvus知识库
"""
import os
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger

# LangChain相关导入
try:
    from langchain_community.document_loaders import PyPDFLoader, UnstructuredPDFLoader
    from langchain_text_splitters import RecursiveCharacterTextSplitter, CharacterTextSplitter
    from langchain_core.documents import Document
except ImportError:
    # 兼容旧版本
    try:
        from langchain.document_loaders import PyPDFLoader, UnstructuredPDFLoader
    except ImportError:
        from langchain.document_loaders import PyPDFLoader
        UnstructuredPDFLoader = None
    from langchain.text_splitter import RecursiveCharacterTextSplitter, CharacterTextSplitter
    from langchain.schema import Document

# 表格解析相关导入
try:
    import pdfplumber
    import pandas as pd
    import fitz  # PyMuPDF
    ADVANCED_PDF_AVAILABLE = True
except ImportError:
    ADVANCED_PDF_AVAILABLE = False
    logger.warning("高级PDF处理库未安装，表格解析功能不可用")

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.rag_system import RAGSystem

# 多模态图像处理导入
try:
    from rag.multimodal_retrieval import MultimodalImageRetriever
    from rag.fast_image_extractor import FastImageExtractor
    MULTIMODAL_AVAILABLE = True
    FAST_EXTRACTION_AVAILABLE = True
except ImportError:
    MULTIMODAL_AVAILABLE = False
    FAST_EXTRACTION_AVAILABLE = False
    logger.warning("多模态图像功能不可用，请安装相关依赖")

# 去重功能导入
try:
    from utils.dedup_manager import DeduplicationManager
    DEDUPLICATION_AVAILABLE = True
except ImportError:
    DEDUPLICATION_AVAILABLE = False
    logger.warning("去重功能不可用")

from idconfig.config import Config

class PDFProcessor:
    """PDF文档处理器"""
    
    def __init__(self):
        self.config = Config()
        self.rag_system = RAGSystem()

        # 文本分割器配置
        self.chunk_size = 1000  # 每个文本块的字符数
        self.chunk_overlap = 200  # 文本块之间的重叠字符数

        # 表格处理配置
        self.table_chunk_size = 2000  # 表格内容的块大小
        self.enable_table_extraction = ADVANCED_PDF_AVAILABLE

        # 图像处理配置
        self.enable_image_extraction = MULTIMODAL_AVAILABLE
        self.enable_fast_extraction = FAST_EXTRACTION_AVAILABLE
        self.multimodal_retriever = None
        self.fast_extractor = None

        # 初始化文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", "。", "！", "？", "；", "，", " ", ""]
        )

        # 表格专用分割器
        self.table_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.table_chunk_size,
            chunk_overlap=100,
            length_function=len,
            separators=["\n\n", "\n", "|", " ", ""]
        )

        # 去重管理器配置
        self.enable_deduplication = DEDUPLICATION_AVAILABLE
        if self.enable_deduplication:
            self.dedup_manager = DeduplicationManager()
            logger.info("PDF处理器：去重功能已启用")
        else:
            self.dedup_manager = None
            logger.info("PDF处理器：去重功能未启用")
        
    def initialize(self):
        """初始化PDF处理器"""
        try:
            # 初始化RAG系统
            if not self.rag_system.initialize():
                logger.error("RAG系统初始化失败")
                return False

            # 初始化图像提取器（优先使用快速提取器）
            if self.enable_fast_extraction:
                try:
                    self.fast_extractor = FastImageExtractor()
                    logger.info("快速图像提取器初始化成功")
                except Exception as e:
                    logger.warning(f"快速图像提取器初始化失败: {e}")
                    self.enable_fast_extraction = False
                    self.fast_extractor = None

            # 初始化多模态检索器（用于图像描述生成，无论是否有快速提取器）
            if self.enable_image_extraction:
                try:
                    self.multimodal_retriever = MultimodalImageRetriever()
                    if self.multimodal_retriever.initialize():
                        logger.info("多模态图像检索器初始化成功")
                    else:
                        logger.warning("多模态图像检索器初始化失败，将跳过图像提取")
                        self.enable_image_extraction = False
                        self.multimodal_retriever = None
                except Exception as e:
                    logger.warning(f"多模态检索器初始化失败: {e}")
                    self.enable_image_extraction = False
                    self.multimodal_retriever = None

            logger.info("PDF处理器初始化完成")
            return True

        except Exception as e:
            logger.error(f"PDF处理器初始化失败: {e}")
            return False
    
    def load_pdf_with_pypdf(self, file_path: str) -> List[Document]:
        """使用PyPDFLoader加载PDF文件"""
        try:
            loader = PyPDFLoader(file_path)
            documents = loader.load()
            logger.info(f"使用PyPDFLoader成功加载PDF: {file_path}, 页数: {len(documents)}")
            return documents
        except Exception as e:
            logger.error(f"PyPDFLoader加载PDF失败: {e}")
            return []
    
    def load_pdf_with_unstructured(self, file_path: str) -> List[Document]:
        """使用UnstructuredPDFLoader加载PDF文件"""
        try:
            if UnstructuredPDFLoader is None:
                logger.warning("UnstructuredPDFLoader不可用，回退到PyPDFLoader")
                return self.load_pdf_with_pypdf(file_path)

            loader = UnstructuredPDFLoader(file_path)
            documents = loader.load()
            logger.info(f"使用UnstructuredPDFLoader成功加载PDF: {file_path}, 文档数: {len(documents)}")
            return documents
        except Exception as e:
            logger.error(f"UnstructuredPDFLoader加载PDF失败: {e}")
            return []
    
    def load_pdf(self, file_path: str, loader_type: str = "pypdf") -> List[Document]:
        """
        加载PDF文件
        
        Args:
            file_path: PDF文件路径
            loader_type: 加载器类型 ("pypdf" 或 "unstructured")
        
        Returns:
            文档列表
        """
        if not os.path.exists(file_path):
            logger.error(f"PDF文件不存在: {file_path}")
            return []
        
        if not file_path.lower().endswith('.pdf'):
            logger.error(f"文件不是PDF格式: {file_path}")
            return []
        
        logger.info(f"开始加载PDF文件: {file_path}")
        
        if loader_type == "pypdf":
            documents = self.load_pdf_with_pypdf(file_path)
        elif loader_type == "unstructured":
            documents = self.load_pdf_with_unstructured(file_path)
        else:
            logger.error(f"不支持的加载器类型: {loader_type}")
            return []
        
        # 如果第一种方法失败，尝试另一种方法
        if not documents and loader_type == "pypdf":
            logger.warning("PyPDFLoader失败，尝试使用UnstructuredPDFLoader")
            documents = self.load_pdf_with_unstructured(file_path)
        elif not documents and loader_type == "unstructured":
            logger.warning("UnstructuredPDFLoader失败，尝试使用PyPDFLoader")
            documents = self.load_pdf_with_pypdf(file_path)
        
        return documents

    def extract_tables_with_pdfplumber(self, file_path: str) -> List[Dict[str, Any]]:
        """使用pdfplumber提取PDF中的表格"""
        try:
            if not ADVANCED_PDF_AVAILABLE:
                logger.warning("pdfplumber未安装，无法提取表格")
                return []

            tables_data = []

            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # 提取表格
                    tables = page.extract_tables()

                    for table_num, table in enumerate(tables):
                        if table and len(table) > 1:  # 确保表格有内容
                            # 转换为DataFrame进行处理
                            df = pd.DataFrame(table[1:], columns=table[0])

                            # 清理数据
                            df = df.dropna(how='all').fillna('')

                            # 生成表格的文本描述
                            table_text = self._format_table_as_text(df, page_num + 1, table_num + 1)

                            table_info = {
                                "page_number": page_num + 1,
                                "table_number": table_num + 1,
                                "content": table_text,
                                "dataframe": df,
                                "raw_table": table,
                                "type": "table"
                            }
                            tables_data.append(table_info)

            logger.info(f"使用pdfplumber提取到 {len(tables_data)} 个表格")
            return tables_data

        except Exception as e:
            logger.error(f"pdfplumber提取表格失败: {e}")
            return []

    def extract_tables_with_pymupdf(self, file_path: str) -> List[Dict[str, Any]]:
        """使用PyMuPDF提取PDF中的表格"""
        try:
            if not ADVANCED_PDF_AVAILABLE:
                logger.warning("PyMuPDF未安装，无法提取表格")
                return []

            tables_data = []
            doc = fitz.open(file_path)

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)

                # 查找表格
                tables = page.find_tables()

                for table_num, table in enumerate(tables):
                    try:
                        # 提取表格数据
                        table_data = table.extract()

                        if table_data and len(table_data) > 1:
                            # 转换为DataFrame
                            df = pd.DataFrame(table_data[1:], columns=table_data[0])
                            df = df.dropna(how='all').fillna('')

                            # 生成表格文本
                            table_text = self._format_table_as_text(df, page_num + 1, table_num + 1)

                            table_info = {
                                "page_number": page_num + 1,
                                "table_number": table_num + 1,
                                "content": table_text,
                                "dataframe": df,
                                "raw_table": table_data,
                                "type": "table",
                                "bbox": table.bbox  # 表格边界框
                            }
                            tables_data.append(table_info)

                    except Exception as e:
                        logger.warning(f"处理第{page_num+1}页第{table_num+1}个表格失败: {e}")
                        continue

            doc.close()
            logger.info(f"使用PyMuPDF提取到 {len(tables_data)} 个表格")
            return tables_data

        except Exception as e:
            logger.error(f"PyMuPDF提取表格失败: {e}")
            return []

    def _format_table_as_text(self, df: pd.DataFrame, page_num: int, table_num: int) -> str:
        """将DataFrame格式化为可搜索的文本"""
        try:
            # 表格标题
            title = f"第{page_num}页表格{table_num}:\n"

            # 表格摘要信息
            summary = f"表格规模: {df.shape[0]}行 x {df.shape[1]}列\n"

            # 列名信息
            columns_info = f"列名: {', '.join(df.columns.astype(str))}\n"

            # 表格内容 - 转换为易于搜索的格式
            content_lines = []

            # 添加列标题行
            header_line = " | ".join(df.columns.astype(str))
            content_lines.append(header_line)
            content_lines.append("-" * len(header_line))

            # 添加数据行
            for _, row in df.iterrows():
                row_line = " | ".join(row.astype(str))
                content_lines.append(row_line)

                # 为了便于搜索，也添加键值对格式
                for col, val in row.items():
                    if val and str(val).strip():
                        content_lines.append(f"{col}: {val}")

            # 组合所有内容
            table_text = title + summary + columns_info + "\n" + "\n".join(content_lines)

            return table_text

        except Exception as e:
            logger.error(f"格式化表格文本失败: {e}")
            return f"第{page_num}页表格{table_num} (格式化失败)"

    def _extract_page_context_text(self, file_path: str, page_num: int, image_bbox: tuple = None) -> Dict[str, str]:
        """
        提取页面的上下文文本信息

        Args:
            file_path: PDF文件路径
            page_num: 页面编号（从0开始）
            image_bbox: 图像边界框 (x0, y0, x1, y1)，可选

        Returns:
            包含上下文文本的字典
        """
        try:
            context_info = {
                "page_text": "",
                "surrounding_text": "",
                "before_text": "",
                "after_text": ""
            }

            # 使用PyMuPDF提取页面文本
            if ADVANCED_PDF_AVAILABLE:
                try:
                    doc = fitz.open(file_path)
                    if page_num < len(doc):
                        page = doc.load_page(page_num)

                        # 提取整页文本
                        page_text = page.get_text()
                        context_info["page_text"] = page_text.strip()

                        # 如果有图像边界框，尝试提取周围文本
                        if image_bbox and len(image_bbox) == 4:
                            try:
                                # 获取图像周围的文本块
                                text_blocks = page.get_text("dict")["blocks"]

                                img_x0, img_y0, img_x1, img_y1 = image_bbox
                                img_center_y = (img_y0 + img_y1) / 2

                                before_texts = []
                                after_texts = []
                                surrounding_texts = []

                                for block in text_blocks:
                                    if "lines" in block:  # 文本块
                                        block_bbox = block["bbox"]
                                        block_y0, block_y1 = block_bbox[1], block_bbox[3]
                                        block_center_y = (block_y0 + block_y1) / 2

                                        # 提取块中的文本
                                        block_text = ""
                                        for line in block["lines"]:
                                            for span in line["spans"]:
                                                block_text += span["text"] + " "

                                        block_text = block_text.strip()
                                        if not block_text:
                                            continue

                                        # 判断文本块与图像的位置关系
                                        if block_center_y < img_y0 - 20:  # 图像上方
                                            before_texts.append(block_text)
                                        elif block_center_y > img_y1 + 20:  # 图像下方
                                            after_texts.append(block_text)
                                        elif (block_bbox[0] < img_x0 - 20 or block_bbox[2] > img_x1 + 20):  # 图像左右
                                            surrounding_texts.append(block_text)

                                # 组合上下文文本
                                context_info["before_text"] = " ".join(before_texts[-2:])  # 最近的2个文本块
                                context_info["after_text"] = " ".join(after_texts[:2])   # 最近的2个文本块
                                context_info["surrounding_text"] = " ".join(surrounding_texts)

                            except Exception as e:
                                logger.warning(f"提取图像周围文本失败: {e}")

                    doc.close()

                except Exception as e:
                    logger.warning(f"使用PyMuPDF提取页面文本失败: {e}")

            # 如果PyMuPDF失败，尝试使用pdfplumber
            if not context_info["page_text"] and ADVANCED_PDF_AVAILABLE:
                try:
                    import pdfplumber
                    with pdfplumber.open(file_path) as pdf:
                        if page_num < len(pdf.pages):
                            page = pdf.pages[page_num]
                            page_text = page.extract_text()
                            if page_text:
                                context_info["page_text"] = page_text.strip()
                except Exception as e:
                    logger.warning(f"使用pdfplumber提取页面文本失败: {e}")

            return context_info

        except Exception as e:
            logger.error(f"提取页面上下文文本失败: {e}")
            return {
                "page_text": "",
                "surrounding_text": "",
                "before_text": "",
                "after_text": ""
            }

    def split_documents(self, documents: List[Document]) -> List[Document]:
        """分割文档为小块"""
        try:
            if not documents:
                logger.warning("没有文档需要分割")
                return []
            
            # 使用文本分割器分割文档
            split_docs = self.text_splitter.split_documents(documents)
            
            logger.info(f"文档分割完成: {len(documents)} -> {len(split_docs)} 块")
            return split_docs
            
        except Exception as e:
            logger.error(f"文档分割失败: {e}")
            return []
    
    def extract_metadata(self, document: Document, file_path: str, chunk_index: int) -> Dict[str, Any]:
        """提取文档元数据"""
        try:
            metadata = {
                "source": file_path,
                "file_name": os.path.basename(file_path),
                "chunk_index": chunk_index,
                "content_length": len(document.page_content),
                "category": "金融文档"  # 默认分类
            }
            
            # 合并原有元数据
            if hasattr(document, 'metadata') and document.metadata:
                metadata.update(document.metadata)
            
            return metadata
            
        except Exception as e:
            logger.error(f"提取元数据失败: {e}")
            return {"source": file_path, "category": "金融文档"}
    
    def process_pdf_to_knowledge_base(self, file_path: str, category: str = "金融文档",
                                    loader_type: str = "pypdf", extract_tables: bool = True,
                                     extract_images: bool = True, skip_deduplication: bool = False) -> bool:
        """
        处理PDF文件并添加到知识库（带去重功能）

        Args:
            file_path: PDF文件路径
            category: 文档分类
            loader_type: 加载器类型
            extract_tables: 是否提取表格
            extract_images: 是否提取图像
            skip_deduplication: 是否跳过去重检查

        Returns:
            处理是否成功
        """
        try:
            logger.info(f"开始处理PDF文件: {file_path}")

            # 0. PDF文件去重检查
            if self.enable_deduplication and not skip_deduplication and self.dedup_manager:
                is_duplicate, existing_file = self.dedup_manager.pdf_deduplicator.is_duplicate_pdf(file_path)
                if is_duplicate:
                    logger.info(f"跳过重复PDF文件: {file_path} (与 {existing_file} 相同)")
                    return True  # 返回True表示"成功"处理（通过跳过）
                else:
                    # 添加到PDF去重缓存
                    self.dedup_manager.pdf_deduplicator.add_pdf(file_path)
                    logger.info(f"PDF文件已添加到去重缓存: {file_path}")

            # 1. 加载PDF文档
            documents = self.load_pdf(file_path, loader_type)
            if not documents:
                logger.error("PDF文档加载失败")
                return False
            
            # 2. 分割文档
            split_docs = self.split_documents(documents)
            if not split_docs:
                logger.error("文档分割失败")
                return False
            
            # 3. 提取表格（如果启用）
            table_knowledge_list = []
            if extract_tables and self.enable_table_extraction:
                logger.info("开始提取PDF表格...")

                # 尝试使用pdfplumber提取表格
                tables = self.extract_tables_with_pdfplumber(file_path)

                # 如果pdfplumber失败，尝试PyMuPDF
                if not tables:
                    tables = self.extract_tables_with_pymupdf(file_path)

                # 处理提取的表格
                for table_info in tables:
                    table_content = table_info["content"]
                    if len(table_content) > 50:  # 确保表格有足够内容
                        # 分割表格内容
                        table_docs = self.table_splitter.split_text(table_content)

                        for j, table_chunk in enumerate(table_docs):
                            knowledge_item = {
                                "content": table_chunk,
                                "category": f"{category} - 表格",
                                "source": f"{os.path.basename(file_path)} - 第{table_info['page_number']}页表格{table_info['table_number']} - 第{j+1}块"
                            }
                            table_knowledge_list.append(knowledge_item)

                logger.info(f"提取到 {len(tables)} 个表格，生成 {len(table_knowledge_list)} 个表格知识块")

            # 4. 提取图像（如果启用）
            image_knowledge_list = []
            if extract_images:
                logger.info("开始提取PDF图像...")
                try:
                    # 计算文件哈希
                    import hashlib
                    with open(file_path, 'rb') as f:
                        file_hash = hashlib.md5(f.read()).hexdigest()

                    # 优先使用快速提取器
                    if self.enable_fast_extraction and self.fast_extractor:
                        start_time = time.time()
                        logger.info("使用快速图像提取器...")
                        images_info = self.fast_extractor.extract_images_fast(file_path, file_hash)

                        if images_info:
                            elapsed = time.time() - start_time
                            logger.info(f"快速提取完成: {len(images_info)} 个图像, 耗时 {elapsed:.2f}秒")

                            # 批量为快速提取的图像生成详细描述
                            if self.multimodal_retriever and images_info:
                                try:
                                    # 准备批量处理的图像数据
                                    batch_image_data = []
                                    valid_images_info = []

                                    for img_info in images_info:
                                        image_path = img_info.get('image_path')
                                        if image_path and os.path.exists(image_path):
                                            try:
                                                from PIL import Image
                                                image = Image.open(image_path)

                                                # 提取图像所在页面的上下文信息
                                                page_num = img_info.get('page_number', 1) - 1  # 转换为0基索引
                                                context_info = self._extract_page_context_text(file_path, page_num)

                                                batch_image_data.append({
                                                    'image': image,
                                                    'image_id': f"p{img_info['page_number']}_i{img_info['image_index']}",
                                                    'context_info': context_info
                                                })
                                                valid_images_info.append(img_info)

                                            except Exception as e:
                                                logger.warning(f"加载图像失败: {image_path}, 错误: {e}")

                                    if batch_image_data:
                                        logger.info(f"开始批量生成 {len(batch_image_data)} 个图像的描述")

                                        # 检查是否启用批量处理
                                        if hasattr(self.config, 'BATCH_IMAGE_DESCRIPTION') and self.config.BATCH_IMAGE_DESCRIPTION:
                                            batch_size = getattr(self.config, 'BATCH_SIZE_IMAGES', 5)

                                            # 智能分批处理
                                            all_descriptions = []
                                            for i in range(0, len(batch_image_data), batch_size):
                                                batch = batch_image_data[i:i + batch_size]
                                                batch_info = valid_images_info[i:i + batch_size]

                                                logger.info(f"处理批次 {i//batch_size + 1}: {len(batch)} 个图像")

                                                # 提取共享上下文（如文档级别的信息）
                                                shared_context = {
                                                    'document_name': os.path.basename(file_path),
                                                    'document_type': '金融文档',
                                                    'batch_info': f"批次 {i//batch_size + 1}/{(len(batch_image_data) + batch_size - 1)//batch_size}"
                                                }

                                                # 批量生成图像描述
                                                batch_descriptions = self.multimodal_retriever._generate_batch_image_descriptions(
                                                    batch, shared_context
                                                )

                                                all_descriptions.extend(batch_descriptions)

                                                # 记录批次处理结果
                                                success_count = len([d for d in batch_descriptions if d and d not in ['图像描述生成不可用', '图像描述生成失败']])
                                                logger.info(f"批次 {i//batch_size + 1} 完成: {success_count}/{len(batch)} 个图像成功")

                                            descriptions = all_descriptions
                                        else:
                                            # 单次批量处理（原逻辑）
                                            shared_context = {
                                                'document_name': os.path.basename(file_path),
                                                'document_type': '金融文档'
                                            }

                                            descriptions = self.multimodal_retriever._generate_batch_image_descriptions(
                                                batch_image_data, shared_context
                                            )

                                        # 将描述结果分配给对应的图像信息
                                        for i, (img_info, description) in enumerate(zip(valid_images_info, descriptions)):
                                            if description and description not in ["图像描述生成不可用", "图像描述生成失败"]:
                                                img_info['description'] = description
                                                img_info['context_info'] = batch_image_data[i]['context_info']
                                                logger.debug(f"为图像生成了批量描述: {img_info.get('image_path')}")
                                            else:
                                                logger.warning(f"图像描述生成失败: {img_info.get('image_path')}")

                                        success_total = len([d for d in descriptions if d and d not in ['图像描述生成不可用', '图像描述生成失败']])
                                        logger.info(f"批量图像描述生成完成，成功处理 {success_total}/{len(batch_image_data)} 个图像")

                                except Exception as e:
                                    logger.error(f"批量图像描述生成失败，回退到单独处理: {e}")

                                    # 回退到原来的单独处理方式
                                    for img_info in images_info:
                                        if self.multimodal_retriever and img_info.get('image_path'):
                                            try:
                                                from PIL import Image
                                                image_path = img_info['image_path']
                                                if os.path.exists(image_path):
                                                    image = Image.open(image_path)
                                                    page_num = img_info.get('page_number', 1) - 1
                                                    context_info = self._extract_page_context_text(file_path, page_num)
                                                    detailed_description = self.multimodal_retriever._generate_image_description(
                                                        image, context_info
                                                    )
                                                    if detailed_description and detailed_description not in ["图像描述生成不可用", "图像描述生成失败"]:
                                                        img_info['description'] = detailed_description
                                                        img_info['context_info'] = context_info
                                            except Exception as e:
                                                logger.warning(f"单独处理图像描述失败: {e}")

                                # 构建详细的图像知识条目
                                image_content_parts = []

                                # 1. 基础图像信息
                                basic_info = f"第{img_info['page_number']}页图像{img_info['image_index']}"
                                basic_info += f"，尺寸: {img_info['width']}x{img_info['height']}"
                                image_content_parts.append(basic_info)

                                # 2. 图像描述（使用上下文增强的详细描述）
                                if img_info.get('description'):
                                    description = img_info['description'].strip()
                                    if description and description not in ["图像描述生成不可用", "图像描述生成失败"]:
                                        image_content_parts.append(f"图像内容: {description}")

                                # 3. 上下文信息（新增）
                                if img_info.get('context_info'):
                                    context_info = img_info['context_info']
                                    if context_info.get('before_text'):
                                        before_text = context_info['before_text'][:100]  # 限制长度
                                        image_content_parts.append(f"前文上下文: {before_text}")
                                    if context_info.get('after_text'):
                                        after_text = context_info['after_text'][:100]  # 限制长度
                                        image_content_parts.append(f"后文上下文: {after_text}")

                                # 4. 图像类型信息
                                if img_info.get('image_type') and img_info['image_type'] not in ['未分类', '未知类型', '常规图像']:
                                    image_content_parts.append(f"图像类型: {img_info['image_type']}")

                                # 5. 提取的文本内容
                                if img_info.get('extracted_text') and img_info['extracted_text'].strip():
                                    image_content_parts.append(f"图像中的文字: {img_info['extracted_text']}")

                                # 6. 质量和增强信息
                                if img_info.get('quality_info'):
                                    quality_info = img_info['quality_info']
                                    if quality_info.get('is_chart') or quality_info.get('is_table'):
                                        chart_type = "图表" if quality_info.get('is_chart') else "表格"
                                        image_content_parts.append(f"检测到{chart_type}内容")

                                # 7. 增强处理信息
                                if img_info.get('enhancement_info'):
                                    enhancement_info = img_info['enhancement_info']
                                    if enhancement_info.get('enhanced'):
                                        enhancements = []
                                        if enhancement_info.get('contrast_enhanced'):
                                            enhancements.append("对比度增强")
                                        if enhancement_info.get('sharpness_enhanced'):
                                            enhancements.append("锐化处理")
                                        if enhancement_info.get('noise_reduced'):
                                            enhancements.append("降噪处理")
                                        if enhancements:
                                            image_content_parts.append(f"图像处理: {', '.join(enhancements)}")

                                # 组合所有内容
                                if image_content_parts:
                                    final_content = "。".join(image_content_parts) + "。"

                                    knowledge_item = {
                                        "content": final_content,
                                        "category": f"{category} - 图像",
                                        "source": f"{os.path.basename(file_path)} - 第{img_info['page_number']}页图像{img_info['image_index']}"
                                    }
                                    image_knowledge_list.append(knowledge_item)
                        else:
                            logger.info("快速提取未发现图像")

                    # 如果快速提取器不可用或未提取到图像，回退到多模态检索器
                    elif self.enable_image_extraction and self.multimodal_retriever:
                        logger.info("使用多模态图像检索器...")
                        images_info = self.multimodal_retriever.extract_images_from_pdf(file_path, file_hash)

                        if images_info:
                            # 建立图像索引
                            success = self.multimodal_retriever.index_images(file_path, file_hash)
                            if success:
                                logger.info(f"成功提取并索引 {len(images_info)} 个图像")

                                # 将图像描述添加到知识库
                                for img_info in images_info:
                                    # 构建详细的图像知识条目
                                    image_content_parts = []

                                    # 1. 基础图像信息
                                    basic_info = f"第{img_info['page_number']}页图像{img_info['image_index']}"
                                    basic_info += f"，尺寸: {img_info['width']}x{img_info['height']}"
                                    image_content_parts.append(basic_info)

                                    # 2. 图像描述（使用硅基流动2生成的详细描述）
                                    if img_info.get('description'):
                                        description = img_info['description'].strip()
                                        if description and description != "图像描述生成不可用" and description != "图像描述生成失败":
                                            image_content_parts.append(f"图像内容: {description}")

                                    # 3. 图像类型信息
                                    if img_info.get('image_type') and img_info['image_type'] not in ['未分类', '未知类型', '常规图像']:
                                        image_content_parts.append(f"图像类型: {img_info['image_type']}")

                                    # 4. 提取的文本内容
                                    if img_info.get('extracted_text') and img_info['extracted_text'].strip():
                                        image_content_parts.append(f"图像中的文字: {img_info['extracted_text']}")

                                    # 5. 质量和增强信息
                                    if img_info.get('quality_info'):
                                        quality_info = img_info['quality_info']
                                        if quality_info.get('is_chart') or quality_info.get('is_table'):
                                            chart_type = "图表" if quality_info.get('is_chart') else "表格"
                                            image_content_parts.append(f"检测到{chart_type}内容")

                                    # 6. 增强处理信息
                                    if img_info.get('enhancement_info'):
                                        enhancement_info = img_info['enhancement_info']
                                        if enhancement_info.get('enhanced'):
                                            enhancements = []
                                            if enhancement_info.get('contrast_enhanced'):
                                                enhancements.append("对比度增强")
                                            if enhancement_info.get('sharpness_enhanced'):
                                                enhancements.append("锐化处理")
                                            if enhancement_info.get('noise_reduced'):
                                                enhancements.append("降噪处理")
                                            if enhancements:
                                                image_content_parts.append(f"图像处理: {', '.join(enhancements)}")

                                    # 组合所有内容
                                    if image_content_parts:
                                        final_content = "。".join(image_content_parts) + "。"

                                        knowledge_item = {
                                            "content": final_content,
                                            "category": f"{category} - 图像",
                                            "source": f"{os.path.basename(file_path)} - 第{img_info['page_number']}页图像{img_info['image_index']}"
                                        }
                                        image_knowledge_list.append(knowledge_item)
                            else:
                                logger.warning("图像索引建立失败")
                        else:
                            logger.info("PDF中未发现图像或图像提取失败")
                    else:
                        logger.info("图像提取功能未启用")

                except Exception as e:
                    logger.error(f"图像提取过程中出现错误: {e}")
                    import traceback
                    traceback.print_exc()

                logger.info(f"生成 {len(image_knowledge_list)} 个图像知识块")

            # 5. 准备文本知识库数据
            text_knowledge_list = []
            for i, doc in enumerate(split_docs):
                # 清理文本内容
                content = doc.page_content.strip()
                if len(content) < 10:  # 跳过过短的内容
                    continue

                # 提取元数据
                metadata = self.extract_metadata(doc, file_path, i)

                knowledge_item = {
                    "content": content,
                    "category": category,
                    "source": f"{os.path.basename(file_path)} - 第{i+1}块"
                }
                text_knowledge_list.append(knowledge_item)

            # 合并文本、表格和图像知识
            knowledge_list = text_knowledge_list + table_knowledge_list + image_knowledge_list
            original_count = len(knowledge_list)

            # 6. 文本内容去重处理
            if self.enable_deduplication and not skip_deduplication and self.dedup_manager and knowledge_list:
                logger.info(f"开始对 {original_count} 个知识块进行去重处理")

                # 提取所有文本内容进行去重
                contents = [item['content'] for item in knowledge_list]
                unique_contents = self.dedup_manager.text_deduplicator.deduplicate_texts(
                    contents, use_fuzzy=True
                )

                # 根据去重结果过滤知识列表
                unique_content_set = set(unique_contents)
                knowledge_list = [
                    item for item in knowledge_list
                    if item['content'] in unique_content_set
                ]

                dedup_count = original_count - len(knowledge_list)
                if dedup_count > 0:
                    logger.info(f"内容去重完成：去除 {dedup_count} 个重复知识块，保留 {len(knowledge_list)} 个唯一知识块")
                else:
                    logger.info("未发现重复的知识内容")

            # 7. 批量添加到知识库
            if knowledge_list:
                success = self.rag_system.batch_add_knowledge(knowledge_list)
                if success:
                    total_text = len(text_knowledge_list)
                    total_tables = len(table_knowledge_list)
                    total_images = len(image_knowledge_list)

                    if self.enable_deduplication and not skip_deduplication:
                        logger.info(f"成功处理PDF文件: {file_path}")
                        logger.info(f"原始知识块: {original_count} 个，去重后: {len(knowledge_list)} 个")
                        logger.info(f"知识块分布 - 文本: {total_text}, 表格: {total_tables}, 图像: {total_images}")
                    else:
                        logger.info(f"成功处理PDF文件: {file_path}, 添加了 {len(knowledge_list)} 个知识块 "
                                  f"(文本: {total_text}, 表格: {total_tables}, 图像: {total_images})")
                    return True
                else:
                    logger.error("批量添加知识到数据库失败")
                    return False
            else:
                logger.warning("没有有效的知识内容可添加")
                return False
                
        except Exception as e:
            logger.error(f"处理PDF文件失败: {e}")
            return False
    

    def extract_table_only(self, file_path: str) -> List[Dict[str, Any]]:
        """仅提取PDF中的表格，不处理文本"""
        try:
            logger.info(f"开始提取PDF表格: {file_path}")

            # 尝试使用pdfplumber提取表格
            tables = self.extract_tables_with_pdfplumber(file_path)

            # 如果pdfplumber失败，尝试PyMuPDF
            if not tables:
                tables = self.extract_tables_with_pymupdf(file_path)

            return tables

        except Exception as e:
            logger.error(f"提取表格失败: {e}")
            return []

    def search_tables_in_pdf(self, file_path: str, query: str) -> List[Dict[str, Any]]:
        """在PDF的表格中搜索特定内容"""
        try:
            tables = self.extract_table_only(file_path)
            matching_tables = []

            for table_info in tables:
                df = table_info.get("dataframe")
                if df is not None:
                    # 在DataFrame中搜索
                    matches = self._search_in_dataframe(df, query)
                    if matches:
                        table_info["matches"] = matches
                        matching_tables.append(table_info)

            return matching_tables

        except Exception as e:
            logger.error(f"在PDF表格中搜索失败: {e}")
            return []

    def _search_in_dataframe(self, df: pd.DataFrame, query: str) -> List[Dict[str, Any]]:
        """在DataFrame中搜索匹配的内容"""
        try:
            matches = []
            query_lower = query.lower()

            # 搜索所有单元格
            for row_idx, row in df.iterrows():
                for col_name, cell_value in row.items():
                    cell_str = str(cell_value).lower()
                    if query_lower in cell_str:
                        matches.append({
                            "row": row_idx,
                            "column": col_name,
                            "value": cell_value,
                            "context": row.to_dict()
                        })

            return matches

        except Exception as e:
            logger.error(f"DataFrame搜索失败: {e}")
            return []

    def analyze_table_structure(self, file_path: str) -> Dict[str, Any]:
        """分析PDF中表格的结构"""
        try:
            tables = self.extract_table_only(file_path)

            analysis = {
                "total_tables": len(tables),
                "tables_by_page": {},
                "column_analysis": {},
                "data_types": {}
            }

            for table_info in tables:
                page_num = table_info["page_number"]
                df = table_info.get("dataframe")

                # 按页面统计
                if page_num not in analysis["tables_by_page"]:
                    analysis["tables_by_page"][page_num] = 0
                analysis["tables_by_page"][page_num] += 1

                # 分析列结构
                if df is not None:
                    for col in df.columns:
                        col_str = str(col)
                        if col_str not in analysis["column_analysis"]:
                            analysis["column_analysis"][col_str] = 0
                        analysis["column_analysis"][col_str] += 1

                        # 分析数据类型
                        sample_values = df[col].dropna().head(5)
                        if len(sample_values) > 0:
                            analysis["data_types"][col_str] = self._infer_column_type(sample_values)

            return analysis

        except Exception as e:
            logger.error(f"分析表格结构失败: {e}")
            return {}

    def _infer_column_type(self, sample_values) -> str:
        """推断列的数据类型"""
        try:
            # 尝试转换为数字
            numeric_count = 0
            date_count = 0

            for value in sample_values:
                value_str = str(value).strip()

                # 检查是否为数字
                try:
                    float(value_str.replace(',', '').replace('%', ''))
                    numeric_count += 1
                except:
                    pass

                # 检查是否为日期
                if any(char in value_str for char in ['年', '月', '日', '-', '/']):
                    date_count += 1

            total = len(sample_values)
            if numeric_count / total > 0.7:
                return "数值型"
            elif date_count / total > 0.5:
                return "日期型"
            else:
                return "文本型"

        except Exception as e:
            return "未知"

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            stats = {
                "chunk_size": self.chunk_size,
                "chunk_overlap": self.chunk_overlap,
                "table_chunk_size": self.table_chunk_size,
                "text_splitter_type": "RecursiveCharacterTextSplitter",
                "table_extraction_enabled": self.enable_table_extraction,
                "advanced_pdf_available": ADVANCED_PDF_AVAILABLE,
                "deduplication_enabled": self.enable_deduplication,
                "deduplication_available": DEDUPLICATION_AVAILABLE
            }

            # 添加去重统计信息
            if self.enable_deduplication and self.dedup_manager:
                dedup_stats = self.dedup_manager.get_deduplication_stats()
                stats["deduplication_stats"] = dedup_stats

            return stats
        except Exception as e:
            logger.error(f"获取处理统计信息失败: {e}")
            return {}

    def get_deduplication_stats(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        if not self.enable_deduplication or not self.dedup_manager:
            return {"deduplication_enabled": False}

        return self.dedup_manager.get_deduplication_stats()

    def clear_deduplication_cache(self):
        """清空去重缓存"""
        if self.enable_deduplication and self.dedup_manager:
            self.dedup_manager.clear_all_caches()
            logger.info("PDF处理器：去重缓存已清空")
        else:
            logger.warning("PDF处理器：去重功能未启用，无法清空缓存")

    def optimize_deduplication_cache(self):
        """优化去重缓存"""
        if self.enable_deduplication and self.dedup_manager:
            self.dedup_manager.optimize_caches()
            logger.info("PDF处理器：去重缓存已优化")
        else:
            logger.warning("PDF处理器：去重功能未启用，无法优化缓存")

    def enable_deduplication_feature(self):
        """启用去重功能"""
        if DEDUPLICATION_AVAILABLE:
            if not self.enable_deduplication:
                self.enable_deduplication = True
                self.dedup_manager = DeduplicationManager()
                logger.info("PDF处理器：去重功能已启用")
            else:
                logger.info("PDF处理器：去重功能已经启用")
        else:
            logger.error("PDF处理器：去重模块不可用，无法启用去重功能")

    def disable_deduplication_feature(self):
        """禁用去重功能"""
        if self.enable_deduplication:
            self.enable_deduplication = False
            self.dedup_manager = None
            logger.info("PDF处理器：去重功能已禁用")
        else:
            logger.info("PDF处理器：去重功能已经禁用")
