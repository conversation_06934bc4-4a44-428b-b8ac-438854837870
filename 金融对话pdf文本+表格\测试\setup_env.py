
"""
环境配置助手
帮助用户快速配置.env文件
"""
import os
import sys
from pathlib import Path

# 切换到项目根目录并添加到Python路径
project_root = Path(__file__).parent.parent
os.chdir(project_root)
sys.path.insert(0, str(project_root))

def create_env_file():
    """创建.env文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✓ .env文件已存在")
        return True
    
    if env_example.exists():
        # 复制.env.example到.env
        with open(env_example, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 已从.env.example创建.env文件")
        return True
    else:
        print("✗ .env.example文件不存在")
        return False

def check_api_keys():
    """检查API密钥配置"""
    from idconfig.config import Config
    
    config = Config()
    
    print("\n检查API密钥配置:")
    print("=" * 40)
    
    models = [
        ("OpenAI", config.OPENAI_API_KEY),
        ("智谱AI", config.ZHIPU_API_KEY),
        ("通义千问", config.QWEN_API_KEY),
        ("硅基流动", config.SILICONFLOW_API_KEY),
        ("百度文心", config.BAIDU_API_KEY and config.BAIDU_SECRET_KEY),
        ("Moonshot", config.MOONSHOT_API_KEY),
        ("DeepSeek", config.DEEPSEEK_API_KEY),
        ("豆包", config.DOUBAO_API_KEY),
        ("Claude", config.ANTHROPIC_API_KEY),
        ("Gemini", config.GOOGLE_API_KEY)
    ]
    
    configured_count = 0
    for name, configured in models:
        status = "✓ 已配置" if configured else "✗ 未配置"
        print(f"{name:10} {status}")
        if configured:
            configured_count += 1
    
    print("=" * 40)
    print(f"已配置模型数量: {configured_count}/10")
    
    if configured_count == 0:
        print("\n⚠️  警告: 没有配置任何API密钥!")
        print("请编辑.env文件，至少配置一个模型的API密钥")
        return False
    else:
        print(f"\n✓ 已配置 {configured_count} 个模型，系统可以正常运行")
        return True

def interactive_setup():
    """交互式设置"""
    print("金融对话系统 - 环境配置助手")
    print("=" * 50)
    
    # 创建.env文件
    if not create_env_file():
        return False
    
    # 检查API密钥
    if not check_api_keys():
        print("\n配置建议:")
        print("1. 硅基流动 - 免费额度较多，推荐新手使用")
        print("   注册地址: https://siliconflow.cn/")
        print("   在.env文件中设置: SILICONFLOW_API_KEY=your_api_key")
        
        print("\n2. OpenAI - 功能强大，需要付费")
        print("   注册地址: https://platform.openai.com/")
        print("   在.env文件中设置: OPENAI_API_KEY=your_api_key")
        
        print("\n3. 智谱AI - 国内服务，响应快速")
        print("   注册地址: https://open.bigmodel.cn/")
        print("   在.env文件中设置: ZHIPU_API_KEY=your_api_key")
        
        print("\n配置完成后，重新运行此脚本检查配置")
        return False
    
    return True

def main():
    """主函数"""
    try:
        if interactive_setup():
            print("\n🎉 环境配置完成！")
            print("现在可以运行以下命令启动系统:")
            print("  python main.py --mode server")
            print("或者:")
            print("  python start.py")
        else:
            print("\n❌ 环境配置未完成，请按照提示配置API密钥")
    except Exception as e:
        print(f"\n❌ 配置过程中出现错误: {e}")
        print("请检查文件权限和网络连接")

if __name__ == "__main__":
    main()
