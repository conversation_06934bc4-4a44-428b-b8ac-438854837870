"""
BGE-M3模型检查脚本
验证BGE-M3模型是否正确配置和工作
"""
import os
import sys
from pathlib import Path
import numpy as np

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

def check_model_files():
    """检查模型文件是否存在"""
    print("检查BGE-M3模型文件...")
    
    model_path = Path("./bge-m3")
    if not model_path.exists():
        print("❌ 模型目录不存在: ./bge-m3")
        print("请确保BGE-M3模型文件已下载到 ./bge-m3 目录")
        return False
    
    # 检查必要的模型文件
    required_files = [
        "config.json",
        "pytorch_model.bin",
        "tokenizer.json",
        "tokenizer_config.json"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not (model_path / file_name).exists():
            missing_files.append(file_name)
    
    if missing_files:
        print(f"❌ 缺少模型文件: {', '.join(missing_files)}")
        return False
    
    print("✓ BGE-M3模型文件检查通过")
    return True

def test_model_loading():
    """测试模型加载"""
    print("\n测试BGE-M3模型加载...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        print("正在加载BGE-M3模型...")
        model = SentenceTransformer("./bge-m3")
        print("✓ BGE-M3模型加载成功")
        
        return model
        
    except Exception as e:
        print(f"❌ BGE-M3模型加载失败: {e}")
        return None

def test_model_encoding(model):
    """测试模型编码功能"""
    print("\n测试BGE-M3模型编码...")
    
    try:
        # 测试文本
        test_texts = [
            "什么是股票？",
            "股票是公司所有权的凭证",
            "债券和股票有什么区别？",
            "基金投资的风险有哪些？"
        ]
        
        print("正在编码测试文本...")
        embeddings = model.encode(test_texts, normalize_embeddings=True)
        
        print(f"✓ 编码完成，向量形状: {embeddings.shape}")
        print(f"✓ 向量维度: {embeddings.shape[1]}")
        
        # 检查维度
        if embeddings.shape[1] != 1024:
            print(f"⚠️  警告: BGE-M3应该是1024维，但检测到{embeddings.shape[1]}维")
        else:
            print("✓ 向量维度正确 (1024维)")
        
        # 检查归一化
        norms = np.linalg.norm(embeddings, axis=1)
        avg_norm = np.mean(norms)
        print(f"✓ 向量归一化检查: 平均norm={avg_norm:.4f}")
        
        if abs(avg_norm - 1.0) > 0.01:
            print("⚠️  警告: 向量未完全归一化")
        else:
            print("✓ 向量归一化正确")
        
        # 测试相似度计算
        print("\n测试相似度计算...")
        similarity_matrix = np.dot(embeddings, embeddings.T)
        
        print("相似度矩阵 (前2x2):")
        print(similarity_matrix[:2, :2])
        
        # 检查对角线是否接近1（自相似度）
        diagonal_avg = np.mean(np.diag(similarity_matrix))
        print(f"✓ 自相似度平均值: {diagonal_avg:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型编码测试失败: {e}")
        return False

def test_config_compatibility():
    """测试配置兼容性"""
    print("\n检查配置兼容性...")
    
    try:
        from idconfig.config import Config
        config = Config()
        
        print(f"配置的向量维度: {config.VECTOR_DIM}")
        print(f"配置的嵌入模型: {config.EMBEDDING_MODEL}")
        print(f"配置的相似度阈值: {config.SIMILARITY_THRESHOLD}")
        
        # 检查配置
        issues = []
        
        if config.VECTOR_DIM != 1024:
            issues.append(f"VECTOR_DIM应该设置为1024，当前为{config.VECTOR_DIM}")
        
        if "bge-m3" not in config.EMBEDDING_MODEL.lower():
            issues.append(f"EMBEDDING_MODEL路径可能不正确: {config.EMBEDDING_MODEL}")
        
        if config.SIMILARITY_THRESHOLD > 0.8:
            issues.append(f"余弦相似度阈值可能过高: {config.SIMILARITY_THRESHOLD}")
        
        if issues:
            print("⚠️  配置问题:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✓ 配置检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def main():
    """主函数"""
    print("BGE-M3模型检查工具")
    print("=" * 50)
    
    # 检查模型文件
    if not check_model_files():
        print("\n建议:")
        print("1. 下载BGE-M3模型到 ./bge-m3 目录")
        print("2. 可以使用以下命令下载:")
        print("   git clone https://huggingface.co/BAAI/bge-m3 ./bge-m3")
        return False
    
    # 测试模型加载
    model = test_model_loading()
    if not model:
        return False
    
    # 测试模型编码
    if not test_model_encoding(model):
        return False
    
    # 测试配置兼容性
    if not test_config_compatibility():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 BGE-M3模型检查完成！")
    print("✓ 模型文件正常")
    print("✓ 模型加载正常") 
    print("✓ 编码功能正常")
    print("✓ 配置兼容正常")
    print("\n现在可以启动金融对话系统了！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断检查")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程中出现错误: {e}")
        sys.exit(1)
