#!/usr/bin/env python3
"""
批量图像描述生成功能测试脚本
"""

import sys
import os
import time
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

from loguru import logger
from rag.pdf_processor import PDFProcessor
from idconfig.config import Config

def test_batch_vs_single_processing():
    """测试批量处理与单独处理的性能对比"""
    print("=" * 60)
    print("批量图像描述生成功能测试")
    print("=" * 60)
    
    # 初始化处理器
    processor = PDFProcessor()
    if not processor.initialize():
        print("❌ PDF处理器初始化失败")
        return
    
    # 测试文件（请替换为实际的PDF文件路径）
    test_pdf = "test_document.pdf"  # 请替换为实际文件
    
    if not os.path.exists(test_pdf):
        print(f"❌ 测试文件不存在: {test_pdf}")
        print("请将一个包含图像的PDF文件命名为 'test_document.pdf' 并放在当前目录")
        return
    
    print(f"📄 测试文件: {test_pdf}")
    print()
    
    # 测试1: 批量处理模式
    print("🚀 测试1: 批量处理模式")
    print("-" * 30)
    
    processor.config.BATCH_IMAGE_DESCRIPTION = True
    processor.config.BATCH_SIZE_IMAGES = 5
    
    start_time = time.time()
    success_batch = processor.process_pdf_to_knowledge_base(
        test_pdf, 
        category="测试文档-批量",
        extract_images=True
    )
    batch_time = time.time() - start_time
    
    print(f"✅ 批量处理完成: {'成功' if success_batch else '失败'}")
    print(f"⏱️  耗时: {batch_time:.2f}秒")
    print()
    
    # 测试2: 单独处理模式
    print("🔄 测试2: 单独处理模式")
    print("-" * 30)
    
    processor.config.BATCH_IMAGE_DESCRIPTION = False
    
    start_time = time.time()
    success_single = processor.process_pdf_to_knowledge_base(
        test_pdf, 
        category="测试文档-单独",
        extract_images=True
    )
    single_time = time.time() - start_time
    
    print(f"✅ 单独处理完成: {'成功' if success_single else '失败'}")
    print(f"⏱️  耗时: {single_time:.2f}秒")
    print()
    
    # 性能对比
    print("📊 性能对比结果")
    print("-" * 30)
    
    if batch_time > 0 and single_time > 0:
        speedup = single_time / batch_time
        time_saved = single_time - batch_time
        efficiency = (time_saved / single_time) * 100
        
        print(f"批量处理时间: {batch_time:.2f}秒")
        print(f"单独处理时间: {single_time:.2f}秒")
        print(f"性能提升: {speedup:.2f}倍")
        print(f"时间节省: {time_saved:.2f}秒 ({efficiency:.1f}%)")
        
        if speedup > 1.5:
            print("🎉 批量处理显著提升了性能！")
        elif speedup > 1.1:
            print("✅ 批量处理有一定性能提升")
        else:
            print("⚠️  批量处理性能提升不明显，可能需要调整配置")
    
    print()

def test_different_batch_sizes():
    """测试不同批量大小的性能"""
    print("🔧 测试不同批量大小")
    print("-" * 30)
    
    processor = PDFProcessor()
    if not processor.initialize():
        print("❌ PDF处理器初始化失败")
        return
    
    test_pdf = "test_document.pdf"
    if not os.path.exists(test_pdf):
        print(f"❌ 测试文件不存在: {test_pdf}")
        return
    
    batch_sizes = [2, 3, 5, 8]
    results = {}
    
    for batch_size in batch_sizes:
        print(f"测试批量大小: {batch_size}")
        
        processor.config.BATCH_IMAGE_DESCRIPTION = True
        processor.config.BATCH_SIZE_IMAGES = batch_size
        
        start_time = time.time()
        success = processor.process_pdf_to_knowledge_base(
            test_pdf, 
            category=f"测试-批量{batch_size}",
            extract_images=True
        )
        elapsed_time = time.time() - start_time
        
        results[batch_size] = {
            'success': success,
            'time': elapsed_time
        }
        
        print(f"  结果: {'成功' if success else '失败'}, 耗时: {elapsed_time:.2f}秒")
    
    # 找出最优批量大小
    successful_results = {k: v for k, v in results.items() if v['success']}
    if successful_results:
        best_batch_size = min(successful_results.keys(), key=lambda k: successful_results[k]['time'])
        best_time = successful_results[best_batch_size]['time']
        
        print(f"\n🏆 最优批量大小: {best_batch_size} (耗时: {best_time:.2f}秒)")
    
    print()

def test_api_configuration():
    """测试API配置"""
    print("🔧 测试API配置")
    print("-" * 30)
    
    config = Config()
    
    # 检查硅基流动2配置
    if hasattr(config, 'SILICONFLOW2_API_KEY') and config.SILICONFLOW2_API_KEY:
        print("✅ 硅基流动2 API密钥已配置")
        print(f"   模型: {getattr(config, 'SILICONFLOW2_MODEL', '未配置')}")
        print(f"   基础URL: {getattr(config, 'SILICONFLOW2_BASE_URL', '未配置')}")
    else:
        print("❌ 硅基流动2 API密钥未配置")
        print("   批量处理将回退到BLIP模型")
    
    # 检查批量处理配置
    batch_enabled = getattr(config, 'BATCH_IMAGE_DESCRIPTION', False)
    batch_size = getattr(config, 'BATCH_SIZE_IMAGES', 5)
    batch_timeout = getattr(config, 'BATCH_TIMEOUT_SECONDS', 120)
    
    print(f"\n📋 批量处理配置:")
    print(f"   启用状态: {'✅ 已启用' if batch_enabled else '❌ 已禁用'}")
    print(f"   批量大小: {batch_size}")
    print(f"   超时时间: {batch_timeout}秒")
    
    print()

def main():
    """主测试函数"""
    print("批量图像描述生成功能测试工具")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    # 测试API配置
    test_api_configuration()
    
    # 性能对比测试
    try:
        test_batch_vs_single_processing()
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
    
    # 不同批量大小测试
    try:
        test_different_batch_sizes()
    except Exception as e:
        print(f"❌ 批量大小测试失败: {e}")
    
    print("=" * 60)
    print("测试完成")
    print("=" * 60)
    
    # 使用建议
    print("\n💡 使用建议:")
    print("1. 如果批量处理显著提升性能，建议启用该功能")
    print("2. 根据测试结果调整最优批量大小")
    print("3. 监控API使用情况，避免超出限制")
    print("4. 对于复杂图像，使用较小的批量大小")
    print("5. 定期检查处理日志，确保质量稳定")

if __name__ == "__main__":
    main()
