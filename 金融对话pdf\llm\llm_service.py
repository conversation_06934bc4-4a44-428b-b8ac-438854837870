"""
在线大模型调用服务
支持多种在线大模型服务
"""
import openai
import requests
import json
import hashlib
import hmac
import base64
import time
import urllib.parse
from typing import Dict, Any, List
from loguru import logger
import sys
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from idconfig.config import Config

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    logger.warning("anthropic库未安装，Claude模型不可用")

try:
    import google.generativeai as genai
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    logger.warning("google-generativeai库未安装，Gemini模型不可用")

try:
    from tencentcloud.common import credential
    from tencentcloud.common.profile.client_profile import ClientProfile
    from tencentcloud.common.profile.http_profile import HttpProfile
    from tencentcloud.hunyuan.v20230901 import hunyuan_client, models
    TENCENT_AVAILABLE = True
except ImportError:
    TENCENT_AVAILABLE = False
    logger.warning("tencentcloud-sdk-python库未安装，腾讯混元模型不可用")

class LLMService:
    def __init__(self):
        self.config = Config()
        self.setup_openai()
    
    def setup_openai(self):
        """设置OpenAI客户端"""
        if self.config.OPENAI_API_KEY:
            openai.api_key = self.config.OPENAI_API_KEY
            if self.config.OPENAI_BASE_URL:
                openai.base_url = self.config.OPENAI_BASE_URL
            logger.info("OpenAI客户端配置完成")
    
    def call_openai(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用OpenAI API"""
        try:
            if not self.config.OPENAI_API_KEY:
                logger.error("OpenAI API密钥未配置")
                return "抱歉，大模型服务暂时不可用。"
            
            client = openai.OpenAI(
                api_key=self.config.OPENAI_API_KEY,
                base_url=self.config.OPENAI_BASE_URL
            )
            
            response = client.chat.completions.create(
                model=self.config.MODEL_NAME,
                messages=messages,
                temperature=temperature,
                max_tokens=2000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"调用OpenAI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def call_zhipu(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用智谱AI API"""
        try:
            if not self.config.ZHIPU_API_KEY:
                logger.error("智谱AI API密钥未配置")
                return "抱歉，智谱AI服务暂时不可用。"
            
            url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.ZHIPU_API_KEY}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.config.ZHIPU_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except Exception as e:
            logger.error(f"调用智谱AI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def call_qwen(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用通义千问API"""
        try:
            if not self.config.QWEN_API_KEY:
                logger.error("通义千问API密钥未配置")
                return "抱歉，通义千问服务暂时不可用。"

            # 这里需要根据实际的通义千问API接口进行调整
            url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
            headers = {
                "Authorization": f"Bearer {self.config.QWEN_API_KEY}",
                "Content-Type": "application/json"
            }

            # 将messages转换为通义千问格式
            prompt = self._convert_messages_to_prompt(messages)

            data = {
                "model": self.config.QWEN_MODEL,
                "input": {
                    "prompt": prompt
                },
                "parameters": {
                    "temperature": temperature,
                    "max_tokens": 2000
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["output"]["text"]

        except Exception as e:
            logger.error(f"调用通义千问API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_siliconflow(self, messages: List[Dict[str, str]], temperature: float = 0.7, model: str = None) -> str:
        """调用硅基流动API"""
        try:
            if not self.config.SILICONFLOW_API_KEY:
                logger.error("硅基流动API密钥未配置")
                return "抱歉，硅基流动服务暂时不可用。"

            url = "https://api.siliconflow.cn/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.SILICONFLOW_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model or self.config.SILICONFLOW_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000,
                "stream": False
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用硅基流动API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_baidu(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用百度文心一言API"""
        try:
            if not self.config.BAIDU_API_KEY or not self.config.BAIDU_SECRET_KEY:
                logger.error("百度API密钥未配置")
                return "抱歉，百度文心一言服务暂时不可用。"

            # 获取access_token
            access_token = self._get_baidu_access_token()
            if not access_token:
                return "抱歉，获取百度访问令牌失败。"

            url = f"{self.config.BAIDU_BASE_URL}/chat/completions_pro?access_token={access_token}"
            headers = {
                "Content-Type": "application/json"
            }

            data = {
                "messages": messages,
                "temperature": temperature,
                "max_output_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            if "result" in result:
                return result["result"]
            else:
                logger.error(f"百度API返回格式错误: {result}")
                return "抱歉，处理您的请求时出现了错误。"

        except Exception as e:
            logger.error(f"调用百度文心一言API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def _get_baidu_access_token(self) -> str:
        """获取百度access_token"""
        try:
            url = "https://aip.baidubce.com/oauth/2.0/token"
            params = {
                "grant_type": "client_credentials",
                "client_id": self.config.BAIDU_API_KEY,
                "client_secret": self.config.BAIDU_SECRET_KEY
            }

            response = requests.post(url, params=params, timeout=10)
            response.raise_for_status()

            result = response.json()
            return result.get("access_token", "")

        except Exception as e:
            logger.error(f"获取百度access_token失败: {e}")
            return ""

    def call_moonshot(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Moonshot AI (Kimi) API"""
        try:
            if not self.config.MOONSHOT_API_KEY:
                logger.error("Moonshot API密钥未配置")
                return "抱歉，Moonshot AI服务暂时不可用。"

            url = f"{self.config.MOONSHOT_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.MOONSHOT_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.MOONSHOT_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用Moonshot AI API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_deepseek(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用DeepSeek API"""
        try:
            if not self.config.DEEPSEEK_API_KEY:
                logger.error("DeepSeek API密钥未配置")
                return "抱歉，DeepSeek服务暂时不可用。"

            url = f"{self.config.DEEPSEEK_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.DEEPSEEK_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.DEEPSEEK_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用DeepSeek API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_doubao(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用字节豆包API"""
        try:
            if not self.config.DOUBAO_API_KEY:
                logger.error("豆包API密钥未配置")
                return "抱歉，豆包服务暂时不可用。"

            url = f"{self.config.DOUBAO_BASE_URL}/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.config.DOUBAO_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": self.config.DOUBAO_MODEL,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": 2000
            }

            response = requests.post(url, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()
            return result["choices"][0]["message"]["content"]

        except Exception as e:
            logger.error(f"调用豆包API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_claude(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Anthropic Claude API"""
        try:
            if not ANTHROPIC_AVAILABLE:
                return "抱歉，Claude服务需要安装anthropic库。"

            if not self.config.ANTHROPIC_API_KEY:
                logger.error("Anthropic API密钥未配置")
                return "抱歉，Claude服务暂时不可用。"

            client = anthropic.Anthropic(api_key=self.config.ANTHROPIC_API_KEY)

            # 转换消息格式
            claude_messages = []
            system_message = ""

            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    claude_messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            response = client.messages.create(
                model=self.config.ANTHROPIC_MODEL,
                max_tokens=2000,
                temperature=temperature,
                system=system_message,
                messages=claude_messages
            )

            return response.content[0].text

        except Exception as e:
            logger.error(f"调用Claude API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"

    def call_gemini(self, messages: List[Dict[str, str]], temperature: float = 0.7) -> str:
        """调用Google Gemini API"""
        try:
            if not GOOGLE_AVAILABLE:
                return "抱歉，Gemini服务需要安装google-generativeai库。"

            if not self.config.GOOGLE_API_KEY:
                logger.error("Google API密钥未配置")
                return "抱歉，Gemini服务暂时不可用。"

            genai.configure(api_key=self.config.GOOGLE_API_KEY)
            model = genai.GenerativeModel(self.config.GOOGLE_MODEL)

            # 转换消息格式为单个提示
            prompt_parts = []
            for msg in messages:
                role = msg["role"]
                content = msg["content"]
                if role == "system":
                    prompt_parts.append(f"系统指令: {content}")
                elif role == "user":
                    prompt_parts.append(f"用户: {content}")
                elif role == "assistant":
                    prompt_parts.append(f"助手: {content}")

            prompt = "\n".join(prompt_parts)

            response = model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=temperature,
                    max_output_tokens=2000
                )
            )

            return response.text

        except Exception as e:
            logger.error(f"调用Gemini API失败: {e}")
            return "抱歉，处理您的请求时出现了错误。"
    
    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为提示文本"""
        prompt_parts = []
        for message in messages:
            role = message["role"]
            content = message["content"]
            if role == "system":
                prompt_parts.append(f"系统: {content}")
            elif role == "user":
                prompt_parts.append(f"用户: {content}")
            elif role == "assistant":
                prompt_parts.append(f"助手: {content}")
        
        return "\n".join(prompt_parts)
    
    def generate_response(self, messages: List[Dict[str, str]],
                         model_provider: str = "openai",
                         temperature: float = 0.7,
                         model: str = None) -> str:
        """生成回复"""
        try:
            provider = model_provider.lower()

            if provider == "openai":
                return self.call_openai(messages, temperature)
            elif provider == "zhipu":
                return self.call_zhipu(messages, temperature)
            elif provider == "qwen":
                return self.call_qwen(messages, temperature)
            elif provider == "siliconflow":
                if model:
                    return self.call_siliconflow(messages, temperature, model)
                else:
                    return self.call_siliconflow(messages, temperature)
            elif provider == "baidu":
                return self.call_baidu(messages, temperature)
            elif provider == "moonshot":
                return self.call_moonshot(messages, temperature)
            elif provider == "deepseek":
                return self.call_deepseek(messages, temperature)
            elif provider == "doubao":
                return self.call_doubao(messages, temperature)
            elif provider == "claude":
                return self.call_claude(messages, temperature)
            elif provider == "gemini":
                return self.call_gemini(messages, temperature)
            else:
                logger.error(f"不支持的模型提供商: {model_provider}")
                return "抱歉，不支持的模型提供商。"

        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return "抱歉，生成回复时出现了错误。"
    
    def create_financial_prompt(self, user_query: str, context: Dict[str, Any]) -> List[Dict[str, str]]:
        """创建金融对话的提示"""
        # 构建系统提示
        system_prompt = """你是一个专业的金融助手，具有丰富的金融知识和经验。请根据提供的上下文信息回答用户的问题。

回答要求：
1. 准确、专业、客观
2. 基于提供的上下文信息
3. 如果上下文信息不足，请明确说明
4. 使用简洁明了的语言
5. 必要时提供相关的金融术语解释

请始终保持专业性和准确性。"""
        
        # 构建上下文信息
        context_text = ""
        
        # 添加知识库信息
        if context.get("knowledge"):
            context_text += "相关专业知识：\n"
            for i, item in enumerate(context["knowledge"][:3], 1):  # 只取前3条
                context_text += f"{i}. {item['content']} (来源: {item['source']})\n"
            context_text += "\n"
        
        # 添加历史对话信息
        if context.get("history"):
            context_text += "相关历史对话：\n"
            for i, item in enumerate(context["history"][:2], 1):  # 只取前2条
                context_text += f"{i}. 用户问: {item['user_query']}\n   回答: {item['assistant_response']}\n"
            context_text += "\n"
        
        # 构建用户消息
        user_message = f"""上下文信息：
{context_text}

用户问题：{user_query}

请基于上述上下文信息回答用户的问题。"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        return messages
    
    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        available_models = []

        if self.config.OPENAI_API_KEY:
            available_models.append("openai")

        if self.config.ZHIPU_API_KEY:
            available_models.append("zhipu")

        if self.config.QWEN_API_KEY:
            available_models.append("qwen")

        if self.config.SILICONFLOW_API_KEY:
            available_models.append("siliconflow")

        if self.config.BAIDU_API_KEY and self.config.BAIDU_SECRET_KEY:
            available_models.append("baidu")

        if self.config.MOONSHOT_API_KEY:
            available_models.append("moonshot")

        if self.config.DEEPSEEK_API_KEY:
            available_models.append("deepseek")

        if self.config.DOUBAO_API_KEY:
            available_models.append("doubao")

        if self.config.ANTHROPIC_API_KEY and ANTHROPIC_AVAILABLE:
            available_models.append("claude")

        if self.config.GOOGLE_API_KEY and GOOGLE_AVAILABLE:
            available_models.append("gemini")

        return available_models

    def get_siliconflow_models(self) -> List[str]:
        """获取硅基流动支持的模型列表"""
        return [
            "Qwen/Qwen2.5-7B-Instruct",
            "Qwen/Qwen2.5-14B-Instruct",
            "Qwen/Qwen2.5-32B-Instruct",
            "Qwen/Qwen2.5-72B-Instruct",
            "meta-llama/Meta-Llama-3.1-8B-Instruct",
            "meta-llama/Meta-Llama-3.1-70B-Instruct",
            "meta-llama/Meta-Llama-3.1-405B-Instruct",
            "deepseek-ai/DeepSeek-V2.5",
            "01-ai/Yi-1.5-9B-Chat-16K",
            "01-ai/Yi-1.5-34B-Chat-16K",
            "google/gemma-2-9b-it",
            "google/gemma-2-27b-it",
            "mistralai/Mistral-7B-Instruct-v0.3",
            "mistralai/Mixtral-8x7B-Instruct-v0.1",
            "mistralai/Mixtral-8x22B-Instruct-v0.1"
        ]

    def get_model_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模型的详细信息"""
        model_info = {
            "openai": {
                "name": "OpenAI GPT",
                "description": "OpenAI的GPT系列模型",
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
                "available": bool(self.config.OPENAI_API_KEY)
            },
            "zhipu": {
                "name": "智谱AI",
                "description": "智谱AI的GLM系列模型",
                "models": ["glm-4", "glm-3-turbo"],
                "default_model": self.config.ZHIPU_MODEL,
                "available": bool(self.config.ZHIPU_API_KEY)
            },
            "qwen": {
                "name": "通义千问",
                "description": "阿里云的通义千问系列模型",
                "models": ["qwen-turbo", "qwen-plus", "qwen-max"],
                "default_model": self.config.QWEN_MODEL,
                "available": bool(self.config.QWEN_API_KEY)
            },
            "siliconflow": {
                "name": "硅基流动",
                "description": "硅基流动平台的多种开源模型",
                "models": self.get_siliconflow_models(),
                "default_model": self.config.SILICONFLOW_MODEL,
                "available": bool(self.config.SILICONFLOW_API_KEY)
            },
            "baidu": {
                "name": "百度文心一言",
                "description": "百度的文心一言系列模型",
                "models": ["ernie-bot", "ernie-bot-turbo"],
                "default_model": self.config.BAIDU_MODEL,
                "available": bool(self.config.BAIDU_API_KEY and self.config.BAIDU_SECRET_KEY)
            },
            "moonshot": {
                "name": "Moonshot AI (Kimi)",
                "description": "月之暗面的Kimi系列模型",
                "models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
                "default_model": self.config.MOONSHOT_MODEL,
                "available": bool(self.config.MOONSHOT_API_KEY)
            },
            "deepseek": {
                "name": "DeepSeek",
                "description": "深度求索的DeepSeek系列模型",
                "models": ["deepseek-chat", "deepseek-coder"],
                "default_model": self.config.DEEPSEEK_MODEL,
                "available": bool(self.config.DEEPSEEK_API_KEY)
            },
            "doubao": {
                "name": "字节豆包",
                "description": "字节跳动的豆包系列模型",
                "models": ["doubao-pro", "doubao-lite"],
                "default_model": self.config.DOUBAO_MODEL,
                "available": bool(self.config.DOUBAO_API_KEY)
            },
            "claude": {
                "name": "Anthropic Claude",
                "description": "Anthropic的Claude系列模型",
                "models": ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"],
                "default_model": self.config.ANTHROPIC_MODEL,
                "available": bool(self.config.ANTHROPIC_API_KEY and ANTHROPIC_AVAILABLE)
            },
            "gemini": {
                "name": "Google Gemini",
                "description": "Google的Gemini系列模型",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "default_model": self.config.GOOGLE_MODEL,
                "available": bool(self.config.GOOGLE_API_KEY and GOOGLE_AVAILABLE)
            }
        }

        return model_info
