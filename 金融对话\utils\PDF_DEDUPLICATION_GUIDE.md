# PDF处理器去重功能使用指南

## 概述

PDF处理器现已集成去重功能，可以在处理PDF文件时自动去除重复内容，提高存储效率和检索质量。

## 功能特性

### 🔍 多层次去重
- **PDF文件级去重**：避免重复处理相同的PDF文件
- **文本内容去重**：去除重复的文本块
- **语义去重**：基于相似度的智能去重
- **批量去重**：支持目录级别的批量去重

### ⚡ 高性能设计
- **缓存机制**：避免重复计算
- **流式处理**：支持大文件处理
- **并行处理**：多核CPU加速
- **内存优化**：控制内存使用

## 快速开始

### 1. 基础使用

```python
from rag.pdf_processor import PDFProcessor

# 初始化处理器（自动启用去重功能）
processor = PDFProcessor()

# 处理单个PDF文件（带去重）
success = processor.process_pdf_to_knowledge_base(
    file_path="document.pdf",
    category="金融报告",
    extract_tables=True,
    extract_images=True
)

print(f"处理结果: {'成功' if success else '失败'}")
```

### 2. 跳过去重检查

```python
# 强制处理文件，跳过去重检查
success = processor.process_pdf_to_knowledge_base(
    file_path="document.pdf",
    category="金融报告",
    skip_deduplication=True  # 跳过去重
)
```

### 3. 批量处理目录

```python
# 批量处理目录中的所有PDF文件
results = processor.process_pdf_directory(
    directory_path="pdf_documents/",
    category="金融文档",
    skip_deduplication=False  # 启用去重
)

# 查看处理结果
for filename, success in results.items():
    print(f"{filename}: {'成功' if success else '失败'}")
```

## 去重机制详解

### 1. PDF文件级去重

```python
# 第一次处理 - 正常处理
processor.process_pdf_to_knowledge_base("report.pdf")

# 第二次处理相同文件 - 自动跳过
processor.process_pdf_to_knowledge_base("report.pdf")  # 会被跳过
```

**工作原理：**
- 计算PDF文件的SHA256哈希值
- 检查是否已处理过相同哈希的文件
- 如果是重复文件，直接跳过处理

### 2. 文本内容去重

```python
# 处理过程中自动进行文本去重
# 重复的文本块会被自动去除
processor.process_pdf_to_knowledge_base(
    "document_with_duplicates.pdf",
    category="测试文档"
)
```

**工作原理：**
- 提取所有文本块
- 使用TF-IDF向量化和余弦相似度检测重复
- 去除相似度超过阈值的重复文本块

### 3. 语义去重

系统支持基于语义相似度的智能去重：

```python
# 这些文本会被识别为相似并去重：
# "公司2023年营收增长20%"
# "2023年公司营收增长了20%"
# "该公司在2023年实现营收20%的增长"
```

## 管理去重功能

### 1. 查看去重统计

```python
# 获取处理器统计信息
stats = processor.get_processing_stats()
print(f"去重功能: {'启用' if stats['deduplication_enabled'] else '禁用'}")

# 获取详细去重统计
dedup_stats = processor.get_deduplication_stats()
print(f"文本缓存: {dedup_stats['text_cache_size']} 项")
print(f"PDF缓存: {dedup_stats['pdf_cache_size']} 项")
```

### 2. 缓存管理

```python
# 清空所有去重缓存
processor.clear_deduplication_cache()

# 优化缓存（清理过期数据）
processor.optimize_deduplication_cache()
```

### 3. 功能开关

```python
# 禁用去重功能
processor.disable_deduplication_feature()

# 启用去重功能
processor.enable_deduplication_feature()

# 检查状态
print(f"去重状态: {'启用' if processor.enable_deduplication else '禁用'}")
```

## 配置参数

### 相似度阈值调整

去重功能使用默认的相似度阈值，如需调整：

```python
# 通过去重管理器调整阈值
if processor.dedup_manager:
    # 调整文本相似度阈值（默认0.95）
    processor.dedup_manager.text_deduplicator.similarity_threshold = 0.9
    
    # 调整向量相似度阈值（默认0.98）
    processor.dedup_manager.vector_deduplicator.vector_similarity_threshold = 0.95
```

### 批处理大小

```python
# 调整批处理大小以优化性能
processor.chunk_size = 1500  # 增大文本块大小
processor.chunk_overlap = 300  # 增大重叠大小
```

## 性能优化建议

### 1. 内存优化
- 对于大量PDF文件，建议分批处理
- 定期清理去重缓存释放内存
- 调整文本块大小以平衡性能和质量

### 2. 处理速度优化
- 安装可选的高性能依赖：
  ```bash
  pip install xxhash numba scikit-learn
  ```
- 使用SSD存储缓存文件
- 根据CPU核数调整并行度

### 3. 存储优化
- 定期优化缓存：`processor.optimize_deduplication_cache()`
- 监控缓存大小：`processor.get_deduplication_stats()`
- 必要时清空缓存：`processor.clear_deduplication_cache()`

## 实际应用场景

### 1. 金融报告处理

```python
# 处理年度报告目录
results = processor.process_pdf_directory(
    "annual_reports/",
    category="年度报告"
)

# 自动去除重复的财务数据和标准条款
```

### 2. 法律文档处理

```python
# 处理合同文档
processor.process_pdf_to_knowledge_base(
    "contract.pdf",
    category="法律文档"
)

# 自动去除重复的法律条款
```

### 3. 研究报告处理

```python
# 批量处理研究报告
for report in research_reports:
    processor.process_pdf_to_knowledge_base(
        report,
        category="研究报告",
        extract_tables=True  # 提取数据表格
    )
```

## 监控和维护

### 1. 定期维护脚本

```python
def maintain_pdf_processor():
    """定期维护PDF处理器"""
    processor = PDFProcessor()
    
    # 获取当前状态
    stats = processor.get_deduplication_stats()
    print(f"维护前缓存状态: {stats}")
    
    # 优化缓存
    processor.optimize_deduplication_cache()
    
    # 如果缓存过大，清理部分缓存
    if stats.get('text_cache_size', 0) > 10000:
        processor.clear_deduplication_cache()
        print("缓存已清理")
    
    print("维护完成")

# 建议每周运行一次
maintain_pdf_processor()
```

### 2. 性能监控

```python
import time

def monitor_processing_performance(pdf_files):
    """监控处理性能"""
    processor = PDFProcessor()
    
    start_time = time.time()
    
    for pdf_file in pdf_files:
        file_start = time.time()
        success = processor.process_pdf_to_knowledge_base(pdf_file)
        file_time = time.time() - file_start
        
        print(f"{pdf_file}: {file_time:.2f}秒, {'成功' if success else '失败'}")
    
    total_time = time.time() - start_time
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每文件: {total_time/len(pdf_files):.2f}秒")
```

## 故障排除

### 常见问题

1. **去重功能未启用**
   ```python
   # 检查去重模块是否正确安装
   from utils.dedup_manager import DeduplicationManager
   ```

2. **内存不足**
   ```python
   # 减小批处理大小
   processor.chunk_size = 500
   # 定期清理缓存
   processor.clear_deduplication_cache()
   ```

3. **处理速度慢**
   ```python
   # 安装高性能依赖
   # pip install xxhash numba
   
   # 跳过去重以提高速度
   processor.process_pdf_to_knowledge_base(
       "file.pdf", 
       skip_deduplication=True
   )
   ```

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看去重详细信息
stats = processor.get_deduplication_stats()
print(f"详细统计: {stats}")
```

## 测试验证

运行测试脚本验证功能：

```bash
cd 金融对话
python test_pdf_dedup.py
```

测试将验证：
- PDF文件去重功能
- 文本内容去重功能
- 批量处理功能
- 缓存管理功能

## 总结

PDF处理器的去重功能为金融对话系统提供了：

1. **自动去重**：无需手动干预，自动识别和去除重复内容
2. **性能优化**：减少存储空间，提高检索效率
3. **灵活控制**：支持启用/禁用，可调节相似度阈值
4. **易于维护**：提供完整的缓存管理和监控功能

通过合理使用这些功能，可以显著提高PDF文档处理的效率和质量。
