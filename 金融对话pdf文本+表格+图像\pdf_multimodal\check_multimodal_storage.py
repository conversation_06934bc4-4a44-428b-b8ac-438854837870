"""
PDF多模态图像检索功能检查脚本
检查图像提取、分析和在Milvus中的存储情况
"""
import os
import sys
import json
from pathlib import Path
from typing import Dict, List, Any
import numpy as np

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

def check_multimodal_dependencies():
    """检查多模态功能依赖"""
    print("检查多模态功能依赖...")
    
    required_packages = {
        "torch": "PyTorch深度学习框架",
        "transformers": "HuggingFace Transformers",
        "clip": "OpenAI CLIP模型",
        "PIL": "Python图像处理库",
        "cv2": "OpenCV计算机视觉库",
        "fitz": "PyMuPDF PDF处理库",
        "numpy": "数值计算库"
    }
    
    missing_packages = []
    available_packages = []
    
    for package, description in required_packages.items():
        try:
            if package == "clip":
                import clip
            elif package == "PIL":
                from PIL import Image
            elif package == "cv2":
                import cv2
            elif package == "fitz":
                import fitz
            else:
                __import__(package)
            
            available_packages.append(f"✓ {package}: {description}")
        except ImportError:
            missing_packages.append(f"❌ {package}: {description}")
    
    print("\n可用依赖:")
    for pkg in available_packages:
        print(f"  {pkg}")
    
    if missing_packages:
        print("\n缺失依赖:")
        for pkg in missing_packages:
            print(f"  {pkg}")
        return False
    else:
        print("\n✓ 所有多模态依赖都已安装")
        return True

def check_multimodal_retriever():
    """检查多模态检索器"""
    print("\n检查多模态检索器...")
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        retriever = MultimodalImageRetriever()
        print("✓ 多模态检索器创建成功")
        
        # 检查初始化
        if retriever.initialize():
            print("✓ 多模态检索器初始化成功")
            return retriever
        else:
            print("❌ 多模态检索器初始化失败")
            return None
            
    except Exception as e:
        print(f"❌ 多模态检索器检查失败: {e}")
        return None

def check_image_storage_structure():
    """检查图像存储结构"""
    print("\n检查图像存储结构...")
    
    # 检查图像存储目录
    image_dir = Path("data/images")
    if image_dir.exists():
        print(f"✓ 图像存储目录存在: {image_dir}")
        
        # 统计图像文件
        image_files = list(image_dir.glob("*.png"))
        print(f"✓ 存储的图像文件数量: {len(image_files)}")
        
        if image_files:
            print("  示例图像文件:")
            for i, img_file in enumerate(image_files[:3]):
                print(f"    - {img_file.name}")
                if i >= 2:
                    break
    else:
        print(f"⚠️  图像存储目录不存在: {image_dir}")
    
    # 检查图像索引文件
    index_file = Path("data/image_index.json")
    if index_file.exists():
        print(f"✓ 图像索引文件存在: {index_file}")
        
        try:
            with open(index_file, 'r', encoding='utf-8') as f:
                index_data = json.load(f)
            
            print(f"✓ 索引的PDF文件数量: {len(index_data)}")
            
            total_images = 0
            for file_hash, file_info in index_data.items():
                images = file_info.get("images", [])
                total_images += len(images)
                
                if images:
                    print(f"  PDF: {file_info.get('pdf_name', 'Unknown')} - {len(images)}个图像")
            
            print(f"✓ 总图像数量: {total_images}")
            return index_data
            
        except Exception as e:
            print(f"❌ 读取图像索引失败: {e}")
            return {}
    else:
        print(f"⚠️  图像索引文件不存在: {index_file}")
        return {}

def check_milvus_image_storage():
    """检查Milvus中的图像存储"""
    print("\n检查Milvus中的图像存储...")
    
    try:
        from rag.milvus_manager import MilvusManager
        from pymilvus import utility, Collection
        
        milvus_manager = MilvusManager()
        if not milvus_manager.initialize():
            print("❌ Milvus连接失败")
            return False
        
        print("✓ Milvus连接成功")
        
        # 检查现有集合
        collections = utility.list_collections()
        print(f"✓ 现有集合: {collections}")
        
        # 检查是否有专门的图像集合
        image_collections = [col for col in collections if 'image' in col.lower() or 'multimodal' in col.lower()]
        
        if image_collections:
            print(f"✓ 发现图像相关集合: {image_collections}")
            
            for col_name in image_collections:
                collection = Collection(col_name)
                collection.load()
                count = collection.num_entities
                print(f"  - {col_name}: {count} 条记录")
        else:
            print("⚠️  未发现专门的图像集合")
            print("  图像向量可能存储在文本集合中或使用本地索引")
        
        # 检查知识库集合中是否有图像相关数据
        knowledge_collection = "financial_knowledge"
        if knowledge_collection in collections:
            collection = Collection(knowledge_collection)
            collection.load()
            count = collection.num_entities
            print(f"✓ 知识库集合 {knowledge_collection}: {count} 条记录")
            
            # 尝试查询图像相关记录
            try:
                results = collection.query(
                    expr="category like 'image%' or category like '图像%'",
                    output_fields=["category", "source"],
                    limit=10
                )
                if results:
                    print(f"  发现 {len(results)} 条图像相关记录")
                    for result in results[:3]:
                        print(f"    - 类别: {result.get('category', 'N/A')}, 来源: {result.get('source', 'N/A')}")
                else:
                    print("  未在知识库集合中发现图像相关记录")
            except Exception as e:
                print(f"  查询图像记录时出错: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Milvus检查失败: {e}")
        return False

def analyze_image_processing_pipeline():
    """分析图像处理流水线"""
    print("\n分析图像处理流水线...")
    
    pipeline_steps = [
        "PDF文档 → PyMuPDF → 图像数据 → 格式转换 → 存储管理",
        "图像数据 → CLIP模型 → 向量化 → 相似度计算",
        "图像数据 → BLIP模型 → 描述生成 → 文本分析",
        "向量数据 → 本地索引 → 检索匹配 → 结果排序"
    ]
    
    print("图像处理流水线:")
    for i, step in enumerate(pipeline_steps, 1):
        print(f"  {i}. {step}")
    
    # 检查流水线组件
    components = {
        "PyMuPDF图像提取": "rag.multimodal_retrieval.extract_images_from_pdf",
        "CLIP向量化": "rag.multimodal_retrieval._generate_image_embedding", 
        "BLIP描述生成": "rag.multimodal_retrieval._generate_image_description",
        "图像分类": "rag.multimodal_retrieval._classify_image_type",
        "相似度搜索": "rag.multimodal_retrieval.search_images_by_text"
    }
    
    print("\n流水线组件检查:")
    for component, method in components.items():
        try:
            module_path, method_name = method.rsplit('.', 1)
            module = __import__(module_path.replace('.', '/'), fromlist=[method_name])
            if hasattr(module, method_name.split('.')[-1]):
                print(f"  ✓ {component}: 可用")
            else:
                print(f"  ❌ {component}: 方法不存在")
        except Exception as e:
            print(f"  ❌ {component}: 导入失败 - {e}")

def check_image_retrieval_functionality():
    """检查图像检索功能"""
    print("\n检查图像检索功能...")
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        retriever = MultimodalImageRetriever()
        
        # 检查搜索方法
        search_methods = [
            ("文本搜索图像", "search_images_by_text"),
            ("图像搜索图像", "search_images_by_image"),
            ("图像索引", "index_images"),
            ("图像提取", "extract_images_from_pdf")
        ]
        
        print("检索功能方法:")
        for method_name, method_attr in search_methods:
            if hasattr(retriever, method_attr):
                print(f"  ✓ {method_name}: {method_attr}")
            else:
                print(f"  ❌ {method_name}: {method_attr} 不存在")
        
        # 检查图像索引数据
        if hasattr(retriever, 'image_index') and retriever.image_index:
            print(f"\n✓ 图像索引已加载，包含 {len(retriever.image_index)} 个PDF文件的图像")
            
            # 分析图像类型分布
            image_types = {}
            for file_hash, file_info in retriever.image_index.items():
                for image_info in file_info.get("images", []):
                    img_type = image_info.get("image_type", "未分类")
                    image_types[img_type] = image_types.get(img_type, 0) + 1
            
            if image_types:
                print("  图像类型分布:")
                for img_type, count in image_types.items():
                    print(f"    - {img_type}: {count}个")
        else:
            print("⚠️  图像索引为空或未加载")
        
        return True
        
    except Exception as e:
        print(f"❌ 图像检索功能检查失败: {e}")
        return False

def main():
    """主函数"""
    print("PDF多模态图像检索功能检查")
    print("=" * 60)

    # 检查依赖
    deps_ok = check_multimodal_dependencies()

    # 检查多模态检索器
    retriever = check_multimodal_retriever() if deps_ok else None

    # 检查存储结构
    image_index = check_image_storage_structure()

    # 检查Milvus存储
    milvus_ok = check_milvus_image_storage()

    # 分析处理流水线
    analyze_image_processing_pipeline()

    # 检查检索功能
    retrieval_ok = check_image_retrieval_functionality()

    # 总结
    print("\n" + "=" * 60)
    print("检查结果总结:")

    status_items = [
        ("多模态依赖", "✓ 正常" if deps_ok else "❌ 缺失"),
        ("检索器初始化", "✓ 正常" if retriever else "❌ 失败"),
        ("图像存储结构", "✓ 正常" if image_index else "⚠️  部分缺失"),
        ("Milvus连接", "✓ 正常" if milvus_ok else "❌ 失败"),
        ("检索功能", "✓ 正常" if retrieval_ok else "❌ 异常")
    ]

    for item, status in status_items:
        print(f"  {item}: {status}")

    # 存储方式说明
    print("\n图像数据存储方式:")
    print("  1. 图像文件: 本地存储在 data/images/ 目录")
    print("  2. 图像索引: JSON文件存储在 data/image_index.json")
    print("  3. 向量数据: 当前使用本地内存索引，未直接存储到Milvus")
    print("  4. 检索方式: 基于CLIP向量的余弦相似度计算")

    if not milvus_ok or not image_index:
        print("\n建议:")
        if not image_index:
            print("  - 运行图像提取功能，处理PDF文档生成图像索引")
        if not milvus_ok:
            print("  - 检查Milvus服务是否正常运行")
            print("  - 考虑将图像向量存储到专门的Milvus集合中")

    return deps_ok and retriever and milvus_ok

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断检查")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程中出现错误: {e}")
        sys.exit(1)
