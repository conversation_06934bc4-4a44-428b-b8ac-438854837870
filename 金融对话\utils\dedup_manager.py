"""
集成去重管理器
提供统一的去重接口，整合多种去重策略
"""
import os
import time
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from loguru import logger

from .deduplication import (
    TextDeduplicator, 
    VectorDeduplicator, 
    PDFDeduplicator, 
    HistoryDeduplicator
)


class DeduplicationManager:
    """集成去重管理器"""
    
    def __init__(self, cache_dir: str = "data/dedup_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化各种去重器
        self.text_deduplicator = TextDeduplicator(cache_dir)
        self.vector_deduplicator = VectorDeduplicator(cache_dir)
        self.pdf_deduplicator = PDFDeduplicator(cache_dir)
        self.history_deduplicator = HistoryDeduplicator(cache_dir)
        
        logger.info("去重管理器初始化完成")
    
    def deduplicate_pdf_processing_pipeline(self, pdf_paths: List[str], 
                                          text_chunks: List[str],
                                          vector_data: List[Dict[str, Any]]) -> Tuple[List[str], List[str], List[Dict[str, Any]]]:
        """
        PDF处理流水线去重
        
        Args:
            pdf_paths: PDF文件路径列表
            text_chunks: 文本块列表
            vector_data: 向量数据列表
            
        Returns:
            去重后的 (PDF路径, 文本块, 向量数据)
        """
        logger.info("开始PDF处理流水线去重")
        start_time = time.time()
        
        # 1. PDF文件去重
        unique_pdfs = self.pdf_deduplicator.deduplicate_pdf_list(pdf_paths)
        
        # 2. 文本块去重（使用模糊匹配）
        unique_texts = self.text_deduplicator.deduplicate_texts(text_chunks, use_fuzzy=True)
        
        # 3. 向量数据去重
        unique_vectors = self.vector_deduplicator.deduplicate_vectors(vector_data)
        
        end_time = time.time()
        logger.info(f"PDF处理流水线去重完成，耗时 {end_time - start_time:.2f} 秒")
        logger.info(f"PDF: {len(pdf_paths)} -> {len(unique_pdfs)}")
        logger.info(f"文本: {len(text_chunks)} -> {len(unique_texts)}")
        logger.info(f"向量: {len(vector_data)} -> {len(unique_vectors)}")
        
        return unique_pdfs, unique_texts, unique_vectors
    
    def deduplicate_knowledge_base_data(self, knowledge_data: List[Dict[str, Any]], 
                                      use_content_dedup: bool = True,
                                      use_vector_dedup: bool = True) -> List[Dict[str, Any]]:
        """
        知识库数据去重
        
        Args:
            knowledge_data: 知识库数据列表，每个元素包含 content, embedding 等字段
            use_content_dedup: 是否使用内容去重
            use_vector_dedup: 是否使用向量去重
            
        Returns:
            去重后的知识库数据
        """
        logger.info(f"开始知识库数据去重，原始数据量: {len(knowledge_data)}")
        start_time = time.time()
        
        unique_data = knowledge_data.copy()
        
        # 1. 内容去重
        if use_content_dedup:
            contents = [item.get('content', '') for item in unique_data]
            unique_contents = self.text_deduplicator.deduplicate_texts(contents, use_fuzzy=True)
            
            # 根据去重结果过滤数据
            content_set = set(unique_contents)
            unique_data = [item for item in unique_data if item.get('content', '') in content_set]
            logger.info(f"内容去重后数据量: {len(unique_data)}")
        
        # 2. 向量去重
        if use_vector_dedup:
            unique_data = self.vector_deduplicator.deduplicate_vectors(unique_data)
            logger.info(f"向量去重后数据量: {len(unique_data)}")
        
        end_time = time.time()
        logger.info(f"知识库数据去重完成: {len(knowledge_data)} -> {len(unique_data)}，"
                   f"耗时 {end_time - start_time:.2f} 秒")
        
        return unique_data
    
    def deduplicate_conversation_history(self, conversations: List[Dict[str, Any]], 
                                       time_window_hours: int = 24) -> List[Dict[str, Any]]:
        """
        对话历史去重
        
        Args:
            conversations: 对话数据列表
            time_window_hours: 时间窗口（小时）
            
        Returns:
            去重后的对话数据
        """
        self.history_deduplicator.time_window_hours = time_window_hours
        return self.history_deduplicator.deduplicate_conversations(conversations)
    
    def batch_deduplicate_texts(self, texts: List[str], 
                              batch_size: int = 1000,
                              use_fuzzy: bool = True) -> List[str]:
        """
        批量去重大量文本（分批处理以节省内存）
        
        Args:
            texts: 文本列表
            batch_size: 批处理大小
            use_fuzzy: 是否使用模糊匹配
            
        Returns:
            去重后的文本列表
        """
        logger.info(f"开始批量去重 {len(texts)} 个文本，批大小: {batch_size}")
        
        unique_texts = []
        total_processed = 0
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            batch_unique = self.text_deduplicator.deduplicate_texts(batch, use_fuzzy=use_fuzzy)
            unique_texts.extend(batch_unique)
            
            total_processed += len(batch)
            logger.info(f"已处理 {total_processed}/{len(texts)} 个文本")
        
        logger.info(f"批量去重完成: {len(texts)} -> {len(unique_texts)}")
        return unique_texts
    
    def get_deduplication_stats(self) -> Dict[str, Any]:
        """获取去重统计信息"""
        stats = {
            "text_cache_size": len(self.text_deduplicator.text_hashes),
            "vector_cache_size": len(self.vector_deduplicator.vector_hashes),
            "pdf_cache_size": len(self.pdf_deduplicator.pdf_hashes),
            "history_cache_size": len(self.history_deduplicator.history_hashes),
            "cache_directory": str(self.cache_dir),
            "cache_files": []
        }
        
        # 获取缓存文件信息
        for cache_file in self.cache_dir.glob("*.pkl"):
            try:
                file_size = cache_file.stat().st_size
                stats["cache_files"].append({
                    "name": cache_file.name,
                    "size_bytes": file_size,
                    "size_mb": round(file_size / 1024 / 1024, 2)
                })
            except Exception as e:
                logger.warning(f"获取缓存文件信息失败 {cache_file}: {e}")
        
        return stats
    
    def clear_all_caches(self):
        """清空所有缓存"""
        logger.info("开始清空所有去重缓存")
        
        self.text_deduplicator.clear_cache()
        self.vector_deduplicator.clear_cache()
        self.pdf_deduplicator.clear_cache()
        self.history_deduplicator.clear_cache()
        
        logger.info("所有去重缓存已清空")
    
    def optimize_caches(self):
        """优化缓存（清理过期数据等）"""
        logger.info("开始优化去重缓存")
        
        # 清理历史对话过期数据
        self.history_deduplicator._cleanup_expired_hashes()
        
        # 限制文本缓存大小
        if hasattr(self.text_deduplicator, 'text_contents'):
            if len(self.text_deduplicator.text_contents) > 1000:
                self.text_deduplicator.text_contents = self.text_deduplicator.text_contents[-1000:]
                logger.info("文本内容缓存已优化")
        
        logger.info("缓存优化完成")
    
    def export_cache_info(self, output_file: str = "dedup_cache_info.json"):
        """导出缓存信息到文件"""
        import json
        
        stats = self.get_deduplication_stats()
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            logger.info(f"缓存信息已导出到: {output_file}")
        except Exception as e:
            logger.error(f"导出缓存信息失败: {e}")


# 全局去重管理器实例
dedup_manager = DeduplicationManager()


# 便捷函数
def quick_deduplicate_texts(texts: List[str], use_fuzzy: bool = True) -> List[str]:
    """快速去重文本"""
    return dedup_manager.text_deduplicator.deduplicate_texts(texts, use_fuzzy=use_fuzzy)


def quick_deduplicate_vectors(vector_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """快速去重向量数据"""
    return dedup_manager.vector_deduplicator.deduplicate_vectors(vector_data)


def quick_deduplicate_pdfs(pdf_paths: List[str]) -> List[str]:
    """快速去重PDF文件"""
    return dedup_manager.pdf_deduplicator.deduplicate_pdf_list(pdf_paths)


def get_dedup_stats() -> Dict[str, Any]:
    """获取去重统计信息"""
    return dedup_manager.get_deduplication_stats()


# 示例使用
if __name__ == "__main__":
    # 创建测试数据
    test_texts = ["文本1", "文本1", "文本2", "文本3"]
    test_vectors = [
        {"content": "内容1", "embedding": [0.1, 0.2, 0.3]},
        {"content": "内容1", "embedding": [0.1, 0.2, 0.3]},  # 重复
        {"content": "内容2", "embedding": [0.4, 0.5, 0.6]}
    ]
    
    # 测试去重
    manager = DeduplicationManager()
    
    unique_texts = manager.batch_deduplicate_texts(test_texts)
    print(f"文本去重: {len(test_texts)} -> {len(unique_texts)}")
    
    unique_vectors = manager.vector_deduplicator.deduplicate_vectors(test_vectors)
    print(f"向量去重: {len(test_vectors)} -> {len(unique_vectors)}")
    
    # 显示统计信息
    stats = manager.get_deduplication_stats()
    print("去重统计:", stats)
