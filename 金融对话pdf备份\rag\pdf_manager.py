"""
PDF管理服务
提供PDF文件的上传、处理、管理和检索功能
"""
import os
import sys
import json
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.pdf_processor import PDFProcessor
from rag.rag_system import RAGSystem
from idconfig.config import Config

class PDFManager:
    """PDF管理服务"""
    
    def __init__(self):
        self.config = Config()
        self.pdf_processor = PDFProcessor()
        self.rag_system = RAGSystem()
        
        # PDF存储目录
        self.pdf_storage_dir = Path("data/pdfs")
        self.pdf_storage_dir.mkdir(parents=True, exist_ok=True)
        
        # 元数据存储文件
        self.metadata_file = Path("data/pdf_metadata.json")
        self.metadata_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载已有元数据
        self.pdf_metadata = self._load_metadata()
    
    def initialize(self):
        """初始化PDF管理器"""
        try:
            # 初始化PDF处理器
            if not self.pdf_processor.initialize():
                logger.error("PDF处理器初始化失败")
                return False
            
            logger.info("PDF管理器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"PDF管理器初始化失败: {e}")
            return False
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载PDF元数据"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"加载PDF元数据失败: {e}")
            return {}
    
    def _save_metadata(self):
        """保存PDF元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.pdf_metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存PDF元数据失败: {e}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败: {e}")
            return ""
    
    def upload_pdf(self, file_path: str, category: str = "金融文档", 
                   description: str = "", tags: List[str] = None) -> Dict[str, Any]:
        """
        上传PDF文件到系统
        
        Args:
            file_path: PDF文件路径
            category: 文档分类
            description: 文档描述
            tags: 标签列表
        
        Returns:
            上传结果
        """
        try:
            if not os.path.exists(file_path):
                return {"success": False, "message": "文件不存在"}
            
            if not file_path.lower().endswith('.pdf'):
                return {"success": False, "message": "文件不是PDF格式"}
            
            # 计算文件哈希
            file_hash = self._calculate_file_hash(file_path)
            if not file_hash:
                return {"success": False, "message": "计算文件哈希失败"}
            
            # 检查是否已存在
            if file_hash in self.pdf_metadata:
                return {
                    "success": False, 
                    "message": "文件已存在", 
                    "existing_file": self.pdf_metadata[file_hash]
                }
            
            # 复制文件到存储目录
            file_name = os.path.basename(file_path)
            stored_path = self.pdf_storage_dir / f"{file_hash}_{file_name}"
            
            import shutil
            shutil.copy2(file_path, stored_path)
            
            # 记录元数据
            metadata = {
                "file_hash": file_hash,
                "original_name": file_name,
                "stored_path": str(stored_path),
                "category": category,
                "description": description,
                "tags": tags or [],
                "upload_time": datetime.now().isoformat(),
                "file_size": os.path.getsize(file_path),
                "processed": False,
                "processing_time": None,
                "chunk_count": 0
            }
            
            self.pdf_metadata[file_hash] = metadata
            self._save_metadata()
            
            logger.info(f"PDF文件上传成功: {file_name}")
            return {"success": True, "message": "文件上传成功", "file_hash": file_hash}
            
        except Exception as e:
            logger.error(f"上传PDF文件失败: {e}")
            return {"success": False, "message": f"上传失败: {str(e)}"}
    
    def process_uploaded_pdf(self, file_hash: str) -> Dict[str, Any]:
        """
        处理已上传的PDF文件
        
        Args:
            file_hash: 文件哈希值
        
        Returns:
            处理结果
        """
        try:
            if file_hash not in self.pdf_metadata:
                return {"success": False, "message": "文件不存在"}
            
            metadata = self.pdf_metadata[file_hash]
            
            if metadata["processed"]:
                return {"success": False, "message": "文件已处理过"}
            
            # 处理PDF文件（包含图像提取）
            start_time = datetime.now()
            success = self.pdf_processor.process_pdf_to_knowledge_base(
                metadata["stored_path"],
                metadata["category"],
                extract_tables=True,
                extract_images=True
            )
            
            if success:
                # 更新元数据
                metadata["processed"] = True
                metadata["processing_time"] = datetime.now().isoformat()
                # 这里可以添加更详细的处理统计信息
                
                self._save_metadata()
                
                logger.info(f"PDF文件处理成功: {metadata['original_name']}")
                return {"success": True, "message": "文件处理成功"}
            else:
                return {"success": False, "message": "文件处理失败"}
                
        except Exception as e:
            logger.error(f"处理PDF文件失败: {e}")
            return {"success": False, "message": f"处理失败: {str(e)}"}
    
    def upload_and_process_pdf(self, file_path: str, category: str = "金融文档", 
                              description: str = "", tags: List[str] = None) -> Dict[str, Any]:
        """
        上传并处理PDF文件（一步完成）
        
        Args:
            file_path: PDF文件路径
            category: 文档分类
            description: 文档描述
            tags: 标签列表
        
        Returns:
            处理结果
        """
        try:
            # 1. 上传文件
            upload_result = self.upload_pdf(file_path, category, description, tags)
            if not upload_result["success"]:
                return upload_result
            
            file_hash = upload_result["file_hash"]
            
            # 2. 处理文件
            process_result = self.process_uploaded_pdf(file_hash)
            
            return {
                "success": process_result["success"],
                "message": f"上传并处理完成: {process_result['message']}",
                "file_hash": file_hash
            }
            
        except Exception as e:
            logger.error(f"上传并处理PDF文件失败: {e}")
            return {"success": False, "message": f"操作失败: {str(e)}"}
    
    def list_pdfs(self, category: str = None, processed_only: bool = False) -> List[Dict[str, Any]]:
        """
        列出PDF文件
        
        Args:
            category: 筛选分类
            processed_only: 只显示已处理的文件
        
        Returns:
            PDF文件列表
        """
        try:
            pdf_list = []
            
            for file_hash, metadata in self.pdf_metadata.items():
                # 分类筛选
                if category and metadata.get("category") != category:
                    continue
                
                # 处理状态筛选
                if processed_only and not metadata.get("processed", False):
                    continue
                
                pdf_info = {
                    "file_hash": file_hash,
                    "name": metadata.get("original_name", ""),
                    "category": metadata.get("category", ""),
                    "description": metadata.get("description", ""),
                    "tags": metadata.get("tags", []),
                    "upload_time": metadata.get("upload_time", ""),
                    "processed": metadata.get("processed", False),
                    "file_size": metadata.get("file_size", 0),
                    "chunk_count": metadata.get("chunk_count", 0)
                }
                pdf_list.append(pdf_info)
            
            # 按上传时间排序
            pdf_list.sort(key=lambda x: x["upload_time"], reverse=True)
            
            return pdf_list
            
        except Exception as e:
            logger.error(f"列出PDF文件失败: {e}")
            return []
    
    def delete_pdf(self, file_hash: str) -> Dict[str, Any]:
        """
        删除PDF文件
        
        Args:
            file_hash: 文件哈希值
        
        Returns:
            删除结果
        """
        try:
            if file_hash not in self.pdf_metadata:
                return {"success": False, "message": "文件不存在"}
            
            metadata = self.pdf_metadata[file_hash]
            
            # 删除存储的文件
            stored_path = Path(metadata["stored_path"])
            if stored_path.exists():
                stored_path.unlink()
            
            # 删除元数据
            del self.pdf_metadata[file_hash]
            self._save_metadata()
            
            logger.info(f"PDF文件删除成功: {metadata['original_name']}")
            return {"success": True, "message": "文件删除成功"}
            
        except Exception as e:
            logger.error(f"删除PDF文件失败: {e}")
            return {"success": False, "message": f"删除失败: {str(e)}"}
    
    def search_pdfs(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        在PDF知识库中搜索
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
        
        Returns:
            搜索结果
        """
        try:
            # 使用RAG系统搜索
            results = self.rag_system.search_knowledge(query, top_k)
            
            # 增强结果信息
            enhanced_results = []
            for result in results:
                # 尝试从source字段提取文件信息
                source = result.get("source", "")
                file_info = self._extract_file_info_from_source(source)
                
                enhanced_result = {
                    **result,
                    "file_info": file_info
                }
                enhanced_results.append(enhanced_result)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"搜索PDF知识库失败: {e}")
            return []
    
    def _extract_file_info_from_source(self, source: str) -> Dict[str, Any]:
        """从source字段提取文件信息"""
        try:
            # 简单的文件信息提取逻辑
            # 可以根据实际的source格式进行调整
            if " - " in source:
                file_name = source.split(" - ")[0]
                # 查找对应的元数据
                for file_hash, metadata in self.pdf_metadata.items():
                    if metadata.get("original_name") == file_name:
                        return {
                            "file_hash": file_hash,
                            "file_name": file_name,
                            "category": metadata.get("category", ""),
                            "upload_time": metadata.get("upload_time", "")
                        }
            
            return {"file_name": source, "category": "", "upload_time": ""}
            
        except Exception as e:
            logger.error(f"提取文件信息失败: {e}")
            return {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            total_files = len(self.pdf_metadata)
            processed_files = sum(1 for m in self.pdf_metadata.values() if m.get("processed", False))
            
            # 按分类统计
            category_stats = {}
            for metadata in self.pdf_metadata.values():
                category = metadata.get("category", "未分类")
                category_stats[category] = category_stats.get(category, 0) + 1
            
            # 计算总文件大小
            total_size = sum(m.get("file_size", 0) for m in self.pdf_metadata.values())
            
            stats = {
                "total_files": total_files,
                "processed_files": processed_files,
                "unprocessed_files": total_files - processed_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "category_distribution": category_stats,
                "storage_directory": str(self.pdf_storage_dir)
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
