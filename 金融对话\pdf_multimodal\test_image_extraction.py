"""
测试PDF图像提取功能
演示从PDF文档中提取图像并建立索引
"""
import os
import sys
from pathlib import Path
import hashlib

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

def calculate_file_hash(file_path: str) -> str:
    """计算文件哈希值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def test_image_extraction():
    """测试图像提取功能"""
    print("测试PDF图像提取功能")
    print("=" * 50)
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        # 初始化检索器
        retriever = MultimodalImageRetriever()
        if not retriever.initialize():
            print("❌ 多模态检索器初始化失败")
            return False
        
        print("✓ 多模态检索器初始化成功")
        
        # 查找PDF文件
        pdf_dir = Path("data/pdfs")
        if not pdf_dir.exists():
            print(f"❌ PDF目录不存在: {pdf_dir}")
            return False
        
        pdf_files = list(pdf_dir.glob("*.pdf"))
        if not pdf_files:
            print(f"❌ 在 {pdf_dir} 中未找到PDF文件")
            return False
        
        print(f"✓ 找到 {len(pdf_files)} 个PDF文件")
        
        # 选择第一个PDF文件进行测试
        test_pdf = pdf_files[0]
        print(f"\n测试文件: {test_pdf.name}")
        
        # 计算文件哈希
        file_hash = calculate_file_hash(str(test_pdf))
        print(f"文件哈希: {file_hash}")
        
        # 提取图像
        print("\n开始提取图像...")
        images_info = retriever.extract_images_from_pdf(str(test_pdf), file_hash)
        
        if images_info:
            print(f"✓ 成功提取 {len(images_info)} 个图像")
            
            # 显示图像信息
            for i, img_info in enumerate(images_info[:3]):  # 只显示前3个
                print(f"\n图像 {i+1}:")
                print(f"  - 页码: {img_info.get('page_number', 'N/A')}")
                print(f"  - 尺寸: {img_info.get('width', 'N/A')}x{img_info.get('height', 'N/A')}")
                print(f"  - 类型: {img_info.get('image_type', 'N/A')}")
                print(f"  - 描述: {img_info.get('description', 'N/A')[:100]}...")
                print(f"  - 路径: {img_info.get('image_path', 'N/A')}")
            
            if len(images_info) > 3:
                print(f"\n... 还有 {len(images_info) - 3} 个图像")
            
            # 建立索引
            print(f"\n建立图像索引...")
            success = retriever.index_images(str(test_pdf), file_hash)
            
            if success:
                print("✓ 图像索引建立成功")
                
                # 测试文本搜索
                print("\n测试文本搜索图像...")
                search_queries = [
                    "财务图表",
                    "柱状图",
                    "数据图表",
                    "financial chart"
                ]
                
                for query in search_queries:
                    results = retriever.search_images_by_text(query, top_k=3)
                    if results:
                        print(f"  查询 '{query}': 找到 {len(results)} 个相关图像")
                        for result in results[:1]:  # 只显示第一个结果
                            print(f"    - 相似度: {result.get('final_score', 0):.3f}")
                            print(f"    - 页码: {result.get('page_number', 'N/A')}")
                            print(f"    - 类型: {result.get('image_type', 'N/A')}")
                    else:
                        print(f"  查询 '{query}': 未找到相关图像")
                
                return True
            else:
                print("❌ 图像索引建立失败")
                return False
        else:
            print("⚠️  未从PDF中提取到图像")
            print("  可能原因:")
            print("  1. PDF中没有图像")
            print("  2. 图像尺寸太小被过滤")
            print("  3. 图像格式不支持")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("\n" + "=" * 50)
    print("测试图像搜索功能")
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        retriever = MultimodalImageRetriever()
        
        # 检查是否有图像索引
        if not retriever.image_index:
            print("⚠️  没有图像索引数据，请先运行图像提取")
            return False
        
        print(f"✓ 加载了 {len(retriever.image_index)} 个PDF的图像索引")
        
        # 统计总图像数
        total_images = 0
        for file_hash, file_info in retriever.image_index.items():
            total_images += len(file_info.get("images", []))
        
        print(f"✓ 总共索引了 {total_images} 个图像")
        
        # 测试各种搜索查询
        test_queries = [
            "图表",
            "数据",
            "财务",
            "chart",
            "graph",
            "table",
            "柱状图",
            "折线图",
            "饼图"
        ]
        
        print("\n搜索测试结果:")
        for query in test_queries:
            results = retriever.search_images_by_text(query, top_k=5)
            if results:
                print(f"  '{query}': {len(results)} 个结果")
                # 显示最佳匹配
                best_result = results[0]
                print(f"    最佳匹配: 得分={best_result.get('final_score', 0):.3f}, "
                      f"类型={best_result.get('image_type', 'N/A')}")
            else:
                print(f"  '{query}': 无结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索测试失败: {e}")
        return False

def main():
    """主函数"""
    print("PDF多模态图像提取和检索测试")
    print("=" * 60)
    
    # 测试图像提取
    extraction_success = test_image_extraction()
    
    # 测试搜索功能
    search_success = test_search_functionality()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"  图像提取: {'✓ 成功' if extraction_success else '❌ 失败'}")
    print(f"  搜索功能: {'✓ 成功' if search_success else '❌ 失败'}")
    
    if extraction_success:
        print("\n✓ PDF图像提取和索引功能正常工作")
        print("  - 图像文件存储在: data/images/")
        print("  - 图像索引存储在: data/image_index.json")
        print("  - 支持基于文本描述的图像搜索")
        print("  - 使用CLIP模型进行向量化")
        print("  - 使用BLIP模型生成图像描述")
    else:
        print("\n❌ 图像提取功能存在问题")
        print("建议:")
        print("  1. 检查PDF文件是否包含图像")
        print("  2. 确认多模态依赖是否正确安装")
        print("  3. 检查模型加载是否成功")
    
    return extraction_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        sys.exit(1)
