"""
PDF处理API端点
提供PDF文件上传、处理、管理和检索的API接口
"""
import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Query
from pydantic import BaseModel
from loguru import logger

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.pdf_manager import PDFManager
from rag.table_retrieval_optimizer import TableRetrievalOptimizer

# 创建路由器
router = APIRouter(prefix="/pdf", tags=["PDF管理"])

# 全局PDF管理器实例
pdf_manager = None
table_optimizer = None

def get_pdf_manager():
    """获取PDF管理器实例"""
    global pdf_manager
    if pdf_manager is None:
        pdf_manager = PDFManager()
        if not pdf_manager.initialize():
            raise HTTPException(status_code=500, detail="PDF管理器初始化失败")
    return pdf_manager

def get_table_optimizer():
    """获取表格优化器实例"""
    global table_optimizer
    if table_optimizer is None:
        table_optimizer = TableRetrievalOptimizer()
        if not table_optimizer.initialize():
            raise HTTPException(status_code=500, detail="表格优化器初始化失败")
    return table_optimizer

# Pydantic模型
class PDFUploadResponse(BaseModel):
    success: bool
    message: str
    file_hash: Optional[str] = None

class PDFProcessResponse(BaseModel):
    success: bool
    message: str

class PDFInfo(BaseModel):
    file_hash: str
    name: str
    category: str
    description: str
    tags: List[str]
    upload_time: str
    processed: bool
    file_size: int
    chunk_count: int

class PDFListResponse(BaseModel):
    pdfs: List[PDFInfo]
    total_count: int

class SearchResult(BaseModel):
    content: str
    category: str
    source: str
    score: float
    file_info: Dict[str, Any]

class SearchResponse(BaseModel):
    results: List[SearchResult]
    query: str
    total_results: int

class StatisticsResponse(BaseModel):
    total_files: int
    processed_files: int
    unprocessed_files: int
    total_size_bytes: int
    total_size_mb: float
    category_distribution: Dict[str, int]
    storage_directory: str

class TableSearchResult(BaseModel):
    table_id: str
    file_name: str
    page_number: int
    table_number: int
    table_type: str
    similarity: float
    summary: str
    matched_content: List[str]
    columns: List[str]
    relevance_explanation: str
    suggested_queries: List[str]

class TableSearchResponse(BaseModel):
    results: List[TableSearchResult]
    query: str
    total_results: int
    search_time_ms: float

class TableStatisticsResponse(BaseModel):
    total_files: int
    total_tables: int
    table_types: Dict[str, int]
    column_distribution: Dict[str, int]
    feature_distribution: Dict[str, int]

@router.post("/upload", response_model=PDFUploadResponse)
async def upload_pdf(
    file: UploadFile = File(...),
    category: str = Form("金融文档"),
    description: str = Form(""),
    tags: str = Form("")  # 逗号分隔的标签字符串
):
    """
    上传PDF文件
    """
    try:
        # 检查文件类型
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")
        
        # 创建临时文件
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        temp_file_path = temp_dir / file.filename
        
        # 保存上传的文件
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 处理标签
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
        
        # 上传到PDF管理器
        manager = get_pdf_manager()
        result = manager.upload_pdf(
            str(temp_file_path),
            category=category,
            description=description,
            tags=tag_list
        )
        
        # 清理临时文件
        if temp_file_path.exists():
            temp_file_path.unlink()
        
        return PDFUploadResponse(**result)
        
    except Exception as e:
        logger.error(f"上传PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")

@router.post("/process/{file_hash}", response_model=PDFProcessResponse)
async def process_pdf(file_hash: str):
    """
    处理已上传的PDF文件
    """
    try:
        manager = get_pdf_manager()
        result = manager.process_uploaded_pdf(file_hash)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return PDFProcessResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.post("/upload-and-process", response_model=PDFProcessResponse)
async def upload_and_process_pdf(
    file: UploadFile = File(...),
    category: str = Form("金融文档"),
    description: str = Form(""),
    tags: str = Form(""),
    extract_images: bool = Form(True)
):
    """
    上传并处理PDF文件（一步完成）

    参数:
    - file: PDF文件
    - category: 文档分类（默认：金融文档）
    - description: 文档描述
    - tags: 标签（逗号分隔）
    - extract_images: 是否提取图像（默认：True）
    """
    try:
        # 检查文件类型
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")
        
        # 创建临时文件
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        temp_file_path = temp_dir / file.filename
        
        # 保存上传的文件
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # 处理标签
        tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()] if tags else []
        
        # 上传并处理
        manager = get_pdf_manager()
        result = manager.upload_and_process_pdf(
            str(temp_file_path),
            category=category,
            description=description,
            tags=tag_list
        )
        
        # 清理临时文件
        if temp_file_path.exists():
            temp_file_path.unlink()
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return PDFProcessResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传并处理PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")

@router.get("/list", response_model=PDFListResponse)
async def list_pdfs(
    category: Optional[str] = Query(None, description="筛选分类"),
    processed_only: bool = Query(False, description="只显示已处理的文件")
):
    """
    列出PDF文件
    """
    try:
        manager = get_pdf_manager()
        pdfs = manager.list_pdfs(category=category, processed_only=processed_only)
        
        pdf_infos = [PDFInfo(**pdf) for pdf in pdfs]
        
        return PDFListResponse(
            pdfs=pdf_infos,
            total_count=len(pdf_infos)
        )
        
    except Exception as e:
        logger.error(f"列出PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取列表失败: {str(e)}")

@router.delete("/delete/{file_hash}")
async def delete_pdf(file_hash: str):
    """
    删除PDF文件
    """
    try:
        manager = get_pdf_manager()
        result = manager.delete_pdf(file_hash)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
        
        return {"message": result["message"]}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除PDF文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/search", response_model=SearchResponse)
async def search_pdfs(
    query: str = Query(..., description="搜索查询"),
    top_k: int = Query(5, description="返回结果数量", ge=1, le=20)
):
    """
    在PDF知识库中搜索
    """
    try:
        manager = get_pdf_manager()
        results = manager.search_pdfs(query, top_k)
        
        search_results = [SearchResult(**result) for result in results]
        
        return SearchResponse(
            results=search_results,
            query=query,
            total_results=len(search_results)
        )
        
    except Exception as e:
        logger.error(f"搜索PDF知识库失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/statistics", response_model=StatisticsResponse)
async def get_statistics():
    """
    获取PDF管理统计信息
    """
    try:
        manager = get_pdf_manager()
        stats = manager.get_statistics()
        
        return StatisticsResponse(**stats)
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")



# 表格检索API端点
@router.get("/tables/search", response_model=TableSearchResponse)
async def search_tables(
    query: str = Query(..., description="搜索查询"),
    table_type: Optional[str] = Query(None, description="表格类型筛选"),
    top_k: int = Query(5, description="返回结果数量", ge=1, le=20)
):
    """
    在表格索引中搜索
    """
    try:
        import time
        start_time = time.time()

        optimizer = get_table_optimizer()
        results = optimizer.search_tables(query, table_type, top_k)

        search_time = (time.time() - start_time) * 1000

        table_results = [TableSearchResult(**result) for result in results]

        return TableSearchResponse(
            results=table_results,
            query=query,
            total_results=len(table_results),
            search_time_ms=round(search_time, 2)
        )

    except Exception as e:
        logger.error(f"搜索表格失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.post("/tables/extract/{file_hash}")
async def extract_tables_from_pdf(file_hash: str):
    """
    从已上传的PDF中提取并索引表格
    """
    try:
        manager = get_pdf_manager()
        optimizer = get_table_optimizer()

        # 获取文件信息
        pdf_list = manager.list_pdfs()
        file_info = None
        for pdf in pdf_list:
            if pdf["file_hash"] == file_hash:
                file_info = pdf
                break

        if not file_info:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 获取文件路径
        file_path = None
        for hash_key, metadata in manager.pdf_metadata.items():
            if hash_key == file_hash:
                file_path = metadata["stored_path"]
                break

        if not file_path:
            raise HTTPException(status_code=404, detail="文件路径不存在")

        # 提取并索引表格
        success = optimizer.extract_and_index_tables(file_path, file_hash)

        if success:
            return {"message": "表格提取和索引成功", "file_hash": file_hash}
        else:
            return {"message": "表格提取失败或未找到表格", "file_hash": file_hash}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提取表格失败: {e}")
        raise HTTPException(status_code=500, detail=f"提取失败: {str(e)}")

@router.get("/tables/statistics", response_model=TableStatisticsResponse)
async def get_table_statistics():
    """
    获取表格统计信息
    """
    try:
        optimizer = get_table_optimizer()
        stats = optimizer.get_table_statistics()

        return TableStatisticsResponse(**stats)

    except Exception as e:
        logger.error(f"获取表格统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/tables/optimize-search")
async def optimize_table_search(
    query: str = Query(..., description="搜索查询"),
    include_context: bool = Query(False, description="是否包含上下文优化")
):
    """
    优化的表格搜索
    """
    try:
        optimizer = get_table_optimizer()

        context = None
        if include_context:
            # 这里可以从RAG系统获取上下文信息
            context = {"history": [], "knowledge": []}

        results = optimizer.optimize_table_search(query, context)

        return {
            "query": query,
            "results": results,
            "total_results": len(results),
            "optimization_applied": include_context
        }

    except Exception as e:
        logger.error(f"优化表格搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# 健康检查端点
@router.get("/health")
async def health_check():
    """
    PDF服务健康检查
    """
    try:
        manager = get_pdf_manager()
        stats = manager.get_statistics()

        # 检查表格优化器
        table_status = "not_initialized"
        try:
            optimizer = get_table_optimizer()
            table_stats = optimizer.get_table_statistics()
            table_status = "initialized"
        except:
            table_stats = {}

        return {
            "status": "healthy",
            "pdf_manager": "initialized",
            "table_optimizer": table_status,
            "total_files": stats.get("total_files", 0),
            "total_tables": table_stats.get("total_tables", 0)
        }

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
