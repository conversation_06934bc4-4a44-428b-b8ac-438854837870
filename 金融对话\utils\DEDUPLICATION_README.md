# 去重处理模块使用指南

## 概述

本模块提供了高效的去重处理功能，专门为金融对话系统设计，支持多种数据类型的去重处理，包括文本、向量、PDF文件和对话历史。

## 功能特性

### 🚀 高性能设计
- **多线程并行处理**：支持多核CPU加速
- **内存优化**：流式处理大数据集
- **缓存机制**：避免重复计算
- **批处理支持**：高效处理大量数据

### 📊 多种去重策略
- **精确匹配**：基于哈希的快速去重
- **模糊匹配**：基于TF-IDF和余弦相似度的语义去重
- **向量去重**：针对embedding向量的相似度去重
- **文件去重**：基于文件内容哈希的PDF去重

### 🛠️ 易用接口
- **便捷函数**：一行代码完成去重
- **集成管理器**：统一管理多种去重任务
- **配置灵活**：可调节相似度阈值等参数

## 安装依赖

### 基础依赖
```bash
pip install loguru numpy
```

### 可选依赖（提升性能）
```bash
# 高性能哈希库
pip install xxhash

# 机器学习库（用于语义相似度）
pip install scikit-learn

# 数值计算加速
pip install numba
```

## 快速开始

### 1. 文本去重

```python
from utils.deduplication import deduplicate_texts

# 基础文本去重
texts = ["文本1", "文本1", "文本2", "文本3"]
unique_texts = deduplicate_texts(texts)
print(f"去重结果: {len(texts)} -> {len(unique_texts)}")

# 模糊去重（语义相似度）
unique_texts_fuzzy = deduplicate_texts(texts, use_fuzzy=True, similarity_threshold=0.9)
```

### 2. 向量数据去重

```python
from utils.deduplication import deduplicate_vectors

vector_data = [
    {"content": "内容1", "embedding": [0.1, 0.2, 0.3], "category": "金融"},
    {"content": "内容1", "embedding": [0.1, 0.2, 0.3], "category": "金融"},  # 重复
    {"content": "内容2", "embedding": [0.4, 0.5, 0.6], "category": "投资"}
]

unique_vectors = deduplicate_vectors(vector_data, similarity_threshold=0.98)
```

### 3. PDF文件去重

```python
from utils.deduplication import deduplicate_pdfs

pdf_files = ["doc1.pdf", "doc2.pdf", "doc1.pdf"]  # 包含重复
unique_pdfs = deduplicate_pdfs(pdf_files)
```

### 4. 对话历史去重

```python
from utils.deduplication import deduplicate_conversations

conversations = [
    {
        "user_query": "什么是股票？",
        "assistant_response": "股票是公司所有权的证明...",
        "timestamp": 1234567890
    },
    # ... 更多对话
]

unique_conversations = deduplicate_conversations(conversations, time_window_hours=24)
```

## 高级用法

### 使用集成管理器

```python
from utils.dedup_manager import DeduplicationManager

manager = DeduplicationManager()

# PDF处理流水线去重
pdf_paths = ["file1.pdf", "file2.pdf"]
text_chunks = ["文本块1", "文本块2", "文本块1"]
vector_data = [{"content": "内容", "embedding": [0.1, 0.2]}]

unique_pdfs, unique_texts, unique_vectors = manager.deduplicate_pdf_processing_pipeline(
    pdf_paths, text_chunks, vector_data
)

# 知识库数据去重
knowledge_data = [
    {"content": "知识1", "embedding": [0.1, 0.2], "category": "金融"},
    # ... 更多知识
]

unique_knowledge = manager.deduplicate_knowledge_base_data(
    knowledge_data, 
    use_content_dedup=True, 
    use_vector_dedup=True
)

# 获取统计信息
stats = manager.get_deduplication_stats()
print(f"缓存统计: {stats}")
```

### 高性能去重

```python
from utils.fast_dedup import fast_deduplicate_texts, fast_deduplicate_vectors

# 快速文本去重（适合大数据量）
large_texts = ["文本"] * 10000
unique_texts = fast_deduplicate_texts(large_texts, use_parallel=True)

# 快速向量去重
large_vectors = [{"content": f"内容{i}", "embedding": [i*0.1]} for i in range(5000)]
unique_vectors = fast_deduplicate_vectors(large_vectors, similarity_threshold=0.98)
```

### 批处理大数据

```python
from utils.dedup_manager import dedup_manager

# 流式处理大量文本
huge_texts = ["文本"] * 100000
unique_texts = dedup_manager.batch_deduplicate_texts(
    huge_texts, 
    batch_size=1000,  # 每批处理1000个
    use_fuzzy=True
)
```

## 性能优化建议

### 1. 选择合适的去重策略
- **精确去重**：速度最快，适合完全相同的内容
- **模糊去重**：较慢但更智能，适合语义相似的内容
- **向量去重**：适合已有embedding的数据

### 2. 调整参数
```python
# 相似度阈值调整
similarity_threshold = 0.95  # 更严格的去重
similarity_threshold = 0.85  # 更宽松的去重

# 批处理大小调整
batch_size = 500   # 内存较小时
batch_size = 2000  # 内存充足时
```

### 3. 使用缓存
```python
# 缓存会自动保存，重启程序后仍然有效
# 定期清理缓存以释放空间
dedup_manager.clear_all_caches()

# 优化缓存
dedup_manager.optimize_caches()
```

## 集成到现有系统

### 1. PDF处理集成

```python
# 在pdf_processor.py中集成
from utils.dedup_manager import dedup_manager

class PDFProcessor:
    def process_pdf_with_dedup(self, pdf_path: str):
        # 检查PDF是否重复
        if dedup_manager.pdf_deduplicator.is_duplicate_pdf(pdf_path)[0]:
            logger.info(f"跳过重复PDF: {pdf_path}")
            return None
        
        # 处理PDF...
        text_chunks = self.extract_text_chunks(pdf_path)
        
        # 去重文本块
        unique_chunks = dedup_manager.text_deduplicator.deduplicate_texts(
            text_chunks, use_fuzzy=True
        )
        
        return unique_chunks
```

### 2. 向量存储集成

```python
# 在milvus_manager.py中集成
from utils.dedup_manager import dedup_manager

class MilvusManager:
    def insert_knowledge_with_dedup(self, data: List[Dict[str, Any]]):
        # 去重向量数据
        unique_data = dedup_manager.deduplicate_knowledge_base_data(data)
        
        # 插入到Milvus
        return self.insert_knowledge(unique_data)
```

### 3. 对话系统集成

```python
# 在对话处理中集成
from utils.dedup_manager import dedup_manager

def save_conversation_with_dedup(user_query: str, assistant_response: str):
    # 检查是否重复对话
    if not dedup_manager.history_deduplicator.is_duplicate_conversation(
        user_query, assistant_response
    ):
        # 保存对话
        save_conversation(user_query, assistant_response)
        dedup_manager.history_deduplicator.add_conversation(user_query, assistant_response)
```

## 监控和维护

### 查看缓存状态
```python
from utils.dedup_manager import get_dedup_stats

stats = get_dedup_stats()
print(f"文本缓存: {stats['text_cache_size']} 项")
print(f"向量缓存: {stats['vector_cache_size']} 项")
print(f"PDF缓存: {stats['pdf_cache_size']} 项")
print(f"缓存文件大小: {sum(f['size_mb'] for f in stats['cache_files'])} MB")
```

### 定期维护
```python
# 每天执行一次缓存优化
dedup_manager.optimize_caches()

# 每周清理一次过期缓存
dedup_manager.history_deduplicator._cleanup_expired_hashes()

# 导出缓存信息用于分析
dedup_manager.export_cache_info("cache_report.json")
```

## 运行演示

```bash
cd utils
python dedup_demo.py
```

演示程序将展示：
- 文本去重效果对比
- 向量去重功能
- PDF文件去重
- 对话去重
- 性能对比测试

## 注意事项

1. **内存使用**：大数据量时建议使用批处理模式
2. **缓存管理**：定期清理缓存避免占用过多磁盘空间
3. **相似度阈值**：根据实际需求调整，过高可能漏掉重复，过低可能误删
4. **并发安全**：多线程环境下注意缓存文件的读写安全

## 故障排除

### 常见问题

1. **ImportError**: 安装可选依赖提升性能
2. **内存不足**: 减小batch_size或使用流式处理
3. **处理速度慢**: 启用并行处理和安装xxhash
4. **缓存文件损坏**: 删除缓存文件重新生成

### 性能调优

1. 安装所有可选依赖
2. 使用SSD存储缓存文件
3. 根据CPU核数调整并行度
4. 定期优化和清理缓存
