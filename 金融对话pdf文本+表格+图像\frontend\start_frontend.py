#!/usr/bin/env python3
"""
前端服务启动脚本
启动一个简单的HTTP服务器来提供前端文件
"""
import os
import sys
import webbrowser
import http.server
import socketserver
from pathlib import Path

def start_frontend_server(port=8080):
    """启动前端服务器"""
    try:
        # 切换到frontend目录并清除浏览器缓存
        frontend_dir = Path(__file__).parent
        os.chdir(frontend_dir)
        
        # 添加缓存控制头
        class NoCacheHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
            def end_headers(self):
                self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
                self.send_header('Pragma', 'no-cache')
                self.send_header('Expires', '0')
                super().end_headers()
        
        # 创建HTTP服务器
        handler = NoCacheHTTPRequestHandler
        
        # 尝试启动服务器
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"🚀 前端服务器启动成功！")
            print(f"📍 服务地址: http://localhost:{port}")
            print(f"📁 服务目录: {frontend_dir}")
            print(f"💡 提示: 请确保后端API服务器已在 http://localhost:8000 启动")
            print(f"🔧 如需修改API地址，请在界面设置中更改")
            print(f"⏹️  按 Ctrl+C 停止服务器")
            print("-" * 60)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{port}')
                print(f"🌐 已自动打开浏览器")
            except:
                print(f"⚠️  无法自动打开浏览器，请手动访问 http://localhost:{port}")
            
            print("-" * 60)
            
            # 启动服务器
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {port} 已被占用，尝试使用端口 {port + 1}")
            start_frontend_server(port + 1)
        else:
            print(f"❌ 启动服务器失败: {e}")
            sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n🛑 服务器已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

def check_backend_status():
    """检查后端服务状态"""
    try:
        import requests
        response = requests.get('http://localhost:8000/health', timeout=3)
        if response.status_code == 200:
            print("✅ 后端API服务器运行正常")
            return True
        else:
            print("⚠️  后端API服务器响应异常")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端API服务器 (http://localhost:8000)")
        print("💡 请先启动后端服务器:")
        print("   cd 金融对话")
        print("   python main.py --mode server")
        return False
    except ImportError:
        print("⚠️  未安装requests库，无法检查后端状态")
        return True
    except Exception as e:
        print(f"⚠️  检查后端状态时出错: {e}")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("🏦 金融对话助手 - 前端界面")
    print("=" * 60)
    
    # 检查后端状态
    check_backend_status()
    print("-" * 60)
    
    # 启动前端服务器
    start_frontend_server()

if __name__ == "__main__":
    main()

