# 大模型配置指南

本文档详细说明如何配置各种在线大模型服务。

## 支持的模型列表

### 1. OpenAI GPT 系列
- **模型**: gpt-3.5-turbo, gpt-4, gpt-4-turbo
- **配置项**:
  ```env
  OPENAI_API_KEY=your_openai_api_key
  OPENAI_BASE_URL=https://api.openai.com/v1
  MODEL_NAME=gpt-3.5-turbo
  ```
- **获取方式**: [OpenAI官网](https://platform.openai.com/)

### 2. 智谱AI GLM 系列
- **模型**: glm-4, glm-3-turbo
- **默认模型**: glm-4
- **配置项**:
  ```env
  ZHIPU_API_KEY=your_zhipu_api_key
  ZHIPU_BASE_URL=https://open.bigmodel.cn/api/paas/v4
  ZHIPU_MODEL=glm-4
  ```
- **获取方式**: [智谱AI开放平台](https://open.bigmodel.cn/)

### 3. 通义千问系列
- **模型**: qwen-turbo, qwen-plus, qwen-max
- **默认模型**: qwen-turbo
- **配置项**:
  ```env
  QWEN_API_KEY=your_qwen_api_key
  QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
  QWEN_MODEL=qwen-turbo
  ```
- **获取方式**: [阿里云灵积平台](https://dashscope.aliyun.com/)

### 4. 硅基流动平台
- **模型**: 支持多种开源模型，包括Qwen、Llama、DeepSeek等
- **默认模型**: Qwen/Qwen2.5-7B-Instruct
- **配置项**:
  ```env
  SILICONFLOW_API_KEY=your_siliconflow_api_key
  SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1
  SILICONFLOW_MODEL=Qwen/Qwen2.5-7B-Instruct
  ```
- **获取方式**: [硅基流动平台](https://siliconflow.cn/)

### 5. 百度文心一言
- **模型**: ernie-bot, ernie-bot-turbo
- **默认模型**: ernie-bot-turbo
- **配置项**:
  ```env
  BAIDU_API_KEY=your_baidu_api_key
  BAIDU_SECRET_KEY=your_baidu_secret_key
  BAIDU_BASE_URL=https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop
  BAIDU_MODEL=ernie-bot-turbo
  ```
- **获取方式**: [百度智能云](https://cloud.baidu.com/product/wenxinworkshop)

### 6. Moonshot AI (Kimi)
- **模型**: moonshot-v1-8k, moonshot-v1-32k, moonshot-v1-128k
- **默认模型**: moonshot-v1-8k
- **配置项**:
  ```env
  MOONSHOT_API_KEY=your_moonshot_api_key
  MOONSHOT_BASE_URL=https://api.moonshot.cn/v1
  MOONSHOT_MODEL=moonshot-v1-8k
  ```
- **获取方式**: [Moonshot AI平台](https://platform.moonshot.cn/)

### 7. DeepSeek
- **模型**: deepseek-chat, deepseek-coder
- **默认模型**: deepseek-chat
- **配置项**:
  ```env
  DEEPSEEK_API_KEY=your_deepseek_api_key
  DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
  DEEPSEEK_MODEL=deepseek-chat
  ```
- **获取方式**: [DeepSeek平台](https://platform.deepseek.com/)

### 8. 字节豆包
- **模型**: doubao-pro, doubao-lite
- **默认模型**: ep-20241220105718-8xqkz
- **配置项**:
  ```env
  DOUBAO_API_KEY=your_doubao_api_key
  DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
  DOUBAO_MODEL=ep-20241220105718-8xqkz
  ```
- **获取方式**: [火山引擎](https://www.volcengine.com/product/doubao)

### 9. Anthropic Claude
- **模型**: claude-3-sonnet, claude-3-opus, claude-3-haiku
- **默认模型**: claude-3-sonnet-20240229
- **配置项**:
  ```env
  ANTHROPIC_API_KEY=your_anthropic_api_key
  ANTHROPIC_BASE_URL=https://api.anthropic.com
  ANTHROPIC_MODEL=claude-3-sonnet-20240229
  ```
- **获取方式**: [Anthropic Console](https://console.anthropic.com/)
- **注意**: 需要安装 `pip install anthropic`

### 10. Google Gemini
- **模型**: gemini-pro, gemini-pro-vision
- **默认模型**: gemini-pro
- **配置项**:
  ```env
  GOOGLE_API_KEY=your_google_api_key
  GOOGLE_BASE_URL=https://generativelanguage.googleapis.com/v1beta
  GOOGLE_MODEL=gemini-pro
  ```
- **获取方式**: [Google AI Studio](https://makersuite.google.com/)
- **注意**: 需要安装 `pip install google-generativeai`

## 配置步骤

1. **复制环境变量文件**:
   ```bash
   cp .env.example .env
   ```

2. **编辑.env文件**，添加你需要使用的模型API密钥

3. **安装额外依赖**（如果需要）:
   ```bash
   pip install anthropic google-generativeai
   ```

4. **测试配置**:
   ```bash
   python main.py --mode test
   ```

## 使用示例

### Python客户端
```python
import requests

# 使用不同的模型提供商
providers = ["openai", "zhipu", "qwen", "siliconflow", "baidu", 
            "moonshot", "deepseek", "doubao", "claude", "gemini"]

for provider in providers:
    response = requests.post("http://localhost:8000/chat", json={
        "message": "什么是股票？",
        "model_provider": provider
    })
    
    if response.status_code == 200:
        result = response.json()
        print(f"{provider}: {result['response'][:100]}...")
```

### 命令行测试
```bash
# 查看可用模型
curl http://localhost:8000/models

# 查看模型详细信息
curl http://localhost:8000/models/info

# 使用特定模型对话
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "什么是股票？", "model_provider": "zhipu"}'
```

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要提交到版本控制系统
2. **费用控制**: 大部分模型服务按使用量收费，请注意控制使用量
3. **网络访问**: 某些服务可能需要特定的网络环境
4. **依赖安装**: 部分模型需要额外的Python包
5. **模型限制**: 不同模型有不同的上下文长度和功能限制

## 故障排除

### 常见错误

1. **API密钥错误**: 检查密钥是否正确配置
2. **网络连接失败**: 检查网络连接和防火墙设置
3. **模型不可用**: 某些模型可能暂时不可用或需要申请权限
4. **依赖缺失**: 安装相应的Python包

### 调试方法

1. 查看日志文件: `logs/financial_chat.log`
2. 使用测试模式: `python main.py --mode test`
3. 检查模型状态: `curl http://localhost:8000/health`

## 性能优化

1. **选择合适的模型**: 根据需求选择性价比最高的模型
2. **调整温度参数**: 控制回复的创造性和一致性
3. **缓存机制**: 对于重复查询可以考虑添加缓存
4. **并发控制**: 避免同时发送过多请求

## 扩展开发

如需添加新的模型支持，请参考 `llm_service.py` 中的实现模式：

1. 在 `config.py` 中添加配置项
2. 在 `llm_service.py` 中添加调用方法
3. 在 `generate_response` 方法中添加分支
4. 更新 `get_available_models` 方法
5. 添加相应的测试用例
