"""
安装PDF表格处理依赖
自动安装表格检索优化所需的Python包
"""
import subprocess
import sys
import os
from pathlib import Path

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}...")
        if description:
            print(f"   用途: {description}")
        
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        
        print(f"✅ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("🚀 PDF表格处理依赖安装程序")
    print("=" * 50)
    
    # 定义需要安装的包
    packages = [
        {
            "name": "pandas>=1.5.0",
            "import_name": "pandas",
            "description": "数据处理和分析，用于表格数据操作"
        },
        {
            "name": "pdfplumber>=0.10.0",
            "import_name": "pdfplumber", 
            "description": "PDF表格提取，专门用于提取PDF中的表格"
        },
        {
            "name": "pymupdf>=1.23.0",
            "import_name": "fitz",
            "description": "高级PDF处理，备用表格提取方案"
        },
        {
            "name": "scikit-learn>=1.0.0",
            "import_name": "sklearn",
            "description": "机器学习库，用于文本相似度计算"
        },
        {
            "name": "openpyxl>=3.0.0",
            "import_name": "openpyxl",
            "description": "Excel文件处理，用于表格数据导出"
        },
        {
            "name": "tabulate>=0.9.0",
            "import_name": "tabulate",
            "description": "表格格式化显示"
        }
    ]
    
    # 检查已安装的包
    print("🔍 检查已安装的包...")
    installed_packages = []
    missing_packages = []
    
    for package in packages:
        if check_package(package["import_name"]):
            print(f"✅ {package['name'].split('>=')[0]} 已安装")
            installed_packages.append(package)
        else:
            print(f"❌ {package['name'].split('>=')[0]} 未安装")
            missing_packages.append(package)
    
    if not missing_packages:
        print("\n🎉 所有依赖都已安装！")
        return
    
    print(f"\n📋 需要安装 {len(missing_packages)} 个包:")
    for package in missing_packages:
        print(f"   - {package['name']}: {package['description']}")
    
    # 询问是否继续安装
    response = input("\n❓ 是否继续安装？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("❌ 安装已取消")
        return
    
    # 升级pip
    print("\n🔧 升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip升级成功")
    except:
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装缺失的包
    print("\n📦 开始安装依赖包...")
    success_count = 0
    
    for package in missing_packages:
        if install_package(package["name"], package["description"]):
            success_count += 1
        print()  # 空行分隔
    
    # 安装结果总结
    print("=" * 50)
    print("📊 安装结果总结:")
    print(f"   成功安装: {success_count}/{len(missing_packages)} 个包")
    print(f"   已有包数: {len(installed_packages)} 个")
    
    if success_count == len(missing_packages):
        print("\n🎉 所有依赖安装完成！")
        print("\n📝 接下来可以:")
        print("   1. 运行表格检索演示: python examples/table_retrieval_demo.py")
        print("   2. 启动API服务器: python main.py --mode server")
        print("   3. 使用表格检索API进行开发")
    else:
        print(f"\n⚠️  有 {len(missing_packages) - success_count} 个包安装失败")
        print("请检查网络连接或手动安装失败的包")
    
    # 验证安装
    print("\n🔍 验证安装...")
    verification_failed = []
    
    for package in missing_packages:
        if check_package(package["import_name"]):
            print(f"✅ {package['name'].split('>=')[0]} 验证成功")
        else:
            print(f"❌ {package['name'].split('>=')[0]} 验证失败")
            verification_failed.append(package)
    
    if verification_failed:
        print(f"\n⚠️  {len(verification_failed)} 个包验证失败:")
        for package in verification_failed:
            print(f"   - {package['name']}")
        print("\n💡 解决方案:")
        print("   1. 检查Python环境是否正确")
        print("   2. 尝试手动安装: pip install <package_name>")
        print("   3. 检查是否有权限问题")
    else:
        print("\n✅ 所有包验证通过！")

def create_requirements_file():
    """创建requirements文件"""
    requirements_content = """# PDF表格处理依赖
pandas>=1.5.0
pdfplumber>=0.10.0
pymupdf>=1.23.0
scikit-learn>=1.0.0
openpyxl>=3.0.0
tabulate>=0.9.0

# 可选依赖（用于高级功能）
matplotlib>=3.5.0  # 表格可视化
seaborn>=0.11.0    # 数据可视化
plotly>=5.0.0      # 交互式图表
"""
    
    requirements_file = Path("requirements_table.txt")
    with open(requirements_file, 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    print(f"📄 已创建依赖文件: {requirements_file}")
    print("   可以使用: pip install -r requirements_table.txt")

if __name__ == "__main__":
    try:
        main()
        
        # 询问是否创建requirements文件
        response = input("\n❓ 是否创建requirements_table.txt文件？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            create_requirements_file()
        
    except KeyboardInterrupt:
        print("\n❌ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出错: {e}")
        print("请检查Python环境和网络连接")
