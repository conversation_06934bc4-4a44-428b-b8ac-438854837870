"""
FastAPI对话接口
提供RESTful API接口用于金融对话交互
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import time
import uuid
from loguru import logger

import sys
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.rag_system import RAGSystem
from llm.llm_service import LLMService
from idconfig.config import Config
from api.pdf_api import router as pdf_router

# 初始化FastAPI应用
app = FastAPI(
    title="金融对话系统",
    description="基于RAG和大模型的金融对话系统",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含PDF API路由
app.include_router(pdf_router)

# 全局变量
rag_system = None
llm_service = None
config = Config()

# 请求模型
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    model_provider: Optional[str] = "openai"
    model: Optional[str] = None  # 用于指定具体的模型（如硅基流动的具体模型）
    temperature: Optional[float] = 0.7

class KnowledgeRequest(BaseModel):
    content: str
    category: str
    source: Optional[str] = ""

class BatchKnowledgeRequest(BaseModel):
    knowledge_list: List[Dict[str, str]]

# 响应模型
class ChatResponse(BaseModel):
    response: str
    session_id: str
    timestamp: int
    context_used: Dict[str, Any]

class StatusResponse(BaseModel):
    status: str
    message: str
    available_models: List[str]

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global rag_system, llm_service
    
    logger.info("正在初始化金融对话系统...")
    
    # 初始化RAG系统
    rag_system = RAGSystem()
    if not rag_system.initialize():
        logger.error("RAG系统初始化失败")
        raise Exception("RAG系统初始化失败")
    
    # 初始化LLM服务
    llm_service = LLMService()
    
    logger.info("金融对话系统初始化完成")

@app.get("/", response_model=StatusResponse)
async def root():
    """根路径，返回系统状态"""
    return StatusResponse(
        status="running",
        message="金融对话系统运行正常",
        available_models=llm_service.get_available_models() if llm_service else []
    )

@app.get("/health", response_model=StatusResponse)
async def health_check():
    """健康检查"""
    try:
        # 检查各个组件状态
        if not rag_system or not llm_service:
            raise Exception("系统组件未初始化")
        
        return StatusResponse(
            status="healthy",
            message="所有组件运行正常",
            available_models=llm_service.get_available_models()
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """对话接口"""
    try:
        # 生成会话ID
        session_id = request.session_id or str(uuid.uuid4())
        timestamp = int(time.time())
        
        # 检索相关上下文
        logger.info(f"用户查询: {request.message}")
        context = rag_system.retrieve_context(request.message)
        
        # 创建提示消息
        messages = llm_service.create_financial_prompt(request.message, context)
        
        # 生成回复
        response = llm_service.generate_response(
            messages=messages,
            model_provider=request.model_provider,
            temperature=request.temperature,
            model=request.model
        )
        
        # 保存对话历史
        rag_system.add_conversation_history(
            session_id=session_id,
            user_query=request.message,
            assistant_response=response,
            timestamp=timestamp
        )
        
        logger.info(f"生成回复完成，会话ID: {session_id}")
        
        return ChatResponse(
            response=response,
            session_id=session_id,
            timestamp=timestamp,
            context_used={
                "knowledge_count": len(context.get("knowledge", [])),
                "history_count": len(context.get("history", []))
            }
        )
        
    except Exception as e:
        logger.error(f"对话处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"对话处理失败: {str(e)}")

@app.post("/knowledge/add")
async def add_knowledge(request: KnowledgeRequest):
    """添加单条知识"""
    try:
        success = rag_system.add_knowledge(
            content=request.content,
            category=request.category,
            source=request.source
        )
        
        if success:
            return {"status": "success", "message": "知识添加成功"}
        else:
            raise HTTPException(status_code=500, detail="知识添加失败")
            
    except Exception as e:
        logger.error(f"添加知识失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/knowledge/batch_add")
async def batch_add_knowledge(request: BatchKnowledgeRequest):
    """批量添加知识"""
    try:
        success = rag_system.batch_add_knowledge(request.knowledge_list)
        
        if success:
            return {
                "status": "success", 
                "message": f"成功添加 {len(request.knowledge_list)} 条知识"
            }
        else:
            raise HTTPException(status_code=500, detail="批量添加知识失败")
            
    except Exception as e:
        logger.error(f"批量添加知识失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/knowledge/search")
async def search_knowledge(query: str, top_k: int = 5):
    """搜索知识库"""
    try:
        results = rag_system.search_knowledge(query, top_k)
        return {
            "status": "success",
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"搜索知识失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/history/search")
async def search_history(query: str, top_k: int = 5):
    """搜索历史对话"""
    try:
        results = rag_system.search_conversation_history(query, top_k)
        return {
            "status": "success",
            "results": results,
            "count": len(results)
        }
        
    except Exception as e:
        logger.error(f"搜索历史对话失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models")
async def get_available_models():
    """获取可用的模型列表"""
    try:
        models = llm_service.get_available_models()
        return {
            "status": "success",
            "available_models": models
        }

    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models/siliconflow")
async def get_siliconflow_models():
    """获取硅基流动支持的模型列表"""
    try:
        models = llm_service.get_siliconflow_models()
        return {
            "status": "success",
            "siliconflow_models": models,
            "count": len(models)
        }

    except Exception as e:
        logger.error(f"获取硅基流动模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/models/info")
async def get_model_info():
    """获取所有模型的详细信息"""
    try:
        model_info = llm_service.get_model_info()
        return {
            "status": "success",
            "model_info": model_info
        }

    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.api_server:app",
        host=config.API_HOST,
        port=config.API_PORT,
        reload=True
    )
