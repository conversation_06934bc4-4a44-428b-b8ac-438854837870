#!/usr/bin/env python3
"""
测试图片集成功能
验证图片检索和回答流程是否正常工作
"""
import sys
import asyncio
import json
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

from rag.rag_system import RAGSystem
from llm.llm_service import LLMService
from api.api_server import ChatRequest
from loguru import logger

async def test_image_integration():
    """测试图片集成功能"""
    logger.info("开始测试图片集成功能...")
    
    # 初始化系统
    rag_system = RAGSystem()
    if not rag_system.initialize():
        logger.error("RAG系统初始化失败")
        return False
    
    llm_service = LLMService()
    
    # 测试查询列表
    test_queries = [
        "请分析一下公司的收入趋势图表",
        "这个财务报表显示了什么信息？",
        "图表中的数据说明了什么？",
        "公司的增长情况如何？",
        "市场份额的变化趋势是什么？"
    ]
    
    for i, query in enumerate(test_queries, 1):
        logger.info(f"\n{'='*50}")
        logger.info(f"测试查询 {i}: {query}")
        logger.info(f"{'='*50}")
        
        try:
            # 检索上下文（包括图片）
            context = rag_system.retrieve_context(query, include_images=True)
            
            # 显示检索结果
            logger.info(f"检索结果:")
            logger.info(f"  - 知识条数: {len(context.get('knowledge', []))}")
            logger.info(f"  - 历史对话: {len(context.get('history', []))}")
            logger.info(f"  - 相关图片: {len(context.get('images', []))}")
            
            # 显示图片信息
            if context.get('images'):
                logger.info("\n相关图片详情:")
                for j, img in enumerate(context['images'], 1):
                    logger.info(f"  图片 {j}:")
                    logger.info(f"    - 来源: {img.get('pdf_name', '未知')}")
                    logger.info(f"    - 页码: {img.get('page_number', 0)}")
                    logger.info(f"    - 类型: {img.get('image_type', '未知')}")
                    logger.info(f"    - 描述: {img.get('description', '无描述')}")
                    logger.info(f"    - 相关度: {img.get('final_score', 0):.3f}")
            
            # 创建提示消息
            messages = llm_service.create_financial_prompt(query, context)
            
            # 显示提示信息（仅显示部分）
            logger.info(f"\n生成的提示长度: {len(messages[1]['content'])} 字符")
            
            # 这里可以选择是否实际调用LLM
            # response = llm_service.generate_response(messages, "openai", 0.7)
            # logger.info(f"LLM回答: {response[:200]}...")
            
            logger.info("✅ 测试通过")
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            return False
    
    logger.info(f"\n{'='*50}")
    logger.info("🎉 所有测试完成！")
    logger.info(f"{'='*50}")
    return True

def test_multimodal_retriever():
    """测试多模态检索器"""
    logger.info("测试多模态检索器...")
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        retriever = MultimodalImageRetriever()
        if not retriever.initialize():
            logger.warning("多模态检索器初始化失败，可能是依赖未安装")
            return False
        
        # 测试图片搜索
        test_query = "收入图表"
        results = retriever.search_images_by_text(test_query, top_k=3)
        
        logger.info(f"搜索查询: {test_query}")
        logger.info(f"找到 {len(results)} 张相关图片")
        
        for i, result in enumerate(results, 1):
            logger.info(f"  图片 {i}:")
            logger.info(f"    - ID: {result.get('image_id', '未知')}")
            logger.info(f"    - 来源: {result.get('pdf_name', '未知')}")
            logger.info(f"    - 相似度: {result.get('similarity', 0):.3f}")
            logger.info(f"    - 综合得分: {result.get('final_score', 0):.3f}")
        
        # 获取统计信息
        stats = retriever.get_image_statistics()
        logger.info(f"\n图片库统计:")
        logger.info(f"  - 总文件数: {stats.get('total_files', 0)}")
        logger.info(f"  - 总图片数: {stats.get('total_images', 0)}")
        logger.info(f"  - 图片类型: {stats.get('image_types', {})}")
        
        return True
        
    except ImportError:
        logger.warning("多模态功能不可用，跳过测试")
        return True
    except Exception as e:
        logger.error(f"多模态检索器测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始图片集成功能测试")
    
    # 测试多模态检索器
    if not test_multimodal_retriever():
        logger.error("多模态检索器测试失败")
        return
    
    # 测试图片集成功能
    success = asyncio.run(test_image_integration())
    
    if success:
        logger.info("✅ 所有测试通过！图片集成功能正常工作")
    else:
        logger.error("❌ 测试失败，请检查配置和依赖")

if __name__ == "__main__":
    main()
