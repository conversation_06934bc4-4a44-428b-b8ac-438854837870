"""
金融对话系统主程序入口
整合所有模块功能，提供统一的启动入口
"""
import sys
import argparse
import uvicorn
from loguru import logger
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from idconfig.config import Config
from rag.rag_system import RAGSystem
from llm.llm_service import LLMService

def setup_logging():
    """设置日志"""
    config = Config()
    
    # 创建日志目录
    log_dir = Path(config.LOG_FILE).parent
    log_dir.mkdir(exist_ok=True)
    
    # 配置loguru
    logger.remove()  # 移除默认处理器
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        level=config.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 添加文件输出
    logger.add(
        config.LOG_FILE,
        level=config.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )

def check_environment():
    """检查环境配置"""
    config = Config()
    
    logger.info("检查环境配置...")
    
    # 检查必要的配置
    issues = []
    
    if not config.MILVUS_HOST:
        issues.append("MILVUS_HOST未配置")
    
    # 检查是否至少配置了一个大模型API密钥
    api_keys_configured = [
        config.OPENAI_API_KEY,
        config.ZHIPU_API_KEY,
        config.QWEN_API_KEY,
        config.SILICONFLOW_API_KEY,
        config.BAIDU_API_KEY and config.BAIDU_SECRET_KEY,  # 百度需要两个密钥
        config.XUNFEI_API_KEY and config.XUNFEI_APP_ID and config.XUNFEI_API_SECRET,  # 讯飞需要三个密钥
        config.TENCENT_SECRET_ID and config.TENCENT_SECRET_KEY,  # 腾讯需要两个密钥
        config.MOONSHOT_API_KEY,
        config.DEEPSEEK_API_KEY,
        config.DOUBAO_API_KEY,
        config.ANTHROPIC_API_KEY,
        config.GOOGLE_API_KEY
    ]

    if not any(api_keys_configured):
        issues.append("至少需要配置一个大模型API密钥")
        issues.append("支持的模型: OpenAI, 智谱AI, 通义千问, 硅基流动, 百度文心, 讯飞星火, 腾讯混元, Moonshot, DeepSeek, 豆包, Claude, Gemini")
    
    if issues:
        logger.error("环境配置检查失败:")
        for issue in issues:
            logger.error(f"  - {issue}")
        logger.error("请检查.env文件或环境变量配置")
        return False

    # 显示已配置的模型
    configured_models = []
    if config.OPENAI_API_KEY:
        configured_models.append("OpenAI")
    if config.ZHIPU_API_KEY:
        configured_models.append("智谱AI")
    if config.QWEN_API_KEY:
        configured_models.append("通义千问")
    if config.SILICONFLOW_API_KEY:
        configured_models.append("硅基流动")
    if config.BAIDU_API_KEY and config.BAIDU_SECRET_KEY:
        configured_models.append("百度文心")
    if config.XUNFEI_API_KEY and config.XUNFEI_APP_ID and config.XUNFEI_API_SECRET:
        configured_models.append("讯飞星火")
    if config.TENCENT_SECRET_ID and config.TENCENT_SECRET_KEY:
        configured_models.append("腾讯混元")
    if config.MOONSHOT_API_KEY:
        configured_models.append("Moonshot")
    if config.DEEPSEEK_API_KEY:
        configured_models.append("DeepSeek")
    if config.DOUBAO_API_KEY:
        configured_models.append("豆包")
    if config.ANTHROPIC_API_KEY:
        configured_models.append("Claude")
    if config.GOOGLE_API_KEY:
        configured_models.append("Gemini")

    logger.info("环境配置检查通过")
    logger.info(f"已配置的模型: {', '.join(configured_models)}")
    return True

def initialize_system():
    """初始化系统"""
    logger.info("正在初始化金融对话系统...")
    
    try:
        # 初始化RAG系统
        logger.info("初始化RAG系统...")
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 初始化LLM服务
        logger.info("初始化LLM服务...")
        LLMService()  # 验证LLM服务可以正常初始化
        
        logger.info("系统初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False

def add_sample_knowledge():
    """添加示例知识数据"""
    logger.info("添加示例金融知识...")
    
    try:
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统初始化失败")
            return False
        
        # 示例金融知识
        sample_knowledge = [
            {
                "content": "股票是股份公司发行的所有权凭证，是股份公司为筹集资金而发行给各个股东作为持股凭证并借以取得股息和红利的一种有价证券。",
                "category": "股票基础",
                "source": "金融基础知识"
            },
            {
                "content": "债券是政府、企业、银行等债务人为筹集资金，按照法定程序发行并向债权人承诺于指定日期还本付息的有价证券。",
                "category": "债券基础",
                "source": "金融基础知识"
            },
            {
                "content": "基金是指为了某种目的而设立的具有一定数量的资金，主要包括信托投资基金、公积金、保险基金、退休基金等。",
                "category": "基金基础",
                "source": "金融基础知识"
            },
            {
                "content": "市盈率(P/E)是指股票价格除以每股收益的比率，是衡量股票投资价值的重要指标之一。",
                "category": "财务指标",
                "source": "投资分析"
            },
            {
                "content": "风险管理是指如何在项目或者企业一个肯定有风险的环境里把风险可能造成的不良影响减至最低的管理过程。",
                "category": "风险管理",
                "source": "风险管理理论"
            }
        ]
        
        success = rag_system.batch_add_knowledge(sample_knowledge)
        if success:
            logger.info(f"成功添加 {len(sample_knowledge)} 条示例知识")
        else:
            logger.warning("添加示例知识失败")
        
        return success
        
    except Exception as e:
        logger.error(f"添加示例知识失败: {e}")
        return False

def start_server():
    """启动API服务器"""
    config = Config()
    
    logger.info(f"启动API服务器 - {config.API_HOST}:{config.API_PORT}")
    
    try:
        uvicorn.run(
            "api.api_server:app",
            host=config.API_HOST,
            port=config.API_PORT,
            reload=False,
            log_level=config.LOG_LEVEL.lower()
        )
    except Exception as e:
        logger.error(f"启动服务器失败: {e}")
        sys.exit(1)

def test_system():
    """测试系统功能"""
    logger.info("测试系统功能...")
    
    try:
        # 测试RAG系统
        rag_system = RAGSystem()
        if not rag_system.initialize():
            logger.error("RAG系统测试失败")
            return False
        
        # 测试搜索功能
        results = rag_system.search_knowledge("股票是什么")
        logger.info(f"知识库搜索测试 - 找到 {len(results)} 条结果")
        
        # 测试LLM服务
        llm_service = LLMService()
        available_models = llm_service.get_available_models()
        logger.info(f"可用模型: {available_models}")
        
        if available_models:
            # 测试生成回复
            messages = [
                {"role": "system", "content": "你是一个金融助手"},
                {"role": "user", "content": "什么是股票？"}
            ]
            response = llm_service.generate_response(messages, available_models[0])
            logger.info(f"LLM测试回复: {response[:100]}...")
        
        logger.info("系统功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"系统测试失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="金融对话系统")
    parser.add_argument("--mode", choices=["server", "test", "init"], default="server",
                       help="运行模式: server(启动服务器), test(测试系统), init(初始化数据)")
    parser.add_argument("--add-sample", action="store_true",
                       help="添加示例知识数据")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    logger.info("=" * 50)
    logger.info("金融对话系统启动")
    logger.info("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 根据模式执行不同操作
    if args.mode == "init":
        logger.info("初始化模式")
        if initialize_system():
            if args.add_sample:
                add_sample_knowledge()
            logger.info("初始化完成")
        else:
            logger.error("初始化失败")
            sys.exit(1)
    
    elif args.mode == "test":
        logger.info("测试模式")
        if not test_system():
            sys.exit(1)
    
    elif args.mode == "server":
        logger.info("服务器模式")
        # 初始化系统
        if not initialize_system():
            sys.exit(1)
        
        # 如果指定添加示例数据
        if args.add_sample:
            add_sample_knowledge()
        
        # 启动服务器
        start_server()
    
    else:
        logger.error(f"未知模式: {args.mode}")
        sys.exit(1)

if __name__ == "__main__":
    main()
