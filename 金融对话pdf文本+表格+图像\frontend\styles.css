/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 浅色主题 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --background-color: #ffffff;
    --surface-color: #f8fafc;
    --border-color: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] {
    /* 深色主题 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --secondary-color: #6b7280;
    --background-color: #111827;
    --surface-color: #1f2937;
    --border-color: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    width: 280px;
    background-color: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    z-index: 1000;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.sidebar-toggle:hover {
    background-color: var(--border-color);
}

.new-chat-btn {
    margin: 1rem;
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.new-chat-btn:hover {
    background-color: var(--primary-hover);
}

.chat-history {
    flex: 1;
    padding: 0 1rem;
    overflow-y: auto;
}

.chat-history h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.history-item {
    padding: 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: 1px solid transparent;
}

.history-item:hover {
    background-color: var(--border-color);
}

.history-item.active {
    background-color: var(--primary-color);
    color: white;
}

.history-item-title {
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.history-item-time {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.features-section {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-btn {
    width: 100%;
    padding: 0.75rem;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.feature-btn:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.settings-section {
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.settings-btn {
    width: 100%;
    padding: 0.75rem;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-btn:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 工具栏 */
.toolbar {
    padding: 1rem;
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.toolbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
}

.toolbar h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.model-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.model-selector label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.model-selector select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--background-color);
    color: var(--text-primary);
    cursor: pointer;
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background-color: var(--border-color);
    color: var(--text-primary);
}

/* 聊天容器 */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.messages-container {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

.message.user-message {
    justify-content: flex-end;
}

.message-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.assistant-message .message-avatar {
    background-color: var(--primary-color);
    color: white;
}

.user-message .message-avatar {
    background-color: var(--success-color);
    color: white;
    order: 2;
    margin-right: 0;
    margin-left: 0.75rem;
}

.message-content {
    max-width: 70%;
    display: flex;
    flex-direction: column;
}

.user-message .message-content {
    align-items: flex-end;
}

.message-text {
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    word-wrap: break-word;
    line-height: 1.5;
}

.user-message .message-text {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
    padding: 0 0.5rem;
}

/* 输入区域 */
.input-container {
    padding: 1rem;
    background-color: var(--surface-color);
    border-top: 1px solid var(--border-color);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 0.75rem;
    transition: border-color 0.2s ease;
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    background: none;
    color: var(--text-primary);
    resize: none;
    min-height: 1.5rem;
    max-height: 8rem;
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.5;
}

#messageInput::placeholder {
    color: var(--text-muted);
}

.input-actions {
    display: flex;
    gap: 0.25rem;
}

.attach-btn, .send-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.attach-btn {
    background-color: var(--border-color);
    color: var(--text-secondary);
}

.attach-btn:hover {
    background-color: var(--secondary-color);
    color: white;
}

.send-btn {
    background-color: var(--primary-color);
    color: white;
}

.send-btn:hover {
    background-color: var(--primary-hover);
}

.send-btn:disabled {
    background-color: var(--border-color);
    color: var(--text-muted);
    cursor: not-allowed;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    padding: 0 0.5rem;
}

.char-count {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.typing-indicator {
    font-size: 0.75rem;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.typing-indicator i {
    animation: pulse 1.5s infinite;
}

.typing-indicator i:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator i:nth-child(3) {
    animation-delay: 0.4s;
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.4;
    }
    50% {
        opacity: 1;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--background-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.large-modal {
    max-width: 800px;
    width: 95%;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background-color: var(--border-color);
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

.setting-group {
    margin-bottom: 1.5rem;
}

.setting-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.setting-group input[type="text"],
.setting-group input[type="range"],
.setting-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--surface-color);
    color: var(--text-primary);
    transition: border-color 0.2s ease;
}

.setting-group input[type="text"]:focus,
.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.setting-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--border-color);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background-color: var(--secondary-color);
    color: white;
}

/* 上传信息 */
.upload-info {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.upload-info h4 {
    color: var(--primary-color);
    margin-bottom: 0.75rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.upload-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.upload-info li {
    padding: 0.25rem 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.upload-info strong {
    color: var(--primary-color);
    font-weight: 600;
}

/* 上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: var(--surface-color);
}

.upload-area i {
    font-size: 3rem;
    color: var(--text-muted);
    margin-bottom: 1rem;
}

.upload-area p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.upload-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: var(--border-color);
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
    width: 0%;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.125rem;
}

/* 知识库管理样式 */
.knowledge-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-btn:hover {
    color: var(--text-primary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--surface-color);
    color: var(--text-primary);
    transition: border-color 0.2s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.search-box {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.search-box input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--surface-color);
    color: var(--text-primary);
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    background-color: var(--surface-color);
}

.search-result-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover {
    background-color: var(--border-color);
}

.result-content {
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.result-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.result-category {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.knowledge-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    padding: 1rem;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: var(--surface-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-2rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        height: 100vh;
        transform: translateX(-100%);
        z-index: 1000;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .mobile-menu-btn {
        display: block;
    }

    .toolbar h1 {
        font-size: 1.25rem;
    }

    .toolbar-right {
        gap: 0.5rem;
    }

    .model-selector {
        display: none;
    }

    .message-content {
        max-width: 85%;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
}
