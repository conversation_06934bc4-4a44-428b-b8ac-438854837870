#!/usr/bin/env python3
"""
调试PDF处理状态的脚本
用于检查当前处理进度和可能的问题
"""

import sys
import os
import time
import psutil
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

from loguru import logger
from rag.rag_system import RAGSystem
from rag.milvus_manager import MilvusManager

def check_system_status():
    """检查系统状态"""
    print("=" * 50)
    print("系统状态检查")
    print("=" * 50)
    
    # 检查内存使用
    memory = psutil.virtual_memory()
    print(f"内存使用: {memory.percent:.1f}% ({memory.used / 1024**3:.1f}GB / {memory.total / 1024**3:.1f}GB)")
    
    # 检查CPU使用
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"CPU使用: {cpu_percent:.1f}%")
    
    # 检查磁盘使用
    disk = psutil.disk_usage('.')
    print(f"磁盘使用: {disk.percent:.1f}% ({disk.used / 1024**3:.1f}GB / {disk.total / 1024**3:.1f}GB)")
    
    print()

def check_milvus_connection():
    """检查Milvus连接状态"""
    print("=" * 50)
    print("Milvus连接检查")
    print("=" * 50)
    
    try:
        milvus_manager = MilvusManager()
        if milvus_manager.initialize():
            print("✓ Milvus连接正常")
            
            # 检查知识库集合状态
            if milvus_manager.knowledge_collection:
                # 获取集合统计信息
                stats = milvus_manager.knowledge_collection.num_entities
                print(f"✓ 知识库集合存在，当前记录数: {stats}")
            else:
                print("✗ 知识库集合未初始化")
        else:
            print("✗ Milvus连接失败")
    except Exception as e:
        print(f"✗ Milvus检查出错: {e}")
    
    print()

def check_embedding_model():
    """检查嵌入模型状态"""
    print("=" * 50)
    print("嵌入模型检查")
    print("=" * 50)
    
    try:
        rag_system = RAGSystem()
        if rag_system.initialize():
            print("✓ RAG系统初始化成功")
            
            # 测试向量编码
            test_text = "这是一个测试文本"
            start_time = time.time()
            embedding = rag_system.encode_text(test_text)
            encode_time = time.time() - start_time
            
            if embedding:
                print(f"✓ 向量编码正常，耗时: {encode_time:.2f}秒，向量维度: {len(embedding)}")
            else:
                print("✗ 向量编码失败")
        else:
            print("✗ RAG系统初始化失败")
    except Exception as e:
        print(f"✗ 嵌入模型检查出错: {e}")
    
    print()

def check_log_status():
    """检查日志状态"""
    print("=" * 50)
    print("日志状态检查")
    print("=" * 50)
    
    log_file = "logs/financial_chat.log"
    if os.path.exists(log_file):
        stat = os.stat(log_file)
        size_mb = stat.st_size / 1024 / 1024
        mod_time = time.ctime(stat.st_mtime)
        print(f"✓ 日志文件存在")
        print(f"  文件大小: {size_mb:.1f}MB")
        print(f"  最后修改: {mod_time}")
        
        # 读取最后几行
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"  总行数: {len(lines)}")
                    print("  最后5行:")
                    for line in lines[-5:]:
                        print(f"    {line.strip()}")
        except Exception as e:
            print(f"  读取日志文件出错: {e}")
    else:
        print("✗ 日志文件不存在")
    
    print()

def check_running_processes():
    """检查相关进程"""
    print("=" * 50)
    print("进程检查")
    print("=" * 50)
    
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'financial' in cmdline.lower() or 'rag' in cmdline.lower() or 'pdf' in cmdline.lower():
                    python_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if python_processes:
        print("发现相关Python进程:")
        for proc in python_processes:
            print(f"  PID: {proc['pid']}, CPU: {proc['cpu_percent']:.1f}%, 内存: {proc['memory_percent']:.1f}%")
            print(f"    命令: {' '.join(proc['cmdline'][:3])}...")
    else:
        print("未发现相关Python进程")
    
    print()

def main():
    """主函数"""
    print("PDF处理状态调试工具")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print()
    
    check_system_status()
    check_log_status()
    check_running_processes()
    check_milvus_connection()
    check_embedding_model()
    
    print("=" * 50)
    print("调试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
