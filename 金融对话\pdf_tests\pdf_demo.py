"""
PDF处理功能演示脚本
展示如何使用PDF处理器和管理器
"""
import sys
import os
from pathlib import Path

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.pdf_processor import PDFProcessor
from rag.pdf_manager import PDFManager
from loguru import logger

def demo_pdf_processor():
    """演示PDF处理器功能"""
    logger.info("=== PDF处理器演示 ===")
    
    # 初始化PDF处理器
    processor = PDFProcessor()
    if not processor.initialize():
        logger.error("PDF处理器初始化失败")
        return
    
    # 示例PDF文件路径（需要替换为实际文件路径）
    pdf_file = "pdf_tests/sample_documents/sample_questions.pdf"

    if not os.path.exists(pdf_file):
        logger.warning(f"示例PDF文件不存在: {pdf_file}")
        logger.info("请将PDF文件放在 pdf_tests/sample_documents/ 目录下，或修改文件路径")
        return
    
    # 处理PDF文件
    logger.info(f"开始处理PDF文件: {pdf_file}")
    success = processor.process_pdf_to_knowledge_base(
        file_path=pdf_file,
        category="金融报告",
        loader_type="pypdf"
    )
    
    if success:
        logger.info("PDF文件处理成功！")
    else:
        logger.error("PDF文件处理失败")

def demo_pdf_manager():
    """演示PDF管理器功能"""
    logger.info("=== PDF管理器演示 ===")
    
    # 初始化PDF管理器
    manager = PDFManager()
    if not manager.initialize():
        logger.error("PDF管理器初始化失败")
        return
    
    # 示例PDF文件路径
    pdf_file = "pdf_tests/sample_documents/sample_questions.pdf"

    if not os.path.exists(pdf_file):
        logger.warning(f"示例PDF文件不存在: {pdf_file}")
        logger.info("请将PDF文件放在 pdf_tests/sample_documents/ 目录下，或修改文件路径")
        
        # 演示其他功能
        demo_manager_other_features(manager)
        return
    
    # 1. 上传PDF文件
    logger.info("1. 上传PDF文件")
    upload_result = manager.upload_pdf(
        file_path=pdf_file,
        category="金融报告",
        description="示例金融文档",
        tags=["金融", "报告", "示例"]
    )
    logger.info(f"上传结果: {upload_result}")
    
    if upload_result["success"]:
        file_hash = upload_result["file_hash"]
        
        # 2. 处理PDF文件
        logger.info("2. 处理PDF文件")
        process_result = manager.process_uploaded_pdf(file_hash)
        logger.info(f"处理结果: {process_result}")
    
    # 演示其他功能
    demo_manager_other_features(manager)

def demo_manager_other_features(manager):
    """演示管理器的其他功能"""
    
    # 3. 列出所有PDF文件
    logger.info("3. 列出所有PDF文件")
    pdf_list = manager.list_pdfs()
    logger.info(f"找到 {len(pdf_list)} 个PDF文件:")
    for pdf in pdf_list:
        logger.info(f"  - {pdf['name']} ({pdf['category']}) - 已处理: {pdf['processed']}")
    
    # 4. 获取统计信息
    logger.info("4. 获取统计信息")
    stats = manager.get_statistics()
    logger.info(f"统计信息: {stats}")
    
    # 5. 搜索功能（如果有已处理的文档）
    if any(pdf['processed'] for pdf in pdf_list):
        logger.info("5. 搜索PDF知识库")
        search_results = manager.search_pdfs("金融", top_k=3)
        logger.info(f"搜索结果: 找到 {len(search_results)} 条相关内容")
        for i, result in enumerate(search_results, 1):
            logger.info(f"  结果 {i}: {result['content'][:100]}... (相似度: {result['score']:.3f})")



def create_sample_knowledge():
    """创建一些示例知识数据"""
    logger.info("=== 创建示例知识数据 ===")
    
    from rag.rag_system import RAGSystem
    
    # 初始化RAG系统
    rag_system = RAGSystem()
    if not rag_system.initialize():
        logger.error("RAG系统初始化失败")
        return
    
    # 示例金融知识
    sample_knowledge = [
        {
            "content": "股票是股份公司发行的所有权凭证，是股份公司为筹集资金而发行给各个股东作为持股凭证并借以取得股息和红利的一种有价证券。",
            "category": "金融基础知识",
            "source": "金融教科书"
        },
        {
            "content": "债券是政府、企业、银行等债务人为筹集资金，按照法定程序发行并向债权人承诺于指定日期还本付息的有价证券。",
            "category": "金融基础知识",
            "source": "金融教科书"
        },
        {
            "content": "基金是指为了某种目的而设立的具有一定数量的资金。主要包括信托投资基金、公积金、保险基金、退休基金，各种基金会的基金。",
            "category": "金融基础知识",
            "source": "金融教科书"
        },
        {
            "content": "市盈率（PE）是指股票价格除以每股收益的比率，是衡量股票投资价值的重要指标之一。",
            "category": "金融指标",
            "source": "投资分析手册"
        },
        {
            "content": "净资产收益率（ROE）是净利润与平均股东权益的百分比，是公司税后利润除以净资产得到的百分比率。",
            "category": "金融指标",
            "source": "投资分析手册"
        }
    ]
    
    # 批量添加知识
    success = rag_system.batch_add_knowledge(sample_knowledge)
    if success:
        logger.info(f"成功添加 {len(sample_knowledge)} 条示例知识")
    else:
        logger.error("添加示例知识失败")

def test_search_functionality():
    """测试搜索功能"""
    logger.info("=== 测试搜索功能 ===")
    
    from rag.rag_system import RAGSystem
    
    # 初始化RAG系统
    rag_system = RAGSystem()
    if not rag_system.initialize():
        logger.error("RAG系统初始化失败")
        return
    
    # 测试搜索
    test_queries = [
        "什么是股票？",
        "债券的定义",
        "市盈率如何计算？",
        "基金投资",
        "ROE指标"
    ]
    
    for query in test_queries:
        logger.info(f"搜索查询: {query}")
        results = rag_system.search_knowledge(query, top_k=3)
        logger.info(f"找到 {len(results)} 条相关结果:")
        for i, result in enumerate(results, 1):
            logger.info(f"  {i}. {result['content'][:80]}... (相似度: {result['score']:.3f})")
        logger.info("")

def main():
    """主函数"""
    logger.info("开始PDF处理功能演示")
    
    # 创建示例目录
    examples_dir = Path("texts_pdf")
    examples_dir.mkdir(exist_ok=True)
    
    pdfs_dir = examples_dir / "pdfs"
    pdfs_dir.mkdir(exist_ok=True)
    
    try:
        # 1. 创建示例知识数据
        create_sample_knowledge()
        
        # 2. 测试搜索功能
        test_search_functionality()
        
        # 3. 演示PDF处理器
        demo_pdf_processor()
        
        # 4. 演示PDF管理器
        demo_pdf_manager()
        
        # 5. 演示批量处理
        demo_batch_processing()
        
        logger.info("PDF处理功能演示完成")
        
    except Exception as e:
        logger.error(f"演示过程中出错: {e}")

if __name__ == "__main__":
    main()
