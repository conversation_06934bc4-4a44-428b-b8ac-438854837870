2025-07-23 10:08:24 | INFO     | __main__:main:251 - ==================================================
2025-07-23 10:08:24 | INFO     | __main__:main:252 - 金融对话系统启动
2025-07-23 10:08:24 | INFO     | __main__:main:253 - ==================================================
2025-07-23 10:08:24 | INFO     | __main__:check_environment:50 - 检查环境配置...
2025-07-23 10:08:24 | INFO     | __main__:check_environment:106 - 环境配置检查通过
2025-07-23 10:08:24 | INFO     | __main__:check_environment:107 - 已配置的模型: 硅基流动
2025-07-23 10:08:24 | INFO     | __main__:main:276 - 服务器模式
2025-07-23 10:08:24 | INFO     | __main__:initialize_system:112 - 正在初始化金融对话系统...
2025-07-23 10:08:24 | INFO     | __main__:initialize_system:116 - 初始化RAG系统...
2025-07-23 10:08:24 | INFO     | rag.rag_system:initialize:29 - 加载嵌入模型: ./bge-m3
2025-07-23 10:08:24 | ERROR    | rag.rag_system:initialize:46 - RAG系统初始化失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: './bge-m3'.
2025-07-23 10:08:24 | ERROR    | __main__:initialize_system:119 - RAG系统初始化失败
2025-07-23 10:09:16 | INFO     | __main__:main:251 - ==================================================
2025-07-23 10:09:16 | INFO     | __main__:main:252 - 金融对话系统启动
2025-07-23 10:09:16 | INFO     | __main__:main:253 - ==================================================
2025-07-23 10:09:16 | INFO     | __main__:check_environment:50 - 检查环境配置...
2025-07-23 10:09:16 | INFO     | __main__:check_environment:106 - 环境配置检查通过
2025-07-23 10:09:16 | INFO     | __main__:check_environment:107 - 已配置的模型: 硅基流动
2025-07-23 10:09:16 | INFO     | __main__:main:276 - 服务器模式
2025-07-23 10:09:16 | INFO     | __main__:initialize_system:112 - 正在初始化金融对话系统...
2025-07-23 10:09:16 | INFO     | __main__:initialize_system:116 - 初始化RAG系统...
2025-07-23 10:09:16 | INFO     | rag.rag_system:initialize:29 - 加载嵌入模型: ./bge-m3
2025-07-23 10:09:16 | ERROR    | rag.rag_system:initialize:46 - RAG系统初始化失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: './bge-m3'.
2025-07-23 10:09:16 | ERROR    | __main__:initialize_system:119 - RAG系统初始化失败
2025-07-23 10:11:16 | INFO     | __main__:main:251 - ==================================================
2025-07-23 10:11:16 | INFO     | __main__:main:252 - 金融对话系统启动
2025-07-23 10:11:16 | INFO     | __main__:main:253 - ==================================================
2025-07-23 10:11:16 | INFO     | __main__:check_environment:50 - 检查环境配置...
2025-07-23 10:11:16 | INFO     | __main__:check_environment:106 - 环境配置检查通过
2025-07-23 10:11:16 | INFO     | __main__:check_environment:107 - 已配置的模型: 硅基流动
2025-07-23 10:11:16 | INFO     | __main__:main:276 - 服务器模式
2025-07-23 10:11:16 | INFO     | __main__:initialize_system:112 - 正在初始化金融对话系统...
2025-07-23 10:11:16 | INFO     | __main__:initialize_system:116 - 初始化RAG系统...
2025-07-23 10:11:16 | INFO     | rag.rag_system:initialize:29 - 加载嵌入模型: ./bge-m3
2025-07-23 10:11:16 | ERROR    | rag.rag_system:initialize:46 - RAG系统初始化失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: './bge-m3'.
2025-07-23 10:11:16 | ERROR    | __main__:initialize_system:119 - RAG系统初始化失败
2025-07-23 10:14:33 | INFO     | __main__:main:251 - ==================================================
2025-07-23 10:14:33 | INFO     | __main__:main:252 - 金融对话系统启动
2025-07-23 10:14:33 | INFO     | __main__:main:253 - ==================================================
2025-07-23 10:14:33 | INFO     | __main__:check_environment:50 - 检查环境配置...
2025-07-23 10:14:33 | INFO     | __main__:check_environment:106 - 环境配置检查通过
2025-07-23 10:14:33 | INFO     | __main__:check_environment:107 - 已配置的模型: 硅基流动
2025-07-23 10:14:33 | INFO     | __main__:main:276 - 服务器模式
2025-07-23 10:14:33 | INFO     | __main__:initialize_system:112 - 正在初始化金融对话系统...
2025-07-23 10:14:33 | INFO     | __main__:initialize_system:116 - 初始化RAG系统...
2025-07-23 10:14:33 | INFO     | rag.rag_system:initialize:29 - 加载嵌入模型: ./bge-m3
2025-07-23 10:14:33 | ERROR    | rag.rag_system:initialize:46 - RAG系统初始化失败: Repo id must use alphanumeric chars or '-', '_', '.', '--' and '..' are forbidden, '-' and '.' cannot start or end the name, max length is 96: './bge-m3'.
2025-07-23 10:14:33 | ERROR    | __main__:initialize_system:119 - RAG系统初始化失败
