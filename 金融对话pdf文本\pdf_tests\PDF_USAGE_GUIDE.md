# PDF文档处理功能使用指南

## 概述

本系统基于LangChain框架为金融对话系统添加了PDF文档检索功能，可以将PDF文件中的内容提取、分块并存储到Milvus向量数据库中，实现智能检索和问答。

## 功能特性

### 1. PDF文档处理
- 支持多种PDF加载方式（PyPDFLoader、UnstructuredPDFLoader）
- 智能文本分块，保持语义完整性
- 自动提取文档元数据
- 支持中文文档处理

### 2. 向量化存储
- 使用BGE-M3模型生成1024维向量
- 存储到Milvus向量数据库
- 支持余弦相似度搜索
- 自动索引优化

### 3. 文档管理
- PDF文件上传和存储
- 文档分类和标签管理
- 处理状态跟踪
- 批量处理支持

### 4. 智能检索
- 语义相似度搜索
- 多文档联合检索
- 结果相关性排序
- 上下文信息提取

## 系统架构

```
金融对话系统
├── rag/
│   ├── pdf_processor.py      # PDF文档处理器
│   ├── pdf_manager.py        # PDF管理服务
│   ├── rag_system.py         # RAG检索系统
│   └── milvus_manager.py     # Milvus数据库管理
├── api/
│   ├── pdf_api.py           # PDF API接口
│   └── api_server.py        # 主API服务器
└── pdf_texts/
    └── pdf_demo.py          # 使用示例
```

## 安装依赖

首先安装所需的Python包：

```bash
pip install -r idconfig/requirements.txt
```

主要新增依赖：
- `langchain>=0.1.0` - LangChain框架
- `langchain-community>=0.0.10` - LangChain社区组件
- `langchain-text-splitters>=0.0.1` - 文本分割器
- `pypdf>=3.17.0` - PDF处理
- `pymupdf>=1.23.0` - 高级PDF处理
- `pdfplumber>=0.10.0` - PDF表格和布局处理

## 快速开始

### 1. 初始化系统

```python
from rag.pdf_manager import PDFManager

# 创建PDF管理器
manager = PDFManager()

# 初始化系统
if manager.initialize():
    print("PDF管理器初始化成功")
else:
    print("初始化失败")
```

### 2. 上传并处理PDF文件

```python
# 上传并处理PDF文件（一步完成）
result = manager.upload_and_process_pdf(
    file_path="path/to/your/document.pdf",
    category="金融报告",
    description="2023年年度财务报告",
    tags=["财务", "年报", "2023"]
)

if result["success"]:
    print("PDF处理成功")
else:
    print(f"处理失败: {result['message']}")
```

### 3. 搜索PDF内容

```python
# 在PDF知识库中搜索
results = manager.search_pdfs("公司营收情况", top_k=5)

for result in results:
    print(f"内容: {result['content'][:100]}...")
    print(f"相似度: {result['score']:.3f}")
    print(f"来源: {result['source']}")
    print("---")
```

## API接口使用

### 启动API服务器

```bash
cd 金融对话
python api/api_server.py
```

服务器将在 `http://localhost:8000` 启动。

### API端点

#### 1. 上传PDF文件

```bash
curl -X POST "http://localhost:8000/pdf/upload-and-process" \
  -F "file=@document.pdf" \
  -F "category=金融报告" \
  -F "description=财务报告" \
  -F "tags=财务,报告"
```

#### 2. 列出PDF文件

```bash
curl "http://localhost:8000/pdf/list"
```

#### 3. 搜索PDF内容

```bash
curl "http://localhost:8000/pdf/search?query=营收&top_k=5"
```

#### 4. 获取统计信息

```bash
curl "http://localhost:8000/pdf/statistics"
```

#### 5. 批量处理目录

```bash
curl -X POST "http://localhost:8000/pdf/batch-process-directory" \
  -F "directory_path=/path/to/pdf/directory" \
  -F "category=金融文档"
```

## 配置说明

### 文本分块配置

在 `pdf_processor.py` 中可以调整文本分块参数：

```python
self.chunk_size = 1000      # 每个文本块的字符数
self.chunk_overlap = 200    # 文本块之间的重叠字符数
```

### 向量搜索配置

在 `config.py` 中可以调整搜索参数：

```python
TOP_K = 5                           # 默认检索数量
SIMILARITY_THRESHOLD = 0.5          # 相似度阈值
VECTOR_DIM = 1024                   # 向量维度（BGE-M3）
```

## 最佳实践

### 1. PDF文件准备
- 确保PDF文件是文本型（非扫描图片）
- 文件大小建议不超过50MB
- 文件名使用有意义的命名

### 2. 分类管理
- 为不同类型的文档设置合适的分类
- 使用标签进行细粒度管理
- 添加描述信息便于后续查找

### 3. 批量处理
- 对于大量文档，使用批量处理功能
- 按文档类型分目录存放
- 定期清理无用文档

### 4. 搜索优化
- 使用具体的关键词进行搜索
- 调整top_k参数获取合适数量的结果
- 根据相似度分数筛选结果

## 故障排除

### 1. PDF加载失败
- 检查PDF文件是否损坏
- 尝试不同的加载器（pypdf/unstructured）
- 确认文件路径正确

### 2. 向量化失败
- 检查BGE-M3模型是否正确加载
- 确认VECTOR_DIM配置为1024
- 检查文本内容是否为空

### 3. Milvus连接问题
- 确认Milvus服务正在运行
- 检查连接配置（host、port）
- 查看Milvus日志

### 4. 搜索结果为空
- 确认文档已成功处理
- 检查相似度阈值设置
- 尝试不同的搜索关键词

## 示例代码

运行完整示例：

```bash
cd 金融对话
python pdf_texts/pdf_demo.py
```

这将演示：
- PDF文档处理
- 知识库管理
- 搜索功能
- 批量处理

## 性能优化

### 1. 文本分块优化
- 根据文档类型调整chunk_size
- 适当设置chunk_overlap避免信息丢失
- 使用合适的分隔符

### 2. 向量检索优化
- 调整Milvus索引参数
- 使用合适的相似度阈值
- 批量插入提高效率

### 3. 内存管理
- 大文件分批处理
- 及时释放不用的资源
- 监控内存使用情况

## 扩展功能

### 1. 支持更多文档格式
- Word文档（.docx）
- Excel表格（.xlsx）
- PowerPoint演示文稿（.pptx）

### 2. 高级文本处理
- 表格内容提取
- 图片OCR识别
- 文档结构分析

### 3. 智能问答增强
- 多轮对话支持
- 上下文记忆
- 答案生成优化

## 联系支持

如有问题或建议，请查看：
- 系统日志文件
- API文档
- 示例代码
- 配置文件说明
