"""
PDF表格检索优化模块
专门针对金融文档中的表格数据进行优化检索和分析
"""
import os
import sys
import json
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import numpy as np

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    import pandas as pd
    import pdfplumber
    import fitz  # PyMuPDF
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    ADVANCED_FEATURES_AVAILABLE = True
except ImportError:
    ADVANCED_FEATURES_AVAILABLE = False
    logger.warning("高级表格检索功能需要安装: pandas, pdfplumber, pymupdf, scikit-learn")

from rag.rag_system import RAGSystem
from idconfig.config import Config

class TableRetrievalOptimizer:
    """表格检索优化器"""
    
    def __init__(self):
        self.config = Config()
        self.rag_system = RAGSystem()
        
        # 表格索引存储
        self.table_index_file = Path("data/table_index.json")
        self.table_index_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载表格索引
        self.table_index = self._load_table_index()
        
        # TF-IDF向量化器（用于表格内容检索）
        if ADVANCED_FEATURES_AVAILABLE:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=1000,
                stop_words=None,  # 保留所有词，因为金融术语很重要
                ngram_range=(1, 2)
            )
        
        # 金融表格关键词
        self.financial_keywords = {
            "财务指标": ["营收", "利润", "资产", "负债", "现金流", "毛利率", "净利率", "ROE", "ROA"],
            "市场数据": ["股价", "市值", "PE", "PB", "市盈率", "市净率", "成交量"],
            "业务数据": ["销售额", "订单", "客户", "产品", "服务", "增长率"],
            "风险指标": ["风险", "违约", "信用", "评级", "波动率", "VaR"]
        }
    
    def initialize(self):
        """初始化表格检索优化器"""
        try:
            if not self.rag_system.initialize():
                logger.error("RAG系统初始化失败")
                return False
            
            logger.info("表格检索优化器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"表格检索优化器初始化失败: {e}")
            return False
    
    def _load_table_index(self) -> Dict[str, Any]:
        """加载表格索引"""
        try:
            if self.table_index_file.exists():
                with open(self.table_index_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"加载表格索引失败: {e}")
            return {}
    
    def _save_table_index(self):
        """保存表格索引"""
        try:
            with open(self.table_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.table_index, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存表格索引失败: {e}")
    
    def extract_and_index_tables(self, file_path: str, file_hash: str) -> bool:
        """提取并索引PDF中的表格"""
        try:
            if not ADVANCED_FEATURES_AVAILABLE:
                logger.warning("高级功能不可用，跳过表格索引")
                return False
            
            logger.info(f"开始提取并索引表格: {file_path}")
            
            tables = []
            
            # 使用pdfplumber提取表格
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_tables = page.extract_tables()
                    
                    for table_num, table in enumerate(page_tables):
                        if table and len(table) > 1:
                            # 处理表格数据
                            table_info = self._process_table_for_indexing(
                                table, page_num + 1, table_num + 1, file_path, file_hash
                            )
                            if table_info:
                                tables.append(table_info)
            
            # 保存到索引
            if tables:
                self.table_index[file_hash] = {
                    "file_path": file_path,
                    "file_name": os.path.basename(file_path),
                    "tables": tables,
                    "total_tables": len(tables)
                }
                self._save_table_index()
                
                logger.info(f"成功索引 {len(tables)} 个表格")
                return True
            else:
                logger.warning("未找到可索引的表格")
                return False
                
        except Exception as e:
            logger.error(f"提取并索引表格失败: {e}")
            return False
    
    def _process_table_for_indexing(self, table: List[List], page_num: int, 
                                  table_num: int, file_path: str, file_hash: str) -> Optional[Dict[str, Any]]:
        """处理表格用于索引"""
        try:
            # 转换为DataFrame
            df = pd.DataFrame(table[1:], columns=table[0])
            df = df.dropna(how='all').fillna('')
            
            # 提取表格特征
            features = self._extract_table_features(df)
            
            # 生成搜索文本
            search_text = self._generate_table_search_text(df)
            
            # 识别表格类型
            table_type = self._classify_table_type(df, search_text)
            
            table_info = {
                "page_number": page_num,
                "table_number": table_num,
                "table_id": f"{file_hash}_p{page_num}_t{table_num}",
                "columns": list(df.columns),
                "row_count": len(df),
                "col_count": len(df.columns),
                "features": features,
                "search_text": search_text,
                "table_type": table_type,
                "data_sample": df.head(3).to_dict('records')  # 保存前3行作为样本
            }
            
            return table_info
            
        except Exception as e:
            logger.error(f"处理表格索引失败: {e}")
            return None
    
    def _extract_table_features(self, df: pd.DataFrame) -> Dict[str, Any]:
        """提取表格特征"""
        try:
            features = {
                "has_numeric_data": False,
                "has_percentage": False,
                "has_currency": False,
                "has_dates": False,
                "numeric_columns": [],
                "text_columns": [],
                "key_metrics": []
            }
            
            for col in df.columns:
                col_data = df[col].astype(str)
                
                # 检查数值型数据
                numeric_count = 0
                percentage_count = 0
                currency_count = 0
                date_count = 0
                
                for value in col_data:
                    value_clean = value.strip()
                    
                    # 数值检查
                    if re.search(r'\d+\.?\d*', value_clean):
                        numeric_count += 1
                    
                    # 百分比检查
                    if '%' in value_clean:
                        percentage_count += 1
                        features["has_percentage"] = True
                    
                    # 货币检查
                    if any(symbol in value_clean for symbol in ['¥', '$', '元', '万元', '亿元']):
                        currency_count += 1
                        features["has_currency"] = True
                    
                    # 日期检查
                    if re.search(r'\d{4}[-年]\d{1,2}[-月]?\d{0,2}日?', value_clean):
                        date_count += 1
                        features["has_dates"] = True
                
                # 分类列类型
                total_values = len(col_data)
                if numeric_count / total_values > 0.7:
                    features["numeric_columns"].append(col)
                    features["has_numeric_data"] = True
                else:
                    features["text_columns"].append(col)
                
                # 识别关键指标
                col_lower = str(col).lower()
                for category, keywords in self.financial_keywords.items():
                    if any(keyword in col_lower for keyword in keywords):
                        features["key_metrics"].append({
                            "column": col,
                            "category": category
                        })
            
            return features
            
        except Exception as e:
            logger.error(f"提取表格特征失败: {e}")
            return {}
    
    def _generate_table_search_text(self, df: pd.DataFrame) -> str:
        """生成表格的搜索文本"""
        try:
            search_parts = []
            
            # 添加列名
            search_parts.extend(df.columns.astype(str))
            
            # 添加所有单元格内容
            for _, row in df.iterrows():
                search_parts.extend(row.astype(str))
            
            # 清理和连接
            search_text = ' '.join(str(part) for part in search_parts if str(part).strip())
            
            return search_text
            
        except Exception as e:
            logger.error(f"生成搜索文本失败: {e}")
            return ""
    
    def _classify_table_type(self, df: pd.DataFrame, search_text: str) -> str:
        """分类表格类型"""
        try:
            search_lower = search_text.lower()
            
            # 财务报表
            if any(keyword in search_lower for keyword in ['资产负债', '利润', '现金流', '财务状况']):
                return "财务报表"
            
            # 市场数据
            elif any(keyword in search_lower for keyword in ['股价', '市值', '交易', '成交量']):
                return "市场数据"
            
            # 业绩指标
            elif any(keyword in search_lower for keyword in ['营收', '销售', '增长率', '毛利率']):
                return "业绩指标"
            
            # 风险数据
            elif any(keyword in search_lower for keyword in ['风险', '违约', '信用评级']):
                return "风险数据"
            
            # 一般数据表
            else:
                return "数据表格"
                
        except Exception as e:
            logger.error(f"分类表格类型失败: {e}")
            return "未知类型"

    def search_tables(self, query: str, table_type: str = None, top_k: int = 5) -> List[Dict[str, Any]]:
        """在表格索引中搜索"""
        try:
            if not ADVANCED_FEATURES_AVAILABLE:
                logger.warning("高级搜索功能不可用")
                return []

            results = []
            query_lower = query.lower()

            # 遍历所有索引的表格
            for file_hash, file_info in self.table_index.items():
                for table_info in file_info.get("tables", []):
                    # 类型过滤
                    if table_type and table_info.get("table_type") != table_type:
                        continue

                    # 计算相似度
                    similarity = self._calculate_table_similarity(query_lower, table_info)

                    if similarity > 0.1:  # 相似度阈值
                        result = {
                            **table_info,
                            "file_name": file_info["file_name"],
                            "file_path": file_info["file_path"],
                            "similarity": similarity,
                            "matched_content": self._extract_matched_content(query_lower, table_info)
                        }
                        results.append(result)

            # 按相似度排序
            results.sort(key=lambda x: x["similarity"], reverse=True)

            return results[:top_k]

        except Exception as e:
            logger.error(f"搜索表格失败: {e}")
            return []

    def _calculate_table_similarity(self, query: str, table_info: Dict[str, Any]) -> float:
        """计算查询与表格的相似度"""
        try:
            search_text = table_info.get("search_text", "").lower()

            # 关键词匹配得分
            keyword_score = 0
            query_words = query.split()

            for word in query_words:
                if word in search_text:
                    keyword_score += 1

            keyword_score = keyword_score / len(query_words) if query_words else 0

            # 列名匹配得分
            column_score = 0
            columns = [str(col).lower() for col in table_info.get("columns", [])]

            for word in query_words:
                for col in columns:
                    if word in col:
                        column_score += 0.5

            column_score = min(column_score, 1.0)

            # 表格类型匹配得分
            type_score = 0
            table_type = table_info.get("table_type", "").lower()
            if any(word in table_type for word in query_words):
                type_score = 0.3

            # 综合得分
            total_score = keyword_score * 0.6 + column_score * 0.3 + type_score * 0.1

            return total_score

        except Exception as e:
            logger.error(f"计算表格相似度失败: {e}")
            return 0.0

    def _extract_matched_content(self, query: str, table_info: Dict[str, Any]) -> List[str]:
        """提取匹配的内容片段"""
        try:
            matched_content = []
            query_words = query.split()

            # 检查列名匹配
            for col in table_info.get("columns", []):
                col_str = str(col).lower()
                if any(word in col_str for word in query_words):
                    matched_content.append(f"列名: {col}")

            # 检查数据样本匹配
            for row in table_info.get("data_sample", []):
                for key, value in row.items():
                    value_str = str(value).lower()
                    if any(word in value_str for word in query_words):
                        matched_content.append(f"{key}: {value}")

            return matched_content[:5]  # 最多返回5个匹配项

        except Exception as e:
            logger.error(f"提取匹配内容失败: {e}")
            return []

    def get_table_statistics(self) -> Dict[str, Any]:
        """获取表格统计信息"""
        try:
            stats = {
                "total_files": len(self.table_index),
                "total_tables": 0,
                "table_types": {},
                "column_distribution": {},
                "feature_distribution": {
                    "has_numeric_data": 0,
                    "has_percentage": 0,
                    "has_currency": 0,
                    "has_dates": 0
                }
            }

            for file_info in self.table_index.values():
                tables = file_info.get("tables", [])
                stats["total_tables"] += len(tables)

                for table_info in tables:
                    # 统计表格类型
                    table_type = table_info.get("table_type", "未知")
                    stats["table_types"][table_type] = stats["table_types"].get(table_type, 0) + 1

                    # 统计列数分布
                    col_count = table_info.get("col_count", 0)
                    col_range = f"{col_count//5*5}-{col_count//5*5+4}列"
                    stats["column_distribution"][col_range] = stats["column_distribution"].get(col_range, 0) + 1

                    # 统计特征分布
                    features = table_info.get("features", {})
                    for feature in stats["feature_distribution"]:
                        if features.get(feature, False):
                            stats["feature_distribution"][feature] += 1

            return stats

        except Exception as e:
            logger.error(f"获取表格统计信息失败: {e}")
            return {}

    def optimize_table_search(self, query: str, context: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """优化的表格搜索，结合上下文信息"""
        try:
            # 基础表格搜索
            table_results = self.search_tables(query, top_k=10)

            # 如果有上下文信息，进行进一步优化
            if context:
                table_results = self._refine_results_with_context(table_results, context)

            # 增强结果信息
            enhanced_results = []
            for result in table_results:
                enhanced_result = self._enhance_table_result(result, query)
                enhanced_results.append(enhanced_result)

            return enhanced_results[:5]  # 返回前5个最佳结果

        except Exception as e:
            logger.error(f"优化表格搜索失败: {e}")
            return []

    def _refine_results_with_context(self, results: List[Dict[str, Any]],
                                   context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据上下文信息优化搜索结果"""
        try:
            # 根据历史查询调整权重
            if "history" in context:
                for result in results:
                    # 如果之前查询过相似的表格，提高权重
                    for hist in context["history"]:
                        if result["file_name"] in hist.get("assistant_response", ""):
                            result["similarity"] *= 1.2

            # 根据知识库信息调整权重
            if "knowledge" in context:
                for result in results:
                    for knowledge in context["knowledge"]:
                        if any(word in result["search_text"].lower()
                              for word in knowledge.get("content", "").lower().split()):
                            result["similarity"] *= 1.1

            # 重新排序
            results.sort(key=lambda x: x["similarity"], reverse=True)

            return results

        except Exception as e:
            logger.error(f"根据上下文优化结果失败: {e}")
            return results

    def _enhance_table_result(self, result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """增强表格搜索结果"""
        try:
            enhanced = result.copy()

            # 添加表格摘要
            enhanced["summary"] = self._generate_table_summary(result)

            # 添加相关性解释
            enhanced["relevance_explanation"] = self._explain_relevance(result, query)

            # 添加建议的后续查询
            enhanced["suggested_queries"] = self._suggest_follow_up_queries(result)

            return enhanced

        except Exception as e:
            logger.error(f"增强表格结果失败: {e}")
            return result

    def _generate_table_summary(self, table_info: Dict[str, Any]) -> str:
        """生成表格摘要"""
        try:
            summary_parts = []

            # 基本信息
            summary_parts.append(f"这是一个{table_info.get('table_type', '数据')}表格")
            summary_parts.append(f"包含{table_info.get('row_count', 0)}行{table_info.get('col_count', 0)}列")

            # 特征信息
            features = table_info.get("features", {})
            if features.get("has_numeric_data"):
                summary_parts.append("包含数值数据")
            if features.get("has_currency"):
                summary_parts.append("包含货币金额")
            if features.get("has_percentage"):
                summary_parts.append("包含百分比数据")

            # 关键指标
            key_metrics = features.get("key_metrics", [])
            if key_metrics:
                metrics_text = "、".join([m["column"] for m in key_metrics[:3]])
                summary_parts.append(f"主要指标包括: {metrics_text}")

            return "，".join(summary_parts) + "。"

        except Exception as e:
            logger.error(f"生成表格摘要失败: {e}")
            return "表格摘要生成失败"

    def _explain_relevance(self, table_info: Dict[str, Any], query: str) -> str:
        """解释相关性"""
        try:
            explanations = []

            # 检查匹配的内容
            matched_content = table_info.get("matched_content", [])
            if matched_content:
                explanations.append(f"匹配内容: {', '.join(matched_content[:2])}")

            # 检查表格类型匹配
            table_type = table_info.get("table_type", "")
            if any(word in table_type.lower() for word in query.lower().split()):
                explanations.append(f"表格类型({table_type})与查询相关")

            return " | ".join(explanations) if explanations else "基于内容相似度匹配"

        except Exception as e:
            logger.error(f"解释相关性失败: {e}")
            return "相关性分析失败"

    def _suggest_follow_up_queries(self, table_info: Dict[str, Any]) -> List[str]:
        """建议后续查询"""
        try:
            suggestions = []

            # 基于表格类型的建议
            table_type = table_info.get("table_type", "")
            if "财务" in table_type:
                suggestions.extend(["营收增长情况", "利润率分析", "资产负债结构"])
            elif "市场" in table_type:
                suggestions.extend(["股价走势", "交易量分析", "市值变化"])

            # 基于关键指标的建议
            features = table_info.get("features", {})
            key_metrics = features.get("key_metrics", [])
            for metric in key_metrics[:2]:
                suggestions.append(f"{metric['column']}的详细分析")

            return suggestions[:3]  # 最多返回3个建议

        except Exception as e:
            logger.error(f"生成后续查询建议失败: {e}")
            return []
