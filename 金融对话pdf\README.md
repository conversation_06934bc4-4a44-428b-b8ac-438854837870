# 金融对话系统 (Financial Chat System)

基于FastAPI、RAG和大模型的智能金融对话系统，支持专业金融知识检索和对话历史管理。

An intelligent financial dialogue system based on FastAPI, RAG (Retrieval-Augmented Generation), and large language models, supporting professional financial knowledge retrieval and conversation history management.

## 🚀 快速开始 (Quick Start)

### 1. 环境准备 (Environment Setup)

```bash
# 克隆或下载项目 (Clone or download the project)
cd 金融对话

# 安装Python依赖 (Install Python dependencies)
pip install -r idconfig/requirements.txt
```

### 2. 下载BGE-M3嵌入模型 (Download BGE-M3 Embedding Model)

```bash
# 方法1: 使用git下载（推荐）(Method 1: Download with git - recommended)
git clone https://huggingface.co/BAAI/bge-m3 ./bge-m3

# 方法2: 使用Python脚本下载 (Method 2: Download with Python script)
python 检查向量模型/download_bge_m3.py

# 验证模型下载 (Verify model download)
python 检查向量模型/check_bge_m3.py
```

### 3. 配置环境变量 (Configure Environment Variables)

```bash
# 复制环境变量模板 (Copy environment template)
cp .env.example .env

# 编辑.env文件，配置API密钥 (Edit .env file and configure API keys)
# 至少需要配置一个大模型API密钥 (At least one LLM API key is required)
```

### 4. 启动Milvus向量数据库 (Start Milvus Vector Database)

```bash
# 下载Milvus docker-compose文件 (Download Milvus docker-compose file)
wget https://github.com/milvus-io/milvus/releases/download/v2.3.4/milvus-standalone-docker-compose.yml -O docker-compose.yml

# 启动Milvus (Start Milvus)
docker-compose up -d
```

### 5. 启动系统 (Start System)

```bash
# 初始化系统并添加示例数据 (Initialize system and add sample data)
python main.py --mode init --add-sample

# 启动API服务器 (Start API server)
python main.py --mode server

# 或使用快速启动脚本 (Or use quick start script)
python 测试/start.py
```

访问 http://localhost:8000 查看API文档 (Visit http://localhost:8000 to view API documentation)

## 🎯 系统特性 (System Features)

- 🤖 **多模型支持**: 支持OpenAI、智谱AI、通义千问、硅基流动、百度文心、Moonshot、DeepSeek、豆包、Claude、Gemini等10+种大模型
- 📚 **RAG检索**: 基于Milvus向量数据库的知识检索系统，使用BGE-M3嵌入模型
- 💾 **双知识库**: 专业信息知识库 + 历史对话存储
- 🚀 **FastAPI接口**: 高性能RESTful API服务
- 📊 **模块化设计**: 各功能模块独立，易于维护和扩展
- 🔍 **智能检索**: 基于余弦相似度的语义搜索
- 📝 **对话历史**: 自动保存和检索历史对话上下文

## 📁 项目结构 (Project Structure)

```
金融对话/
├── main.py                          # 主程序入口 (Main entry point)
├── .env.example                     # 环境变量配置模板 (Environment template)
├── README.md                        # 项目说明文档 (Project documentation)
├── idconfig/                        # 配置模块 (Configuration module)
│   ├── config.py                    # 配置管理 (Configuration management)
│   ├── requirements.txt             # 依赖包列表 (Dependencies list)
│   └── README.md                    # 详细说明文档 (Detailed documentation)
├── rag/                             # RAG检索模块 (RAG retrieval module)
│   ├── milvus_manager.py            # Milvus数据库管理 (Milvus database management)
│   └── rag_system.py                # RAG检索系统 (RAG retrieval system)
├── llm/                             # 大模型服务模块 (LLM service module)
│   └── llm_service.py               # 大模型调用服务 (LLM calling service)
├── api/                             # Web API模块 (Web API module)
│   └── api_server.py                # FastAPI服务器 (FastAPI server)
├── 检查向量模型/                     # 模型管理工具 (Model management tools)
│   ├── check_bge_m3.py              # BGE-M3模型检查 (BGE-M3 model checker)
│   └── download_bge_m3.py           # BGE-M3模型下载 (BGE-M3 model downloader)
└── 测试/                           # 测试和工具脚本 (Tests and utility scripts)
    ├── test_client.py               # 测试客户端 (Test client)
    ├── setup_env.py                 # 环境设置 (Environment setup)
    └── start.py                     # 启动脚本 (Startup script)
```

## 🔧 配置说明 (Configuration)

### 支持的大模型 (Supported LLMs)

| 提供商 (Provider) | 模型 (Models) | 配置变量 (Config Variables) |
|------------------|---------------|---------------------------|
| OpenAI | GPT-3.5, GPT-4 | `OPENAI_API_KEY` |
| 智谱AI | GLM-4 | `ZHIPU_API_KEY` |
| 通义千问 | Qwen-Turbo | `QWEN_API_KEY` |
| 硅基流动 | Qwen2.5, Llama3.1等 | `SILICONFLOW_API_KEY` |
| 百度文心 | ERNIE-Bot | `BAIDU_API_KEY`, `BAIDU_SECRET_KEY` |
| Moonshot AI | Kimi | `MOONSHOT_API_KEY` |
| DeepSeek | DeepSeek-Chat | `DEEPSEEK_API_KEY` |
| 字节豆包 | Doubao | `DOUBAO_API_KEY` |
| Anthropic | Claude-3 | `ANTHROPIC_API_KEY` |
| Google | Gemini-Pro | `GOOGLE_API_KEY` |

### 环境变量配置 (Environment Configuration)

详细配置请参考 `.env.example` 文件。主要配置项包括：

- **Milvus配置**: `MILVUS_HOST`, `MILVUS_PORT`
- **嵌入模型**: `EMBEDDING_MODEL=./bge-m3`, `VECTOR_DIM=1024`
- **检索配置**: `TOP_K=5`, `SIMILARITY_THRESHOLD=0.5`
- **API配置**: `API_HOST=0.0.0.0`, `API_PORT=8000`

## 🛠️ 使用示例 (Usage Examples)

### API调用示例 (API Usage Examples)

```bash
# 对话接口 (Chat API)
curl -X POST "http://localhost:8000/chat" \
     -H "Content-Type: application/json" \
     -d '{
       "message": "什么是股票？",
       "model_provider": "openai"
     }'

# 添加知识 (Add Knowledge)
curl -X POST "http://localhost:8000/knowledge/add" \
     -H "Content-Type: application/json" \
     -d '{
       "content": "股票是公司所有权的凭证",
       "category": "股票基础",
       "source": "金融教材"
     }'

# 搜索知识 (Search Knowledge)
curl -X GET "http://localhost:8000/knowledge/search?query=股票&top_k=5"
```

### Python客户端示例 (Python Client Example)

```python
import requests

# 发送对话请求 (Send chat request)
response = requests.post("http://localhost:8000/chat", json={
    "message": "请解释一下什么是市盈率？",
    "model_provider": "openai"
})

result = response.json()
print(f"回复: {result['response']}")
```

## 🧪 测试 (Testing)

```bash
# 系统功能测试 (System function test)
python main.py --mode test

# 交互式测试客户端 (Interactive test client)
python 测试/test_client.py

# 环境配置检查 (Environment configuration check)
python 测试/setup_env.py
```

## 📚 详细文档 (Detailed Documentation)

更详细的使用说明、API文档、扩展开发指南等，请查看：
- [详细说明文档](idconfig/README.md)
- [模型配置说明](idconfig/MODEL_CONFIG.md)

For more detailed usage instructions, API documentation, and extension development guides, please see:
- [Detailed Documentation](idconfig/README.md)
- [Model Configuration Guide](idconfig/MODEL_CONFIG.md)

## 🤝 贡献 (Contributing)

欢迎提交Issue和Pull Request来改进这个项目！

Welcome to submit Issues and Pull Requests to improve this project!

## 📄 许可证 (License)

MIT License

## 🆘 支持 (Support)

如有问题或建议，请通过以下方式联系：
- 提交 GitHub Issue
- 发送 Pull Request

If you have any questions or suggestions, please contact us through:
- Submit a GitHub Issue
- Send a Pull Request
