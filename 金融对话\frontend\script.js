// 全局变量
let currentSessionId = null;
let isLoading = false;
let chatHistory = [];
let settings = {
    apiUrl: 'http://localhost:8000',
    temperature: 0.7,
    theme: 'light',
    autoSave: true
};

// DOM元素
const elements = {
    sidebar: document.getElementById('sidebar'),
    sidebarToggle: document.getElementById('sidebarToggle'),
    mobileMenuBtn: document.getElementById('mobileMenuBtn'),
    newChatBtn: document.getElementById('newChatBtn'),
    historyList: document.getElementById('historyList'),
    messagesContainer: document.getElementById('messagesContainer'),
    messageInput: document.getElementById('messageInput'),
    sendBtn: document.getElementById('sendBtn'),
    attachBtn: document.getElementById('attachBtn'),
    charCount: document.getElementById('charCount'),
    typingIndicator: document.getElementById('typingIndicator'),
    modelSelect: document.getElementById('modelSelect'),
    themeToggle: document.getElementById('themeToggle'),
    settingsBtn: document.getElementById('settingsBtn'),
    settingsModal: document.getElementById('settingsModal'),
    settingsModalClose: document.getElementById('settingsModalClose'),
    settingsCancel: document.getElementById('settingsCancel'),
    settingsSave: document.getElementById('settingsSave'),
    pdfModal: document.getElementById('pdfModal'),
    pdfModalClose: document.getElementById('pdfModalClose'),
    uploadArea: document.getElementById('uploadArea'),
    selectFileBtn: document.getElementById('selectFileBtn'),
    pdfFileInput: document.getElementById('pdfFileInput'),
    loadingOverlay: document.getElementById('loadingOverlay'),
    knowledgeBtn: document.getElementById('knowledgeBtn'),
    searchBtn: document.getElementById('searchBtn'),
    knowledgeModal: document.getElementById('knowledgeModal'),
    knowledgeModalClose: document.getElementById('knowledgeModalClose'),
    searchModal: document.getElementById('searchModal'),
    searchModalClose: document.getElementById('searchModalClose')
};

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    loadSettings();
    setupEventListeners();
    checkServerConnection();
    loadAvailableModels();
    generateNewSession();
    
    // 自动调整输入框高度
    autoResizeTextarea();
}

// 设置事件监听器
function setupEventListeners() {
    // 侧边栏切换
    elements.sidebarToggle.addEventListener('click', toggleSidebar);
    elements.mobileMenuBtn.addEventListener('click', toggleSidebar);
    
    // 新对话
    elements.newChatBtn.addEventListener('click', startNewChat);
    
    // 消息发送
    elements.sendBtn.addEventListener('click', sendMessage);
    elements.messageInput.addEventListener('keydown', handleInputKeydown);
    elements.messageInput.addEventListener('input', updateCharCount);
    
    // 文件上传
    elements.attachBtn.addEventListener('click', () => elements.pdfModal.classList.add('show'));
    elements.selectFileBtn.addEventListener('click', () => elements.pdfFileInput.click());
    elements.pdfFileInput.addEventListener('change', handleFileSelect);
    
    // 主题切换
    elements.themeToggle.addEventListener('click', toggleTheme);
    
    // 设置
    elements.settingsBtn.addEventListener('click', () => elements.settingsModal.classList.add('show'));
    elements.settingsModalClose.addEventListener('click', () => elements.settingsModal.classList.remove('show'));
    elements.settingsCancel.addEventListener('click', () => elements.settingsModal.classList.remove('show'));
    elements.settingsSave.addEventListener('click', saveSettings);
    
    // PDF模态框
    elements.pdfModalClose.addEventListener('click', () => elements.pdfModal.classList.remove('show'));

    // 知识库管理
    elements.knowledgeBtn.addEventListener('click', () => elements.knowledgeModal.classList.add('show'));
    elements.knowledgeModalClose.addEventListener('click', () => elements.knowledgeModal.classList.remove('show'));

    // 搜索功能
    elements.searchBtn.addEventListener('click', () => elements.searchModal.classList.add('show'));
    elements.searchModalClose.addEventListener('click', () => elements.searchModal.classList.remove('show'));

    // 拖拽上传
    setupDragAndDrop();

    // 知识库标签页切换
    setupKnowledgeTabs();

    // 知识库功能
    setupKnowledgeFeatures();
    
    // 点击模态框外部关闭
    elements.settingsModal.addEventListener('click', (e) => {
        if (e.target === elements.settingsModal) {
            elements.settingsModal.classList.remove('show');
        }
    });
    
    elements.pdfModal.addEventListener('click', (e) => {
        if (e.target === elements.pdfModal) {
            elements.pdfModal.classList.remove('show');
        }
    });
}

// 切换侧边栏
function toggleSidebar() {
    elements.sidebar.classList.toggle('open');
}

// 开始新对话
function startNewChat() {
    generateNewSession();
    clearMessages();
    addWelcomeMessage();
}

// 生成新会话ID
function generateNewSession() {
    currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 清空消息
function clearMessages() {
    elements.messagesContainer.innerHTML = '';
}

// 添加欢迎消息
function addWelcomeMessage() {
    const welcomeMessage = {
        type: 'assistant',
        content: '您好！我是您的金融对话助手，可以为您解答各种金融问题。请随时向我提问！',
        timestamp: new Date()
    };
    displayMessage(welcomeMessage);
}

// 处理输入框按键
function handleInputKeydown(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
}

// 更新字符计数
function updateCharCount() {
    const count = elements.messageInput.value.length;
    elements.charCount.textContent = `${count}/2000`;
    
    if (count > 1800) {
        elements.charCount.style.color = 'var(--error-color)';
    } else if (count > 1500) {
        elements.charCount.style.color = 'var(--warning-color)';
    } else {
        elements.charCount.style.color = 'var(--text-muted)';
    }
}

// 自动调整输入框高度
function autoResizeTextarea() {
    elements.messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 128) + 'px';
    });
}

// 发送消息
async function sendMessage() {
    const message = elements.messageInput.value.trim();
    if (!message || isLoading) return;
    
    // 显示用户消息
    const userMessage = {
        type: 'user',
        content: message,
        timestamp: new Date()
    };
    displayMessage(userMessage);
    
    // 清空输入框
    elements.messageInput.value = '';
    elements.messageInput.style.height = 'auto';
    updateCharCount();
    
    // 显示加载状态
    setLoading(true);
    
    try {
        const response = await fetch(`${settings.apiUrl}/chat`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                session_id: currentSessionId,
                model_provider: elements.modelSelect.value,
                temperature: settings.temperature
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // 显示助手回复
        const assistantMessage = {
            type: 'assistant',
            content: data.response,
            timestamp: new Date(),
            context: data.context_used,
            related_images: data.related_images || []
        };
        displayMessage(assistantMessage);
        
        // 保存到历史记录
        if (settings.autoSave) {
            saveToHistory(userMessage, assistantMessage);
        }
        
    } catch (error) {
        console.error('发送消息失败:', error);
        showError('发送消息失败: ' + error.message);
    } finally {
        setLoading(false);
    }
}

// 显示消息
function displayMessage(message) {
    const messageElement = document.createElement('div');
    messageElement.className = `message ${message.type}-message`;

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = message.type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const content = document.createElement('div');
    content.className = 'message-content';

    const text = document.createElement('div');
    text.className = 'message-text';
    text.textContent = message.content;

    // 添加相关图片（如果有）
    if (message.related_images && message.related_images.length > 0) {
        const imagesContainer = document.createElement('div');
        imagesContainer.className = 'related-images';

        const imagesTitle = document.createElement('div');
        imagesTitle.className = 'images-title';
        imagesTitle.innerHTML = '<i class="fas fa-images"></i> 相关图片';
        imagesContainer.appendChild(imagesTitle);

        const imagesGrid = document.createElement('div');
        imagesGrid.className = 'images-grid';

        message.related_images.forEach(img => {
            const imageCard = createImageCard(img);
            imagesGrid.appendChild(imageCard);
        });

        imagesContainer.appendChild(imagesGrid);
        content.appendChild(imagesContainer);
    }

    const time = document.createElement('div');
    time.className = 'message-time';
    time.textContent = formatTime(message.timestamp);

    content.appendChild(text);
    content.appendChild(time);

    messageElement.appendChild(avatar);
    messageElement.appendChild(content);

    elements.messagesContainer.appendChild(messageElement);
    elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
}

// 创建图片卡片
function createImageCard(imageInfo) {
    const card = document.createElement('div');
    card.className = 'image-card';

    const imageContainer = document.createElement('div');
    imageContainer.className = 'image-container';

    const img = document.createElement('img');
    img.src = `${settings.apiUrl}/multimodal/image/${imageInfo.image_id}`;
    img.alt = imageInfo.description || '图片';
    img.className = 'image-preview';
    img.loading = 'lazy';

    // 图片加载错误处理
    img.onerror = function() {
        this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NSA2NUw5NSA3NUwxMTUgNTVMMTM1IDc1VjEwNUg2NVY3NUw4NSA2NVoiIGZpbGw9IiNEMUQ1REIiLz4KPGNpcmNsZSBjeD0iODUiIGN5PSI1NSIgcj0iNSIgZmlsbD0iI0QxRDVEQiIvPgo8dGV4dCB4PSIxMDAiIHk9IjEyNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNjc3NDhGIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYfml6Dms5XliqDovb08L3RleHQ+Cjwvc3ZnPgo=';
        this.alt = '图片加载失败';
    };

    // 点击图片放大预览
    img.onclick = function() {
        showImageModal(imageInfo);
    };

    imageContainer.appendChild(img);

    const info = document.createElement('div');
    info.className = 'image-info';

    const title = document.createElement('div');
    title.className = 'image-title';
    title.textContent = `${imageInfo.pdf_name} - 第${imageInfo.page_number}页`;

    const description = document.createElement('div');
    description.className = 'image-description';
    description.textContent = imageInfo.description || '无描述';

    const metadata = document.createElement('div');
    metadata.className = 'image-metadata';
    metadata.innerHTML = `
        <span class="image-type">${imageInfo.image_type}</span>
        <span class="image-score">相关度: ${(imageInfo.final_score * 100).toFixed(1)}%</span>
    `;

    info.appendChild(title);
    info.appendChild(description);
    info.appendChild(metadata);

    card.appendChild(imageContainer);
    card.appendChild(info);

    return card;
}

// 显示图片模态框
function showImageModal(imageInfo) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.innerHTML = `
        <div class="image-modal-content">
            <div class="image-modal-header">
                <h3>图片详情</h3>
                <button class="image-modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="image-modal-body">
                <div class="image-modal-preview">
                    <img src="${settings.apiUrl}/multimodal/image/${imageInfo.image_id}"
                         alt="${imageInfo.description || '图片'}"
                         class="modal-image">
                </div>
                <div class="image-modal-info">
                    <div class="info-item">
                        <label>来源文档:</label>
                        <span>${imageInfo.pdf_name}</span>
                    </div>
                    <div class="info-item">
                        <label>页码:</label>
                        <span>第 ${imageInfo.page_number} 页</span>
                    </div>
                    <div class="info-item">
                        <label>图片类型:</label>
                        <span>${imageInfo.image_type}</span>
                    </div>
                    <div class="info-item">
                        <label>描述:</label>
                        <span>${imageInfo.description || '无描述'}</span>
                    </div>
                    <div class="info-item">
                        <label>相关度评分:</label>
                        <span>${(imageInfo.final_score * 100).toFixed(1)}%</span>
                    </div>
                    <div class="info-item">
                        <label>图片尺寸:</label>
                        <span>${imageInfo.width} × ${imageInfo.height}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(modal);

    // 显示模态框
    setTimeout(() => modal.classList.add('show'), 10);

    // 关闭事件
    const closeBtn = modal.querySelector('.image-modal-close');
    closeBtn.onclick = () => closeImageModal(modal);

    modal.onclick = (e) => {
        if (e.target === modal) {
            closeImageModal(modal);
        }
    };

    // ESC键关闭
    const handleEsc = (e) => {
        if (e.key === 'Escape') {
            closeImageModal(modal);
            document.removeEventListener('keydown', handleEsc);
        }
    };
    document.addEventListener('keydown', handleEsc);
}

// 关闭图片模态框
function closeImageModal(modal) {
    modal.classList.remove('show');
    setTimeout(() => {
        if (modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }, 300);
}

// 设置加载状态
function setLoading(loading) {
    isLoading = loading;
    elements.sendBtn.disabled = loading;
    elements.typingIndicator.style.display = loading ? 'flex' : 'none';
    
    if (loading) {
        elements.sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    } else {
        elements.sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i>';
    }
}

// 格式化时间
function formatTime(date) {
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 小于1分钟
        return '刚刚';
    } else if (diff < 3600000) { // 小于1小时
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 小于1天
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        return date.toLocaleDateString();
    }
}

// 显示错误消息
function showError(message) {
    const errorMessage = {
        type: 'assistant',
        content: `❌ ${message}`,
        timestamp: new Date()
    };
    displayMessage(errorMessage);
}

// 检查服务器连接
async function checkServerConnection() {
    elements.loadingOverlay.style.display = 'flex';
    
    try {
        const response = await fetch(`${settings.apiUrl}/health`);
        if (response.ok) {
            console.log('服务器连接正常');
        } else {
            throw new Error('服务器响应异常');
        }
    } catch (error) {
        console.error('服务器连接失败:', error);
        showError('无法连接到服务器，请检查服务器是否正在运行');
    } finally {
        elements.loadingOverlay.style.display = 'none';
    }
}

// 加载可用模型
async function loadAvailableModels() {
    try {
        const response = await fetch(`${settings.apiUrl}/models`);
        if (response.ok) {
            const data = await response.json();
            updateModelSelect(data.available_models);
        }
    } catch (error) {
        console.error('加载模型列表失败:', error);
    }
}

// 更新模型选择器
function updateModelSelect(models) {
    // 清空现有选项
    elements.modelSelect.innerHTML = '';
    
    // 添加可用模型
    models.forEach(model => {
        const option = document.createElement('option');
        option.value = model;
        option.textContent = getModelDisplayName(model);
        elements.modelSelect.appendChild(option);
    });
}

// 获取模型显示名称
function getModelDisplayName(model) {
    const modelNames = {
        'openai': 'OpenAI',
        'zhipu': '智谱AI',
        'qwen': '通义千问',
        'siliconflow': '硅基流动',
        'baidu': '百度文心',
        'moonshot': 'Moonshot',
        'deepseek': 'DeepSeek',
        'doubao': '豆包',
        'anthropic': 'Claude',
        'google': 'Gemini'
    };
    return modelNames[model] || model;
}

// 主题切换
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';

    document.documentElement.setAttribute('data-theme', newTheme);
    settings.theme = newTheme;

    // 更新图标
    const icon = elements.themeToggle.querySelector('i');
    icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';

    saveSettings();
}

// 加载设置
function loadSettings() {
    const savedSettings = localStorage.getItem('chatSettings');
    if (savedSettings) {
        settings = { ...settings, ...JSON.parse(savedSettings) };
    }

    // 应用设置
    document.documentElement.setAttribute('data-theme', settings.theme);

    // 更新UI
    document.getElementById('apiUrl').value = settings.apiUrl;
    document.getElementById('temperature').value = settings.temperature;
    document.getElementById('temperatureValue').textContent = settings.temperature;
    document.getElementById('themeSelect').value = settings.theme;
    document.getElementById('autoSave').checked = settings.autoSave;

    // 更新主题图标
    const icon = elements.themeToggle.querySelector('i');
    icon.className = settings.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';

    // 温度滑块事件
    document.getElementById('temperature').addEventListener('input', function() {
        document.getElementById('temperatureValue').textContent = this.value;
    });
}

// 保存设置
function saveSettings() {
    settings.apiUrl = document.getElementById('apiUrl').value;
    settings.temperature = parseFloat(document.getElementById('temperature').value);
    settings.theme = document.getElementById('themeSelect').value;
    settings.autoSave = document.getElementById('autoSave').checked;

    localStorage.setItem('chatSettings', JSON.stringify(settings));

    // 应用主题
    document.documentElement.setAttribute('data-theme', settings.theme);

    // 更新主题图标
    const icon = elements.themeToggle.querySelector('i');
    icon.className = settings.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';

    elements.settingsModal.classList.remove('show');

    // 显示保存成功消息
    showSuccess('设置已保存');
}

// 显示成功消息
function showSuccess(message) {
    const successMessage = {
        type: 'assistant',
        content: `✅ ${message}`,
        timestamp: new Date()
    };
    displayMessage(successMessage);
}

// 保存到历史记录
function saveToHistory(userMessage, assistantMessage) {
    const historyItem = {
        id: currentSessionId,
        title: userMessage.content.substring(0, 30) + (userMessage.content.length > 30 ? '...' : ''),
        timestamp: new Date(),
        messages: [userMessage, assistantMessage]
    };

    // 检查是否已存在
    const existingIndex = chatHistory.findIndex(item => item.id === currentSessionId);
    if (existingIndex >= 0) {
        chatHistory[existingIndex].messages.push(userMessage, assistantMessage);
    } else {
        chatHistory.unshift(historyItem);
    }

    // 限制历史记录数量
    if (chatHistory.length > 50) {
        chatHistory = chatHistory.slice(0, 50);
    }

    // 保存到本地存储
    localStorage.setItem('chatHistory', JSON.stringify(chatHistory));

    // 更新历史列表UI
    updateHistoryList();
}

// 更新历史列表
function updateHistoryList() {
    elements.historyList.innerHTML = '';

    chatHistory.forEach(item => {
        const historyElement = document.createElement('div');
        historyElement.className = 'history-item';
        historyElement.innerHTML = `
            <div class="history-item-title">${item.title}</div>
            <div class="history-item-time">${formatTime(new Date(item.timestamp))}</div>
        `;

        historyElement.addEventListener('click', () => loadHistoryItem(item));
        elements.historyList.appendChild(historyElement);
    });
}

// 加载历史对话
function loadHistoryItem(item) {
    currentSessionId = item.id;
    clearMessages();

    item.messages.forEach(message => {
        displayMessage(message);
    });

    // 更新活跃状态
    document.querySelectorAll('.history-item').forEach(el => el.classList.remove('active'));
    event.currentTarget.classList.add('active');
}

// 设置拖拽上传
function setupDragAndDrop() {
    const uploadArea = elements.uploadArea;

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        uploadArea.classList.add('dragover');
    }

    function unhighlight() {
        uploadArea.classList.remove('dragover');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const files = e.dataTransfer.files;
        handleFiles(files);
    }
}

// 处理文件选择
function handleFileSelect(e) {
    const files = e.target.files;
    handleFiles(files);
}

// 处理文件
function handleFiles(files) {
    if (files.length === 0) return;

    const file = files[0];
    if (file.type !== 'application/pdf') {
        showError('请选择PDF文件');
        return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB限制
        showError('文件大小不能超过10MB');
        return;
    }

    uploadPDF(file);
}

// 上传PDF
async function uploadPDF(file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', '金融文档');
    formData.append('description', `上传的PDF文件: ${file.name}`);
    formData.append('tags', '金融,PDF,知识库');

    // 显示上传进度
    const progressContainer = document.getElementById('uploadProgress');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    progressContainer.style.display = 'block';
    progressFill.style.width = '0%';
    progressText.textContent = '上传中...';

    try {
        // 使用upload-and-process端点，一步完成上传和处理
        const response = await fetch(`${settings.apiUrl}/pdf/upload-and-process`, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`上传失败: ${response.statusText}`);
        }

        const data = await response.json();

        // 模拟进度，显示处理过程
        let progress = 0;
        const stages = [
            { progress: 20, text: '文件上传中...' },
            { progress: 40, text: '解析PDF内容...' },
            { progress: 60, text: '文本分块处理...' },
            { progress: 80, text: '生成向量嵌入...' },
            { progress: 100, text: '存储到知识库...' }
        ];

        let stageIndex = 0;
        const interval = setInterval(() => {
            if (stageIndex < stages.length) {
                const stage = stages[stageIndex];
                progressFill.style.width = stage.progress + '%';
                progressText.textContent = stage.text;
                stageIndex++;
            } else {
                clearInterval(interval);
                progressText.textContent = '处理完成！';

                setTimeout(() => {
                    elements.pdfModal.classList.remove('show');
                    progressContainer.style.display = 'none';
                    showSuccess(`PDF文件 "${file.name}" 已成功上传并添加到金融知识库`);

                    // 显示处理结果的详细信息
                    if (data.success) {
                        const detailMessage = {
                            type: 'assistant',
                            content: `✅ PDF处理完成！\n\n📄 文件名: ${file.name}\n📚 已添加到: 金融专业信息和术语知识库\n🔍 现在您可以向我询问关于该文档的问题了！`,
                            timestamp: new Date()
                        };
                        displayMessage(detailMessage);
                    }
                }, 1000);
            }
        }, 800);

    } catch (error) {
        console.error('上传PDF失败:', error);
        showError('上传PDF失败: ' + error.message);
        progressContainer.style.display = 'none';
    }
}

// 初始化时加载历史记录
function loadChatHistory() {
    const savedHistory = localStorage.getItem('chatHistory');
    if (savedHistory) {
        chatHistory = JSON.parse(savedHistory);
        updateHistoryList();
    }
}

// 设置知识库标签页切换
function setupKnowledgeTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');

            // 移除所有活跃状态
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // 添加活跃状态
            btn.classList.add('active');
            document.getElementById(targetTab + 'Tab').classList.add('active');
        });
    });
}

// 设置知识库功能
function setupKnowledgeFeatures() {
    // 添加知识
    document.getElementById('addKnowledgeBtn').addEventListener('click', addKnowledge);

    // 搜索知识
    document.getElementById('searchKnowledgeBtn').addEventListener('click', searchKnowledge);
    document.getElementById('knowledgeSearchInput').addEventListener('keydown', (e) => {
        if (e.key === 'Enter') searchKnowledge();
    });

    // 搜索历史
    document.getElementById('searchHistoryBtn').addEventListener('click', searchHistory);
    document.getElementById('historySearchInput').addEventListener('keydown', (e) => {
        if (e.key === 'Enter') searchHistory();
    });

    // 刷新统计
    document.getElementById('refreshStatsBtn').addEventListener('click', refreshKnowledgeStats);
}

// 添加知识
async function addKnowledge() {
    const content = document.getElementById('knowledgeContent').value.trim();
    const category = document.getElementById('knowledgeCategory').value.trim();
    const source = document.getElementById('knowledgeSource').value.trim();

    if (!content || !category) {
        showError('请填写知识内容和分类');
        return;
    }

    try {
        const response = await fetch(`${settings.apiUrl}/knowledge/add`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                content: content,
                category: category,
                source: source
            })
        });

        if (!response.ok) {
            throw new Error(`添加失败: ${response.statusText}`);
        }

        const data = await response.json();
        showSuccess('知识添加成功');

        // 清空表单
        document.getElementById('knowledgeContent').value = '';
        document.getElementById('knowledgeCategory').value = '';
        document.getElementById('knowledgeSource').value = '';

    } catch (error) {
        console.error('添加知识失败:', error);
        showError('添加知识失败: ' + error.message);
    }
}

// 搜索知识
async function searchKnowledge() {
    const query = document.getElementById('knowledgeSearchInput').value.trim();
    if (!query) {
        showError('请输入搜索关键词');
        return;
    }

    try {
        const response = await fetch(`${settings.apiUrl}/knowledge/search?query=${encodeURIComponent(query)}&top_k=10`);

        if (!response.ok) {
            throw new Error(`搜索失败: ${response.statusText}`);
        }

        const data = await response.json();
        displayKnowledgeSearchResults(data.results);

    } catch (error) {
        console.error('搜索知识失败:', error);
        showError('搜索知识失败: ' + error.message);
    }
}

// 显示知识搜索结果
function displayKnowledgeSearchResults(results) {
    const container = document.getElementById('knowledgeSearchResults');

    if (results.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <p>未找到相关知识</p>
            </div>
        `;
        return;
    }

    container.innerHTML = results.map(result => `
        <div class="search-result-item">
            <div class="result-content">${result.content}</div>
            <div class="result-meta">
                <span class="result-category">${result.category}</span>
                <span>相似度: ${(result.score * 100).toFixed(1)}%</span>
            </div>
        </div>
    `).join('');
}

// 搜索历史
async function searchHistory() {
    const query = document.getElementById('historySearchInput').value.trim();
    if (!query) {
        showError('请输入搜索关键词');
        return;
    }

    try {
        const response = await fetch(`${settings.apiUrl}/history/search?query=${encodeURIComponent(query)}&top_k=10`);

        if (!response.ok) {
            throw new Error(`搜索失败: ${response.statusText}`);
        }

        const data = await response.json();
        displayHistorySearchResults(data.results);

    } catch (error) {
        console.error('搜索历史失败:', error);
        showError('搜索历史失败: ' + error.message);
    }
}

// 显示历史搜索结果
function displayHistorySearchResults(results) {
    const container = document.getElementById('historySearchResults');

    if (results.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <p>未找到相关对话</p>
            </div>
        `;
        return;
    }

    container.innerHTML = results.map(result => `
        <div class="search-result-item">
            <div class="result-content">
                <strong>用户:</strong> ${result.user_query}<br>
                <strong>助手:</strong> ${result.assistant_response}
            </div>
            <div class="result-meta">
                <span>${new Date(result.timestamp).toLocaleString()}</span>
                <span>相似度: ${(result.score * 100).toFixed(1)}%</span>
            </div>
        </div>
    `).join('');
}

// 刷新知识库统计
async function refreshKnowledgeStats() {
    try {
        // 这里可以添加获取知识库统计的API调用
        // 暂时使用模拟数据
        document.getElementById('totalKnowledge').textContent = '暂无数据';
        document.getElementById('totalCategories').textContent = '暂无数据';

        showSuccess('统计信息已刷新');

    } catch (error) {
        console.error('刷新统计失败:', error);
        showError('刷新统计失败: ' + error.message);
    }
}

// 在初始化函数中添加历史记录加载
function initializeApp() {
    loadSettings();
    setupEventListeners();
    checkServerConnection();
    loadAvailableModels();
    loadChatHistory(); // 添加这行
    generateNewSession();

    // 自动调整输入框高度
    autoResizeTextarea();
}
