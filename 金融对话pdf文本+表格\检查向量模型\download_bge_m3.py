"""
BGE-M3模型下载脚本
自动下载BGE-M3模型到本地
"""
import os
import sys
from pathlib import Path

def download_with_git():
    """使用git下载模型"""
    print("使用git下载BGE-M3模型...")
    
    try:
        import subprocess
        
        # 检查git是否可用
        result = subprocess.run(["git", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ git未安装或不可用")
            return False
        
        # 下载模型
        cmd = ["git", "clone", "https://huggingface.co/BAAI/bge-m3", "./bge-m3"]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ BGE-M3模型下载成功")
            return True
        else:
            print(f"❌ git下载失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ git下载出错: {e}")
        return False

def download_with_huggingface_hub():
    """使用huggingface_hub下载模型"""
    print("使用huggingface_hub下载BGE-M3模型...")
    
    try:
        from huggingface_hub import snapshot_download
        
        print("正在下载模型文件...")
        snapshot_download(
            repo_id="BAAI/bge-m3",
            local_dir="./bge-m3",
            local_dir_use_symlinks=False
        )
        
        print("✓ BGE-M3模型下载成功")
        return True
        
    except ImportError:
        print("❌ huggingface_hub未安装")
        print("请运行: pip install huggingface_hub")
        return False
    except Exception as e:
        print(f"❌ huggingface_hub下载失败: {e}")
        return False

def check_existing_model():
    """检查是否已存在模型"""
    model_path = Path("./bge-m3")
    
    if model_path.exists():
        print("检查现有模型...")
        
        # 检查关键文件
        key_files = ["config.json", "pytorch_model.bin"]
        missing_files = []
        
        for file_name in key_files:
            if not (model_path / file_name).exists():
                missing_files.append(file_name)
        
        if not missing_files:
            print("✓ BGE-M3模型已存在且完整")
            return True
        else:
            print(f"⚠️  模型文件不完整，缺少: {', '.join(missing_files)}")
            return False
    
    return False

def main():
    """主函数"""
    print("BGE-M3模型下载工具")
    print("=" * 40)
    
    # 检查是否已存在模型
    if check_existing_model():
        print("\n模型已存在，无需重新下载")
        return True
    
    print("\n开始下载BGE-M3模型...")
    print("模型大小约1.5GB，请确保网络连接稳定")
    
    # 尝试不同的下载方法
    methods = [
        ("Git", download_with_git),
        ("Hugging Face Hub", download_with_huggingface_hub)
    ]
    
    for method_name, method_func in methods:
        print(f"\n尝试使用{method_name}下载...")
        
        if method_func():
            print(f"\n🎉 使用{method_name}下载成功！")
            
            # 验证下载结果
            if check_existing_model():
                print("✓ 模型文件验证通过")
                print("\n下一步:")
                print("1. 运行 python check_bge_m3.py 验证模型")
                print("2. 运行 python main.py --mode server 启动系统")
                return True
            else:
                print("❌ 模型文件验证失败")
                continue
        else:
            print(f"❌ {method_name}下载失败")
            continue
    
    print("\n❌ 所有下载方法都失败了")
    print("\n手动下载建议:")
    print("1. 访问 https://huggingface.co/BAAI/bge-m3")
    print("2. 下载所有文件到 ./bge-m3 目录")
    print("3. 或使用其他下载工具")
    
    return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n用户中断下载")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 下载过程中出现错误: {e}")
        sys.exit(1)
