"""
文本格式化工具
用于清理和格式化对话系统的回答，去除特殊字符和格式化标记
"""
import re


class TextFormatter:
    """文本格式化器"""
    
    def __init__(self):
        # 定义需要清理的模式
        self.patterns = {
            # Markdown标题标记
            'markdown_headers': r'#{1,6}\s*',
            
            # 粗体标记
            'bold_asterisk': r'\*\*(.*?)\*\*',
            'bold_underscore': r'__(.*?)__',
            
            # 斜体标记
            'italic_asterisk': r'\*(.*?)\*',
            'italic_underscore': r'_(.*?)_',
            
            # 代码块标记
            'code_block': r'```[\s\S]*?```',
            'inline_code': r'`([^`]+)`',
            
            # 链接标记
            'links': r'\[([^\]]+)\]\([^\)]+\)',
            
            # 列表标记
            'unordered_list': r'^\s*[-*+]\s+',
            'ordered_list': r'^\s*\d+\.\s+',
            
            # 引用标记
            'blockquote': r'^\s*>\s*',
            
            # 分隔线
            'horizontal_rule': r'^[-*_]{3,}$',
            
            # 表格标记
            'table_separator': r'\|',
            
            # HTML标签
            'html_tags': r'<[^>]+>',
            
            # 特殊符号
            'special_symbols': r'[▪▫●○■□◆◇★☆]',
            
            # 多余的空白字符
            'multiple_spaces': r'\s{2,}',
            'multiple_newlines': r'\n{3,}',
            
            # 特殊格式标记
            'section_markers': r'###\s*\d+\.\s*\*\*',
            'summary_markers': r'###\s*总结\s*',
            'conclusion_markers': r'###\s*结论\s*',
        }
    
    def clean_markdown(self, text: str) -> str:
        """清理Markdown格式标记"""
        if not text:
            return text
        
        # 清理标题标记
        text = re.sub(self.patterns['markdown_headers'], '', text, flags=re.MULTILINE)
        
        # 清理粗体标记，保留内容
        text = re.sub(self.patterns['bold_asterisk'], r'\1', text)
        text = re.sub(self.patterns['bold_underscore'], r'\1', text)
        
        # 清理斜体标记，保留内容
        text = re.sub(self.patterns['italic_asterisk'], r'\1', text)
        text = re.sub(self.patterns['italic_underscore'], r'\1', text)
        
        # 清理代码块
        text = re.sub(self.patterns['code_block'], '', text, flags=re.DOTALL)
        text = re.sub(self.patterns['inline_code'], r'\1', text)
        
        # 清理链接，保留文本
        text = re.sub(self.patterns['links'], r'\1', text)
        
        # 清理列表标记
        text = re.sub(self.patterns['unordered_list'], '', text, flags=re.MULTILINE)
        text = re.sub(self.patterns['ordered_list'], '', text, flags=re.MULTILINE)
        
        # 清理引用标记
        text = re.sub(self.patterns['blockquote'], '', text, flags=re.MULTILINE)
        
        # 清理分隔线
        text = re.sub(self.patterns['horizontal_rule'], '', text, flags=re.MULTILINE)
        
        # 清理表格分隔符
        text = re.sub(self.patterns['table_separator'], '', text)
        
        return text
    
    def clean_html(self, text: str) -> str:
        """清理HTML标签"""
        if not text:
            return text
        
        # 移除HTML标签
        text = re.sub(self.patterns['html_tags'], '', text)
        
        return text
    
    def clean_special_symbols(self, text: str) -> str:
        """清理特殊符号"""
        if not text:
            return text
        
        # 移除特殊符号
        text = re.sub(self.patterns['special_symbols'], '', text)
        
        return text
    
    def clean_section_markers(self, text: str) -> str:
        """清理章节标记"""
        if not text:
            return text
        
        # 清理特殊的章节标记
        text = re.sub(self.patterns['section_markers'], '', text)
        text = re.sub(self.patterns['summary_markers'], '总结', text)
        text = re.sub(self.patterns['conclusion_markers'], '结论', text)
        
        # 清理类似 "### 1. **销售部的构成**" 的格式
        text = re.sub(r'###\s*\d+\.\s*\*\*(.*?)\*\*', r'\1', text)
        
        # 清理类似 "### 2. **大客户销售部与销售处的构成**" 的格式
        text = re.sub(r'###\s*\d+\.\s*(.*?)(?=\n|$)', r'\1', text)
        
        # 清理单独的 ### 标记
        text = re.sub(r'###\s*', '', text)
        
        return text
    
    def normalize_whitespace(self, text: str) -> str:
        """规范化空白字符"""
        if not text:
            return text
        
        # 合并多个空格为单个空格
        text = re.sub(self.patterns['multiple_spaces'], ' ', text)
        
        # 合并多个换行为最多两个换行
        text = re.sub(self.patterns['multiple_newlines'], '\n\n', text)
        
        # 清理行首行尾空白
        lines = text.split('\n')
        lines = [line.strip() for line in lines]
        text = '\n'.join(lines)
        
        # 清理整体首尾空白
        text = text.strip()
        
        return text
    
    def format_lists(self, text: str) -> str:
        """格式化列表，使用简单的破折号"""
        if not text:
            return text
        
        # 将各种列表标记统一为简单的破折号
        lines = text.split('\n')
        formatted_lines = []
        
        for line in lines:
            # 检查是否是列表项
            if re.match(r'^\s*[-*+•▪▫●○■□]\s+', line):
                # 提取列表内容
                content = re.sub(r'^\s*[-*+•▪▫●○■□]\s+', '', line)
                formatted_lines.append(f"- {content}")
            elif re.match(r'^\s*\d+\.\s+', line):
                # 数字列表保持原样，但简化格式
                content = re.sub(r'^\s*\d+\.\s+', '', line)
                # 获取数字
                match = re.match(r'^\s*(\d+)\.\s+', line)
                if match:
                    num = match.group(1)
                    formatted_lines.append(f"{num}. {content}")
                else:
                    formatted_lines.append(line)
            else:
                formatted_lines.append(line)
        
        return '\n'.join(formatted_lines)
    
    def clean_response(self, text: str) -> str:
        """
        清理对话回答的完整流程
        
        Args:
            text: 原始回答文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return text
        
        # 1. 清理章节标记（最重要的，针对您的例子）
        text = self.clean_section_markers(text)
        
        # 2. 清理Markdown格式
        text = self.clean_markdown(text)
        
        # 3. 清理HTML标签
        text = self.clean_html(text)
        
        # 4. 清理特殊符号
        text = self.clean_special_symbols(text)
        
        # 5. 格式化列表
        text = self.format_lists(text)
        
        # 6. 规范化空白字符
        text = self.normalize_whitespace(text)
        
        return text
    
    def clean_response_conservative(self, text: str) -> str:
        """
        保守的清理方式，只清理最明显的格式标记
        
        Args:
            text: 原始回答文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return text
        
        # 只清理最明显的格式标记
        # 1. 清理 ### 标记
        text = re.sub(r'###\s*', '', text)
        
        # 2. 清理粗体标记 **text**
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        
        # 3. 清理多余空白
        text = re.sub(r'\s{2,}', ' ', text)
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # 4. 清理首尾空白
        text = text.strip()
        
        return text


# 创建全局实例
text_formatter = TextFormatter()


def clean_response_text(text: str, conservative: bool = False) -> str:
    """
    便捷函数：清理回答文本
    
    Args:
        text: 原始文本
        conservative: 是否使用保守清理模式
        
    Returns:
        清理后的文本
    """
    if conservative:
        return text_formatter.clean_response_conservative(text)
    else:
        return text_formatter.clean_response(text)


# 示例使用
if __name__ == "__main__":
    # 测试示例
    sample_text = """### 1. **销售部的构成**  
根据提供的上下文信息，武汉力源信息技术股份有限公司的**销售部**下设四个部门：
- **渠道销售部**：负责全国分销渠道管理及区域市场开发。
- **电话及网络销售部**：负责大众市场客户直接销售及技术支持。
- **大客户销售部**：负责锁定大额采购的大客户（如细分领域龙头企业）。
- **国际贸易部**：负责公司产品的国际贸易业务。

### 2. **大客户销售部与销售处的构成**  
- **上下文信息不足**：提供的资料未明确说明**大客户销售部由几个销售处构成**或其具体管理的销售处数量。"""
    
    formatter = TextFormatter()
    
    print("原始文本:")
    print(sample_text)
    print("\n" + "="*50 + "\n")
    
    print("清理后文本:")
    cleaned = formatter.clean_response(sample_text)
    print(cleaned)
    print("\n" + "="*50 + "\n")
    
    print("保守清理后文本:")
    conservative_cleaned = formatter.clean_response_conservative(sample_text)
    print(conservative_cleaned)
