#!/usr/bin/env python3
"""
图片功能性能优化脚本
检查和优化图片检索性能
"""
import sys
import time
import json
from pathlib import Path
from typing import List, Dict, Any

# 添加父目录到Python路径
parent_dir = Path(__file__).parent
sys.path.insert(0, str(parent_dir))

from loguru import logger

def benchmark_image_search():
    """基准测试图片搜索性能"""
    logger.info("开始图片搜索性能测试...")
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        retriever = MultimodalImageRetriever()
        if not retriever.initialize():
            logger.warning("多模态检索器初始化失败")
            return
        
        # 测试查询
        test_queries = [
            "收入图表",
            "利润分析",
            "市场份额",
            "增长趋势",
            "财务报表",
            "柱状图",
            "折线图",
            "数据分析"
        ]
        
        total_time = 0
        total_results = 0
        
        for query in test_queries:
            start_time = time.time()
            results = retriever.search_images_by_text(query, top_k=5)
            end_time = time.time()
            
            search_time = end_time - start_time
            total_time += search_time
            total_results += len(results)
            
            logger.info(f"查询: '{query}' - 耗时: {search_time:.3f}s, 结果: {len(results)}张")
        
        avg_time = total_time / len(test_queries)
        avg_results = total_results / len(test_queries)
        
        logger.info(f"\n性能统计:")
        logger.info(f"  - 平均搜索时间: {avg_time:.3f}s")
        logger.info(f"  - 平均结果数量: {avg_results:.1f}张")
        logger.info(f"  - 总搜索时间: {total_time:.3f}s")
        
        # 性能建议
        if avg_time > 1.0:
            logger.warning("⚠️ 搜索速度较慢，建议优化:")
            logger.warning("  - 检查图片索引大小")
            logger.warning("  - 考虑使用更快的向量搜索算法")
            logger.warning("  - 优化预筛选逻辑")
        else:
            logger.info("✅ 搜索性能良好")
            
    except ImportError:
        logger.warning("多模态功能不可用")
    except Exception as e:
        logger.error(f"性能测试失败: {e}")

def check_image_index_health():
    """检查图片索引健康状况"""
    logger.info("检查图片索引健康状况...")
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        retriever = MultimodalImageRetriever()
        if not retriever.initialize():
            logger.warning("多模态检索器初始化失败")
            return
        
        stats = retriever.get_image_statistics()
        
        logger.info(f"图片索引统计:")
        logger.info(f"  - 总文件数: {stats.get('total_files', 0)}")
        logger.info(f"  - 总图片数: {stats.get('total_images', 0)}")
        logger.info(f"  - 图片类型分布: {stats.get('image_types', {})}")
        logger.info(f"  - 尺寸分布: {stats.get('size_distribution', {})}")
        
        # 健康检查
        total_images = stats.get('total_images', 0)
        if total_images == 0:
            logger.warning("⚠️ 没有找到图片，请检查:")
            logger.warning("  - 是否已上传PDF文档")
            logger.warning("  - PDF中是否包含图片")
            logger.warning("  - 图片提取功能是否正常")
        elif total_images < 10:
            logger.warning(f"⚠️ 图片数量较少 ({total_images}张)，建议:")
            logger.warning("  - 上传更多包含图片的PDF文档")
            logger.warning("  - 检查图片提取设置")
        else:
            logger.info(f"✅ 图片索引健康，共有 {total_images} 张图片")
        
        # 检查图片类型分布
        image_types = stats.get('image_types', {})
        if not image_types:
            logger.warning("⚠️ 没有图片类型信息，可能需要重新处理图片")
        else:
            logger.info(f"图片类型分布合理: {image_types}")
            
    except ImportError:
        logger.warning("多模态功能不可用")
    except Exception as e:
        logger.error(f"健康检查失败: {e}")

def optimize_search_parameters():
    """优化搜索参数"""
    logger.info("优化搜索参数...")
    
    try:
        from rag.multimodal_retrieval import MultimodalImageRetriever
        
        retriever = MultimodalImageRetriever()
        if not retriever.initialize():
            logger.warning("多模态检索器初始化失败")
            return
        
        # 测试不同的相似度阈值
        test_query = "收入图表"
        thresholds = [0.1, 0.15, 0.2, 0.25, 0.3]
        
        logger.info(f"测试查询: '{test_query}'")
        logger.info("测试不同相似度阈值的效果:")
        
        for threshold in thresholds:
            # 这里需要修改检索器的阈值设置
            results = retriever.search_images_by_text(test_query, top_k=10)
            filtered_results = [r for r in results if r.get('final_score', 0) >= threshold]
            
            logger.info(f"  阈值 {threshold}: {len(filtered_results)} 张图片")
        
        # 建议最优阈值
        logger.info("\n建议:")
        logger.info("  - 阈值 0.15-0.2 通常能提供较好的精度和召回率平衡")
        logger.info("  - 对于要求高精度的场景，可以使用 0.25-0.3")
        logger.info("  - 对于要求高召回率的场景，可以使用 0.1-0.15")
        
    except ImportError:
        logger.warning("多模态功能不可用")
    except Exception as e:
        logger.error(f"参数优化失败: {e}")

def check_memory_usage():
    """检查内存使用情况"""
    logger.info("检查内存使用情况...")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        logger.info(f"当前内存使用:")
        logger.info(f"  - RSS: {memory_info.rss / 1024 / 1024:.1f} MB")
        logger.info(f"  - VMS: {memory_info.vms / 1024 / 1024:.1f} MB")
        
        # 检查系统内存
        system_memory = psutil.virtual_memory()
        logger.info(f"系统内存:")
        logger.info(f"  - 总内存: {system_memory.total / 1024 / 1024 / 1024:.1f} GB")
        logger.info(f"  - 可用内存: {system_memory.available / 1024 / 1024 / 1024:.1f} GB")
        logger.info(f"  - 使用率: {system_memory.percent:.1f}%")
        
        if system_memory.percent > 80:
            logger.warning("⚠️ 系统内存使用率较高，可能影响性能")
        else:
            logger.info("✅ 系统内存使用正常")
            
    except ImportError:
        logger.warning("psutil未安装，无法检查内存使用")
    except Exception as e:
        logger.error(f"内存检查失败: {e}")

def main():
    """主函数"""
    logger.info("🔧 开始图片功能性能优化")
    
    # 检查图片索引健康状况
    check_image_index_health()
    
    # 性能基准测试
    benchmark_image_search()
    
    # 优化搜索参数
    optimize_search_parameters()
    
    # 检查内存使用
    check_memory_usage()
    
    logger.info("🎯 性能优化完成")
    logger.info("\n优化建议总结:")
    logger.info("1. 确保有足够的图片数据用于测试")
    logger.info("2. 根据实际需求调整相似度阈值")
    logger.info("3. 监控内存使用，避免内存不足")
    logger.info("4. 定期清理无用的图片索引")

if __name__ == "__main__":
    main()
