# 金融对话助手 - 前端界面

这是一个现代化的金融对话助手前端界面，提供简洁清晰的用户体验和丰富的功能。

## 功能特性

### 🎯 核心功能
- **智能对话**: 与金融AI助手进行自然语言对话
- **多模型支持**: 支持OpenAI、智谱AI、通义千问等多种大语言模型
- **实时响应**: 流畅的对话体验，支持打字指示器
- **会话管理**: 自动保存对话历史，支持多会话切换
- **PDF知识库**: 上传PDF文件自动添加到金融专业信息和术语知识库

### 📚 知识库管理
- **添加知识**: 手动添加金融知识到系统知识库
- **知识搜索**: 快速搜索知识库中的相关内容
- **分类管理**: 按类别组织和管理知识内容
- **统计信息**: 查看知识库的统计数据

### 📄 文档处理
- **PDF上传**: 支持拖拽上传PDF文档到金融专业信息和术语知识库
- **自动处理**: 上传后自动解析、分块、向量化并存储到Milvus知识库
- **智能问答**: 上传后可直接询问文档相关问题
- **进度显示**: 实时显示上传、处理和存储进度
- **支持格式**: 金融报告、研究报告、招股说明书等PDF文档

### 🔍 搜索功能
- **对话搜索**: 搜索历史对话记录
- **知识搜索**: 在知识库中查找相关信息
- **相似度评分**: 显示搜索结果的相关性评分

### 🎨 界面特性
- **响应式设计**: 适配桌面和移动设备
- **主题切换**: 支持浅色/深色主题
- **现代化UI**: 简洁美观的Material Design风格
- **动画效果**: 流畅的过渡动画和交互反馈

## 文件结构

```
frontend/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript功能文件
└── README.md           # 说明文档
```

## 快速开始

### 方法一：使用启动脚本（推荐）

1. **启动后端服务**：
```bash
cd 金融对话
python main.py --mode server
```

2. **启动前端界面**：
```bash
# Windows用户
cd frontend
start_frontend.bat

# 或者使用Python脚本
python start_frontend.py
```

### 方法二：手动启动

1. **启动后端服务**：
```bash
cd 金融对话
python main.py --mode server
```

2. **启动前端服务器**：
```bash
cd frontend
python -m http.server 8080
# 然后在浏览器中访问 http://localhost:8080
```

### 3. 配置设置
点击右上角的设置按钮，配置：
- API地址（默认：http://localhost:8000）
- 温度参数（控制回复的创造性）
- 主题偏好
- 自动保存选项

## PDF上传到知识库功能

### 📚 知识库集成
- 上传的PDF文件会自动添加到 **金融专业信息和术语知识库**
- 系统会自动进行文本提取、分块处理和向量化
- 处理完成后，您可以直接询问文档相关问题

### 📄 支持的文档类型
- 金融研究报告
- 公司年报和财务报表
- 招股说明书
- 投资分析报告
- 金融法规文件
- 其他金融相关PDF文档

### 🔄 处理流程
1. **上传**: 拖拽或选择PDF文件
2. **解析**: 提取PDF文本内容
3. **分块**: 将长文本分割为适当大小的块
4. **向量化**: 使用BGE-M3模型生成向量嵌入
5. **存储**: 保存到Milvus向量数据库
6. **完成**: 可以开始询问相关问题

## 界面说明

### 主要区域
1. **侧边栏**: 包含新对话按钮、历史记录、功能按钮和设置
2. **工具栏**: 显示标题、模型选择器和主题切换按钮
3. **消息区域**: 显示对话内容，支持滚动查看历史消息
4. **输入区域**: 文本输入框、附件按钮和发送按钮

### 功能按钮
- **新对话**: 开始一个新的对话会话
- **知识库**: 管理和搜索知识库内容
- **搜索**: 搜索历史对话记录
- **设置**: 配置应用参数和偏好

### 快捷键
- `Enter`: 发送消息
- `Shift + Enter`: 换行
- `Esc`: 关闭模态框

## 技术特性

### 前端技术
- **HTML5**: 语义化标记
- **CSS3**: 现代样式和动画
- **Vanilla JavaScript**: 原生JS，无框架依赖
- **Font Awesome**: 图标库
- **CSS Variables**: 主题系统

### 响应式设计
- 移动端优化的侧边栏
- 自适应布局
- 触摸友好的交互

### 性能优化
- 懒加载历史记录
- 防抖搜索
- 本地存储缓存
- 异步API调用

## 浏览器兼容性

支持现代浏览器：
- Chrome 70+
- Firefox 65+
- Safari 12+
- Edge 79+

## 自定义配置

### 主题定制
可以通过修改CSS变量来自定义主题颜色：

```css
:root {
    --primary-color: #2563eb;
    --background-color: #ffffff;
    /* 更多变量... */
}
```

### API配置
在设置中可以修改API地址和参数，支持：
- 自定义API端点
- 温度参数调节
- 模型选择

## 故障排除

### 常见问题
1. **无法连接服务器**: 检查后端服务是否启动，API地址是否正确
2. **消息发送失败**: 确认网络连接和API密钥配置
3. **文件上传失败**: 检查文件格式（仅支持PDF）和大小限制（10MB）

### 调试模式
打开浏览器开发者工具查看控制台日志，获取详细错误信息。

## 更新日志

### v1.0.0
- 初始版本发布
- 基础对话功能
- 知识库管理
- PDF文档上传
- 主题切换
- 响应式设计

## 贡献指南

欢迎提交问题和改进建议！

## 许可证

MIT License
