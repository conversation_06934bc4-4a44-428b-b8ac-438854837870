"""
图像质量增强模块
专门用于PDF提取图像的噪音去除、质量提升和预处理
"""
import os
import sys
import numpy as np
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
from loguru import logger

try:
    from PIL import Image, ImageFilter, ImageEnhance, ImageOps
    import cv2
    from scipy import ndimage
    from skimage import filters, morphology, restoration, exposure
    from skimage.util import img_as_ubyte, img_as_float
    ENHANCEMENT_AVAILABLE = True
except ImportError:
    ENHANCEMENT_AVAILABLE = False
    logger.warning("图像增强功能需要安装: opencv-python, scikit-image, scipy")

class ImageQualityEnhancer:
    """图像质量增强器"""
    
    def __init__(self):
        self.enhancement_enabled = ENHANCEMENT_AVAILABLE
        
        # 图像质量评估阈值
        self.min_contrast_threshold = 0.1
        self.min_sharpness_threshold = 0.3
        self.noise_threshold = 0.15
        
        # 增强参数
        self.denoise_params = {
            'gaussian_sigma': 0.8,
            'bilateral_d': 9,
            'bilateral_sigma_color': 75,
            'bilateral_sigma_space': 75
        }
        
        self.enhancement_params = {
            'contrast_factor': 1.2,
            'sharpness_factor': 1.1,
            'brightness_factor': 1.05
        }
    
    def enhance_image(self, image: Image.Image) -> Tuple[Image.Image, Dict[str, Any]]:
        """
        增强图像质量
        
        Args:
            image: 输入PIL图像
            
        Returns:
            增强后的图像和处理信息
        """
        if not self.enhancement_enabled:
            return image, {"enhanced": False, "reason": "增强功能不可用"}
        
        try:
            # 图像质量评估
            quality_metrics = self._assess_image_quality(image)
            
            # 转换为numpy数组进行处理
            img_array = np.array(image)
            original_mode = image.mode
            
            # 处理步骤记录
            processing_steps = []
            
            # 1. 噪音检测和去除
            if quality_metrics['noise_level'] > self.noise_threshold:
                img_array = self._remove_noise(img_array)
                processing_steps.append("噪音去除")
            
            # 2. 对比度增强
            if quality_metrics['contrast'] < self.min_contrast_threshold:
                img_array = self._enhance_contrast(img_array)
                processing_steps.append("对比度增强")
            
            # 3. 锐化处理
            if quality_metrics['sharpness'] < self.min_sharpness_threshold:
                img_array = self._sharpen_image(img_array)
                processing_steps.append("锐化处理")
            
            # 4. 亮度调整
            img_array = self._adjust_brightness(img_array)
            processing_steps.append("亮度调整")
            
            # 5. 边缘保护的平滑
            img_array = self._edge_preserving_smooth(img_array)
            processing_steps.append("边缘保护平滑")
            
            # 转换回PIL图像
            enhanced_image = Image.fromarray(img_array, mode=original_mode)
            
            # 后处理质量评估
            enhanced_metrics = self._assess_image_quality(enhanced_image)
            
            processing_info = {
                "enhanced": True,
                "steps": processing_steps,
                "original_metrics": quality_metrics,
                "enhanced_metrics": enhanced_metrics,
                "improvement": {
                    "contrast": enhanced_metrics['contrast'] - quality_metrics['contrast'],
                    "sharpness": enhanced_metrics['sharpness'] - quality_metrics['sharpness'],
                    "noise_reduction": quality_metrics['noise_level'] - enhanced_metrics['noise_level']
                }
            }
            
            logger.info(f"图像增强完成: {', '.join(processing_steps)}")
            return enhanced_image, processing_info
            
        except Exception as e:
            logger.error(f"图像增强失败: {e}")
            return image, {"enhanced": False, "error": str(e)}
    
    def _assess_image_quality(self, image: Image.Image) -> Dict[str, float]:
        """评估图像质量"""
        try:
            # 转换为灰度图像进行分析
            gray_image = image.convert('L')
            img_array = np.array(gray_image)
            
            # 计算对比度 (标准差)
            contrast = np.std(img_array) / 255.0
            
            # 计算锐度 (拉普拉斯算子方差)
            laplacian = cv2.Laplacian(img_array, cv2.CV_64F)
            sharpness = np.var(laplacian) / 10000.0  # 归一化
            
            # 估算噪音水平 (高频成分)
            noise_level = self._estimate_noise_level(img_array)
            
            # 计算亮度
            brightness = np.mean(img_array) / 255.0
            
            return {
                'contrast': contrast,
                'sharpness': sharpness,
                'noise_level': noise_level,
                'brightness': brightness
            }
            
        except Exception as e:
            logger.error(f"图像质量评估失败: {e}")
            return {
                'contrast': 0.5,
                'sharpness': 0.5,
                'noise_level': 0.1,
                'brightness': 0.5
            }
    
    def _estimate_noise_level(self, img_array: np.ndarray) -> float:
        """估算图像噪音水平"""
        try:
            # 使用高通滤波器检测高频噪音
            kernel = np.array([[-1, -1, -1],
                              [-1,  8, -1],
                              [-1, -1, -1]])
            
            filtered = cv2.filter2D(img_array.astype(np.float32), -1, kernel)
            noise_level = np.std(filtered) / 255.0
            
            return min(noise_level, 1.0)
            
        except Exception:
            return 0.1
    
    def _remove_noise(self, img_array: np.ndarray) -> np.ndarray:
        """去除图像噪音"""
        try:
            if len(img_array.shape) == 3:
                # 彩色图像使用双边滤波
                denoised = cv2.bilateralFilter(
                    img_array,
                    self.denoise_params['bilateral_d'],
                    self.denoise_params['bilateral_sigma_color'],
                    self.denoise_params['bilateral_sigma_space']
                )
            else:
                # 灰度图像使用高斯滤波
                denoised = cv2.GaussianBlur(
                    img_array,
                    (5, 5),
                    self.denoise_params['gaussian_sigma']
                )
            
            return denoised
            
        except Exception as e:
            logger.warning(f"噪音去除失败: {e}")
            return img_array
    
    def _enhance_contrast(self, img_array: np.ndarray) -> np.ndarray:
        """增强对比度"""
        try:
            # 使用CLAHE (对比度限制自适应直方图均衡化)
            if len(img_array.shape) == 3:
                # 彩色图像：在LAB色彩空间中处理L通道
                lab = cv2.cvtColor(img_array, cv2.COLOR_RGB2LAB)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                lab[:, :, 0] = clahe.apply(lab[:, :, 0])
                enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
            else:
                # 灰度图像直接应用CLAHE
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                enhanced = clahe.apply(img_array)
            
            return enhanced
            
        except Exception as e:
            logger.warning(f"对比度增强失败: {e}")
            return img_array
    
    def _sharpen_image(self, img_array: np.ndarray) -> np.ndarray:
        """锐化图像"""
        try:
            # 使用Unsharp Mask锐化
            kernel = np.array([[-1, -1, -1],
                              [-1,  9, -1],
                              [-1, -1, -1]])
            
            if len(img_array.shape) == 3:
                # 彩色图像分通道处理
                sharpened = np.zeros_like(img_array)
                for i in range(img_array.shape[2]):
                    sharpened[:, :, i] = cv2.filter2D(img_array[:, :, i], -1, kernel)
            else:
                # 灰度图像
                sharpened = cv2.filter2D(img_array, -1, kernel)
            
            # 限制像素值范围
            sharpened = np.clip(sharpened, 0, 255)
            
            return sharpened.astype(np.uint8)
            
        except Exception as e:
            logger.warning(f"图像锐化失败: {e}")
            return img_array
    
    def _adjust_brightness(self, img_array: np.ndarray) -> np.ndarray:
        """调整亮度"""
        try:
            # 计算当前亮度
            current_brightness = np.mean(img_array)
            target_brightness = 128  # 目标亮度
            
            # 计算调整因子
            if current_brightness > 0:
                adjustment = target_brightness / current_brightness
                adjustment = np.clip(adjustment, 0.8, 1.2)  # 限制调整范围
                
                adjusted = img_array * adjustment
                adjusted = np.clip(adjusted, 0, 255)
                
                return adjusted.astype(np.uint8)
            
            return img_array
            
        except Exception as e:
            logger.warning(f"亮度调整失败: {e}")
            return img_array
    
    def _edge_preserving_smooth(self, img_array: np.ndarray) -> np.ndarray:
        """边缘保护的平滑处理"""
        try:
            # 使用边缘保护滤波器
            if len(img_array.shape) == 3:
                smoothed = cv2.edgePreservingFilter(img_array, flags=1, sigma_s=50, sigma_r=0.4)
            else:
                # 灰度图像使用双边滤波
                smoothed = cv2.bilateralFilter(img_array, 9, 75, 75)
            
            return smoothed
            
        except Exception as e:
            logger.warning(f"边缘保护平滑失败: {e}")
            return img_array
    
    def filter_low_quality_images(self, image: Image.Image, 
                                 min_quality_score: float = 0.3) -> Tuple[bool, Dict[str, Any]]:
        """
        过滤低质量图像
        
        Args:
            image: 输入图像
            min_quality_score: 最低质量分数阈值
            
        Returns:
            (是否通过质量检查, 质量信息)
        """
        try:
            quality_metrics = self._assess_image_quality(image)
            
            # 计算综合质量分数
            quality_score = (
                quality_metrics['contrast'] * 0.3 +
                quality_metrics['sharpness'] * 0.3 +
                (1 - quality_metrics['noise_level']) * 0.2 +
                min(quality_metrics['brightness'], 1 - quality_metrics['brightness']) * 2 * 0.2
            )
            
            passed = quality_score >= min_quality_score
            
            quality_info = {
                "quality_score": quality_score,
                "passed": passed,
                "metrics": quality_metrics,
                "threshold": min_quality_score
            }
            
            if not passed:
                logger.info(f"图像质量不达标: 得分={quality_score:.3f}, 阈值={min_quality_score}")
            
            return passed, quality_info
            
        except Exception as e:
            logger.error(f"图像质量检查失败: {e}")
            return True, {"error": str(e)}  # 出错时默认通过
    
    def detect_image_type(self, image: Image.Image) -> str:
        """
        检测图像类型（图表、照片、文档等）
        
        Args:
            image: 输入图像
            
        Returns:
            图像类型
        """
        try:
            img_array = np.array(image.convert('L'))
            
            # 计算边缘密度
            edges = cv2.Canny(img_array, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # 计算颜色复杂度
            if image.mode == 'RGB':
                colors = len(set(tuple(pixel) for pixel in np.array(image).reshape(-1, 3)))
                color_complexity = colors / (image.width * image.height)
            else:
                color_complexity = 0.1
            
            # 基于特征判断图像类型
            if edge_density > 0.1 and color_complexity < 0.1:
                return "图表/图形"
            elif edge_density < 0.05 and color_complexity > 0.3:
                return "照片/图片"
            elif edge_density > 0.05:
                return "文档/文本"
            else:
                return "其他"
                
        except Exception as e:
            logger.error(f"图像类型检测失败: {e}")
            return "未知"
