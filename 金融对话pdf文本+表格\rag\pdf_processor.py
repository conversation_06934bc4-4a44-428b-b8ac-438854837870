"""
PDF文档处理模块
使用LangChain框架处理PDF文件，提取文本并分块存储到Milvus知识库
"""
import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger

# LangChain相关导入
from langchain.document_loaders import PyPDFLoader, UnstructuredPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, CharacterTextSplitter
from langchain.schema import Document

# 表格解析相关导入
try:
    import pdfplumber
    import pandas as pd
    import fitz  # PyMuPDF
    ADVANCED_PDF_AVAILABLE = True
except ImportError:
    ADVANCED_PDF_AVAILABLE = False
    logger.warning("高级PDF处理库未安装，表格解析功能不可用")

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.rag_system import RAGSystem
from idconfig.config import Config

class PDFProcessor:
    """PDF文档处理器"""
    
    def __init__(self):
        self.config = Config()
        self.rag_system = RAGSystem()

        # 文本分割器配置
        self.chunk_size = 1000  # 每个文本块的字符数
        self.chunk_overlap = 200  # 文本块之间的重叠字符数

        # 表格处理配置
        self.table_chunk_size = 2000  # 表格内容的块大小
        self.enable_table_extraction = ADVANCED_PDF_AVAILABLE

        # 初始化文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", "。", "！", "？", "；", "，", " ", ""]
        )

        # 表格专用分割器
        self.table_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.table_chunk_size,
            chunk_overlap=100,
            length_function=len,
            separators=["\n\n", "\n", "|", " ", ""]
        )
        
    def initialize(self):
        """初始化PDF处理器"""
        try:
            # 初始化RAG系统
            if not self.rag_system.initialize():
                logger.error("RAG系统初始化失败")
                return False
            
            logger.info("PDF处理器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"PDF处理器初始化失败: {e}")
            return False
    
    def load_pdf_with_pypdf(self, file_path: str) -> List[Document]:
        """使用PyPDFLoader加载PDF文件"""
        try:
            loader = PyPDFLoader(file_path)
            documents = loader.load()
            logger.info(f"使用PyPDFLoader成功加载PDF: {file_path}, 页数: {len(documents)}")
            return documents
        except Exception as e:
            logger.error(f"PyPDFLoader加载PDF失败: {e}")
            return []
    
    def load_pdf_with_unstructured(self, file_path: str) -> List[Document]:
        """使用UnstructuredPDFLoader加载PDF文件"""
        try:
            loader = UnstructuredPDFLoader(file_path)
            documents = loader.load()
            logger.info(f"使用UnstructuredPDFLoader成功加载PDF: {file_path}, 文档数: {len(documents)}")
            return documents
        except Exception as e:
            logger.error(f"UnstructuredPDFLoader加载PDF失败: {e}")
            return []
    
    def load_pdf(self, file_path: str, loader_type: str = "pypdf") -> List[Document]:
        """
        加载PDF文件
        
        Args:
            file_path: PDF文件路径
            loader_type: 加载器类型 ("pypdf" 或 "unstructured")
        
        Returns:
            文档列表
        """
        if not os.path.exists(file_path):
            logger.error(f"PDF文件不存在: {file_path}")
            return []
        
        if not file_path.lower().endswith('.pdf'):
            logger.error(f"文件不是PDF格式: {file_path}")
            return []
        
        logger.info(f"开始加载PDF文件: {file_path}")
        
        if loader_type == "pypdf":
            documents = self.load_pdf_with_pypdf(file_path)
        elif loader_type == "unstructured":
            documents = self.load_pdf_with_unstructured(file_path)
        else:
            logger.error(f"不支持的加载器类型: {loader_type}")
            return []
        
        # 如果第一种方法失败，尝试另一种方法
        if not documents and loader_type == "pypdf":
            logger.warning("PyPDFLoader失败，尝试使用UnstructuredPDFLoader")
            documents = self.load_pdf_with_unstructured(file_path)
        elif not documents and loader_type == "unstructured":
            logger.warning("UnstructuredPDFLoader失败，尝试使用PyPDFLoader")
            documents = self.load_pdf_with_pypdf(file_path)
        
        return documents

    def extract_tables_with_pdfplumber(self, file_path: str) -> List[Dict[str, Any]]:
        """使用pdfplumber提取PDF中的表格"""
        try:
            if not ADVANCED_PDF_AVAILABLE:
                logger.warning("pdfplumber未安装，无法提取表格")
                return []

            tables_data = []

            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # 提取表格
                    tables = page.extract_tables()

                    for table_num, table in enumerate(tables):
                        if table and len(table) > 1:  # 确保表格有内容
                            # 转换为DataFrame进行处理
                            df = pd.DataFrame(table[1:], columns=table[0])

                            # 清理数据
                            df = df.dropna(how='all').fillna('')

                            # 生成表格的文本描述
                            table_text = self._format_table_as_text(df, page_num + 1, table_num + 1)

                            table_info = {
                                "page_number": page_num + 1,
                                "table_number": table_num + 1,
                                "content": table_text,
                                "dataframe": df,
                                "raw_table": table,
                                "type": "table"
                            }
                            tables_data.append(table_info)

            logger.info(f"使用pdfplumber提取到 {len(tables_data)} 个表格")
            return tables_data

        except Exception as e:
            logger.error(f"pdfplumber提取表格失败: {e}")
            return []

    def extract_tables_with_pymupdf(self, file_path: str) -> List[Dict[str, Any]]:
        """使用PyMuPDF提取PDF中的表格"""
        try:
            if not ADVANCED_PDF_AVAILABLE:
                logger.warning("PyMuPDF未安装，无法提取表格")
                return []

            tables_data = []
            doc = fitz.open(file_path)

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)

                # 查找表格
                tables = page.find_tables()

                for table_num, table in enumerate(tables):
                    try:
                        # 提取表格数据
                        table_data = table.extract()

                        if table_data and len(table_data) > 1:
                            # 转换为DataFrame
                            df = pd.DataFrame(table_data[1:], columns=table_data[0])
                            df = df.dropna(how='all').fillna('')

                            # 生成表格文本
                            table_text = self._format_table_as_text(df, page_num + 1, table_num + 1)

                            table_info = {
                                "page_number": page_num + 1,
                                "table_number": table_num + 1,
                                "content": table_text,
                                "dataframe": df,
                                "raw_table": table_data,
                                "type": "table",
                                "bbox": table.bbox  # 表格边界框
                            }
                            tables_data.append(table_info)

                    except Exception as e:
                        logger.warning(f"处理第{page_num+1}页第{table_num+1}个表格失败: {e}")
                        continue

            doc.close()
            logger.info(f"使用PyMuPDF提取到 {len(tables_data)} 个表格")
            return tables_data

        except Exception as e:
            logger.error(f"PyMuPDF提取表格失败: {e}")
            return []

    def _format_table_as_text(self, df: pd.DataFrame, page_num: int, table_num: int) -> str:
        """将DataFrame格式化为可搜索的文本"""
        try:
            # 表格标题
            title = f"第{page_num}页表格{table_num}:\n"

            # 表格摘要信息
            summary = f"表格规模: {df.shape[0]}行 x {df.shape[1]}列\n"

            # 列名信息
            columns_info = f"列名: {', '.join(df.columns.astype(str))}\n"

            # 表格内容 - 转换为易于搜索的格式
            content_lines = []

            # 添加列标题行
            header_line = " | ".join(df.columns.astype(str))
            content_lines.append(header_line)
            content_lines.append("-" * len(header_line))

            # 添加数据行
            for idx, row in df.iterrows():
                row_line = " | ".join(row.astype(str))
                content_lines.append(row_line)

                # 为了便于搜索，也添加键值对格式
                for col, val in row.items():
                    if val and str(val).strip():
                        content_lines.append(f"{col}: {val}")

            # 组合所有内容
            table_text = title + summary + columns_info + "\n" + "\n".join(content_lines)

            return table_text

        except Exception as e:
            logger.error(f"格式化表格文本失败: {e}")
            return f"第{page_num}页表格{table_num} (格式化失败)"

    def split_documents(self, documents: List[Document]) -> List[Document]:
        """分割文档为小块"""
        try:
            if not documents:
                logger.warning("没有文档需要分割")
                return []
            
            # 使用文本分割器分割文档
            split_docs = self.text_splitter.split_documents(documents)
            
            logger.info(f"文档分割完成: {len(documents)} -> {len(split_docs)} 块")
            return split_docs
            
        except Exception as e:
            logger.error(f"文档分割失败: {e}")
            return []
    
    def extract_metadata(self, document: Document, file_path: str, chunk_index: int) -> Dict[str, Any]:
        """提取文档元数据"""
        try:
            metadata = {
                "source": file_path,
                "file_name": os.path.basename(file_path),
                "chunk_index": chunk_index,
                "content_length": len(document.page_content),
                "category": "金融文档"  # 默认分类
            }
            
            # 合并原有元数据
            if hasattr(document, 'metadata') and document.metadata:
                metadata.update(document.metadata)
            
            return metadata
            
        except Exception as e:
            logger.error(f"提取元数据失败: {e}")
            return {"source": file_path, "category": "金融文档"}
    
    def process_pdf_to_knowledge_base(self, file_path: str, category: str = "金融文档",
                                    loader_type: str = "pypdf", extract_tables: bool = True) -> bool:
        """
        处理PDF文件并添加到知识库

        Args:
            file_path: PDF文件路径
            category: 文档分类
            loader_type: 加载器类型
            extract_tables: 是否提取表格

        Returns:
            处理是否成功
        """
        try:
            logger.info(f"开始处理PDF文件: {file_path}")
            
            # 1. 加载PDF文档
            documents = self.load_pdf(file_path, loader_type)
            if not documents:
                logger.error("PDF文档加载失败")
                return False
            
            # 2. 分割文档
            split_docs = self.split_documents(documents)
            if not split_docs:
                logger.error("文档分割失败")
                return False
            
            # 3. 提取表格（如果启用）
            table_knowledge_list = []
            if extract_tables and self.enable_table_extraction:
                logger.info("开始提取PDF表格...")

                # 尝试使用pdfplumber提取表格
                tables = self.extract_tables_with_pdfplumber(file_path)

                # 如果pdfplumber失败，尝试PyMuPDF
                if not tables:
                    tables = self.extract_tables_with_pymupdf(file_path)

                # 处理提取的表格
                for table_info in tables:
                    table_content = table_info["content"]
                    if len(table_content) > 50:  # 确保表格有足够内容
                        # 分割表格内容
                        table_docs = self.table_splitter.split_text(table_content)

                        for j, table_chunk in enumerate(table_docs):
                            knowledge_item = {
                                "content": table_chunk,
                                "category": f"{category} - 表格",
                                "source": f"{os.path.basename(file_path)} - 第{table_info['page_number']}页表格{table_info['table_number']} - 第{j+1}块"
                            }
                            table_knowledge_list.append(knowledge_item)

                logger.info(f"提取到 {len(tables)} 个表格，生成 {len(table_knowledge_list)} 个表格知识块")

            # 4. 准备文本知识库数据
            text_knowledge_list = []
            for i, doc in enumerate(split_docs):
                # 清理文本内容
                content = doc.page_content.strip()
                if len(content) < 10:  # 跳过过短的内容
                    continue

                # 提取元数据
                metadata = self.extract_metadata(doc, file_path, i)

                knowledge_item = {
                    "content": content,
                    "category": category,
                    "source": f"{os.path.basename(file_path)} - 第{i+1}块"
                }
                text_knowledge_list.append(knowledge_item)

            # 合并文本和表格知识
            knowledge_list = text_knowledge_list + table_knowledge_list
            
            # 5. 批量添加到知识库
            if knowledge_list:
                success = self.rag_system.batch_add_knowledge(knowledge_list)
                if success:
                    logger.info(f"成功处理PDF文件: {file_path}, 添加了 {len(knowledge_list)} 个知识块")
                    return True
                else:
                    logger.error("批量添加知识到数据库失败")
                    return False
            else:
                logger.warning("没有有效的知识内容可添加")
                return False
                
        except Exception as e:
            logger.error(f"处理PDF文件失败: {e}")
            return False
    
    def process_pdf_directory(self, directory_path: str, category: str = "金融文档") -> Dict[str, bool]:
        """
        批量处理目录中的所有PDF文件
        
        Args:
            directory_path: 目录路径
            category: 文档分类
        
        Returns:
            处理结果字典 {文件名: 是否成功}
        """
        try:
            if not os.path.exists(directory_path):
                logger.error(f"目录不存在: {directory_path}")
                return {}
            
            # 查找所有PDF文件
            pdf_files = []
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    if file.lower().endswith('.pdf'):
                        pdf_files.append(os.path.join(root, file))
            
            if not pdf_files:
                logger.warning(f"目录中没有找到PDF文件: {directory_path}")
                return {}
            
            logger.info(f"找到 {len(pdf_files)} 个PDF文件，开始批量处理")
            
            # 批量处理
            results = {}
            for pdf_file in pdf_files:
                try:
                    success = self.process_pdf_to_knowledge_base(pdf_file, category)
                    results[os.path.basename(pdf_file)] = success
                    logger.info(f"文件 {os.path.basename(pdf_file)} 处理{'成功' if success else '失败'}")
                except Exception as e:
                    logger.error(f"处理文件 {pdf_file} 时出错: {e}")
                    results[os.path.basename(pdf_file)] = False
            
            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            logger.info(f"批量处理完成: {success_count}/{total_count} 个文件成功")
            
            return results
            
        except Exception as e:
            logger.error(f"批量处理PDF目录失败: {e}")
            return {}
    
    def extract_table_only(self, file_path: str) -> List[Dict[str, Any]]:
        """仅提取PDF中的表格，不处理文本"""
        try:
            logger.info(f"开始提取PDF表格: {file_path}")

            # 尝试使用pdfplumber提取表格
            tables = self.extract_tables_with_pdfplumber(file_path)

            # 如果pdfplumber失败，尝试PyMuPDF
            if not tables:
                tables = self.extract_tables_with_pymupdf(file_path)

            return tables

        except Exception as e:
            logger.error(f"提取表格失败: {e}")
            return []

    def search_tables_in_pdf(self, file_path: str, query: str) -> List[Dict[str, Any]]:
        """在PDF的表格中搜索特定内容"""
        try:
            tables = self.extract_table_only(file_path)
            matching_tables = []

            for table_info in tables:
                df = table_info.get("dataframe")
                if df is not None:
                    # 在DataFrame中搜索
                    matches = self._search_in_dataframe(df, query)
                    if matches:
                        table_info["matches"] = matches
                        matching_tables.append(table_info)

            return matching_tables

        except Exception as e:
            logger.error(f"在PDF表格中搜索失败: {e}")
            return []

    def _search_in_dataframe(self, df: pd.DataFrame, query: str) -> List[Dict[str, Any]]:
        """在DataFrame中搜索匹配的内容"""
        try:
            matches = []
            query_lower = query.lower()

            # 搜索所有单元格
            for row_idx, row in df.iterrows():
                for col_name, cell_value in row.items():
                    cell_str = str(cell_value).lower()
                    if query_lower in cell_str:
                        matches.append({
                            "row": row_idx,
                            "column": col_name,
                            "value": cell_value,
                            "context": row.to_dict()
                        })

            return matches

        except Exception as e:
            logger.error(f"DataFrame搜索失败: {e}")
            return []

    def analyze_table_structure(self, file_path: str) -> Dict[str, Any]:
        """分析PDF中表格的结构"""
        try:
            tables = self.extract_table_only(file_path)

            analysis = {
                "total_tables": len(tables),
                "tables_by_page": {},
                "column_analysis": {},
                "data_types": {}
            }

            for table_info in tables:
                page_num = table_info["page_number"]
                df = table_info.get("dataframe")

                # 按页面统计
                if page_num not in analysis["tables_by_page"]:
                    analysis["tables_by_page"][page_num] = 0
                analysis["tables_by_page"][page_num] += 1

                # 分析列结构
                if df is not None:
                    for col in df.columns:
                        col_str = str(col)
                        if col_str not in analysis["column_analysis"]:
                            analysis["column_analysis"][col_str] = 0
                        analysis["column_analysis"][col_str] += 1

                        # 分析数据类型
                        sample_values = df[col].dropna().head(5)
                        if len(sample_values) > 0:
                            analysis["data_types"][col_str] = self._infer_column_type(sample_values)

            return analysis

        except Exception as e:
            logger.error(f"分析表格结构失败: {e}")
            return {}

    def _infer_column_type(self, sample_values) -> str:
        """推断列的数据类型"""
        try:
            # 尝试转换为数字
            numeric_count = 0
            date_count = 0

            for value in sample_values:
                value_str = str(value).strip()

                # 检查是否为数字
                try:
                    float(value_str.replace(',', '').replace('%', ''))
                    numeric_count += 1
                except:
                    pass

                # 检查是否为日期
                if any(char in value_str for char in ['年', '月', '日', '-', '/']):
                    date_count += 1

            total = len(sample_values)
            if numeric_count / total > 0.7:
                return "数值型"
            elif date_count / total > 0.5:
                return "日期型"
            else:
                return "文本型"

        except Exception as e:
            return "未知"

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        try:
            stats = {
                "chunk_size": self.chunk_size,
                "chunk_overlap": self.chunk_overlap,
                "table_chunk_size": self.table_chunk_size,
                "text_splitter_type": "RecursiveCharacterTextSplitter",
                "table_extraction_enabled": self.enable_table_extraction,
                "advanced_pdf_available": ADVANCED_PDF_AVAILABLE
            }
            return stats
        except Exception as e:
            logger.error(f"获取处理统计信息失败: {e}")
            return {}
