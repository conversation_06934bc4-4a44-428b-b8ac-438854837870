<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金融对话助手</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-chart-line"></i> 金融助手</h2>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <!-- 新对话按钮 -->
            <button class="new-chat-btn" id="newChatBtn">
                <i class="fas fa-plus"></i> 新对话
            </button>
            
            <!-- 对话历史 -->
            <div class="chat-history">
                <h3>对话历史</h3>
                <div class="history-list" id="historyList">
                    <!-- 历史对话项将在这里动态添加 -->
                </div>
            </div>
            
            <!-- 功能区域 -->
            <div class="features-section">
                <button class="feature-btn" id="knowledgeBtn">
                    <i class="fas fa-database"></i> 知识库
                </button>
                <button class="feature-btn" id="searchBtn">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>

            <!-- 设置区域 -->
            <div class="settings-section">
                <button class="settings-btn" id="settingsBtn">
                    <i class="fas fa-cog"></i> 设置
                </button>
            </div>
        </div>
        
        <!-- 主聊天区域 -->
        <div class="main-content">
            <!-- 顶部工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <button class="mobile-menu-btn" id="mobileMenuBtn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>金融对话助手</h1>
                </div>
                <div class="toolbar-right">
                    <div class="model-selector">
                        <label for="modelSelect">模型:</label>
                        <select id="modelSelect">
                            <option value="openai">OpenAI</option>
                            <option value="zhipu">智谱AI</option>
                            <option value="qwen">通义千问</option>
                            <option value="siliconflow">硅基流动</option>
                            <option value="baidu">百度文心</option>
                            <option value="moonshot">Moonshot</option>
                            <option value="deepseek">DeepSeek</option>
                            <option value="doubao">豆包</option>
                            <option value="anthropic">Claude</option>
                            <option value="google">Gemini</option>
                        </select>
                    </div>
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
            
            <!-- 聊天消息区域 -->
            <div class="chat-container">
                <div class="messages-container" id="messagesContainer">
                    <!-- 欢迎消息 -->
                    <div class="message assistant-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                您好！我是您的金融对话助手，可以为您解答各种金融问题。请随时向我提问！
                            </div>
                            <div class="message-time">
                                刚刚
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="input-container">
                    <div class="input-wrapper">
                        <textarea 
                            id="messageInput" 
                            placeholder="请输入您的金融问题..." 
                            rows="1"
                            maxlength="2000"
                        ></textarea>
                        <div class="input-actions">
                            <button class="attach-btn" id="attachBtn" title="上传PDF文档">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button class="send-btn" id="sendBtn" title="发送消息">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                    <div class="input-footer">
                        <span class="char-count" id="charCount">0/2000</span>
                        <span class="typing-indicator" id="typingIndicator" style="display: none;">
                            <i class="fas fa-circle"></i>
                            <i class="fas fa-circle"></i>
                            <i class="fas fa-circle"></i>
                            AI正在思考...
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 设置模态框 -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>设置</h3>
                <button class="modal-close" id="settingsModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label>API地址:</label>
                    <input type="text" id="apiUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
                </div>
                <div class="setting-group">
                    <label>温度参数:</label>
                    <input type="range" id="temperature" min="0" max="1" step="0.1" value="0.7">
                    <span id="temperatureValue">0.7</span>
                </div>
                <div class="setting-group">
                    <label>主题:</label>
                    <select id="themeSelect">
                        <option value="light">浅色主题</option>
                        <option value="dark">深色主题</option>
                        <option value="auto">跟随系统</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="autoSave"> 自动保存对话
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="settingsCancel">取消</button>
                <button class="btn btn-primary" id="settingsSave">保存</button>
            </div>
        </div>
    </div>
    
    <!-- PDF上传模态框 -->
    <div class="modal" id="pdfModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>上传PDF文档</h3>
                <button class="modal-close" id="pdfModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-info">
                    <h4><i class="fas fa-info-circle"></i> PDF上传说明</h4>
                    <ul>
                        <li>📚 文件将自动添加到<strong>金融专业信息和术语知识库</strong></li>
                        <li>🔍 上传后可以向AI助手询问文档相关问题</li>
                        <li>📄 支持金融报告、研究报告、招股说明书等</li>
                        <li>⚡ 文件大小限制：10MB以内</li>
                    </ul>
                </div>
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>拖拽PDF文件到此处或点击选择文件</p>
                    <input type="file" id="pdfFileInput" accept=".pdf" style="display: none;">
                    <button class="btn btn-secondary" id="selectFileBtn">选择文件</button>
                </div>
                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span id="progressText">上传中...</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在连接服务器...</p>
        </div>
    </div>
    
    <!-- 知识库管理模态框 -->
    <div class="modal" id="knowledgeModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>知识库管理</h3>
                <button class="modal-close" id="knowledgeModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="knowledge-tabs">
                    <button class="tab-btn active" data-tab="add">添加知识</button>
                    <button class="tab-btn" data-tab="search">搜索知识</button>
                    <button class="tab-btn" data-tab="manage">管理知识</button>
                </div>

                <!-- 添加知识标签页 -->
                <div class="tab-content active" id="addTab">
                    <div class="form-group">
                        <label for="knowledgeContent">知识内容:</label>
                        <textarea id="knowledgeContent" rows="6" placeholder="请输入知识内容..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="knowledgeCategory">分类:</label>
                        <input type="text" id="knowledgeCategory" placeholder="例如：股票基础、债券知识等">
                    </div>
                    <div class="form-group">
                        <label for="knowledgeSource">来源:</label>
                        <input type="text" id="knowledgeSource" placeholder="知识来源（可选）">
                    </div>
                    <button class="btn btn-primary" id="addKnowledgeBtn">
                        <i class="fas fa-plus"></i> 添加知识
                    </button>
                </div>

                <!-- 搜索知识标签页 -->
                <div class="tab-content" id="searchTab">
                    <div class="search-box">
                        <input type="text" id="knowledgeSearchInput" placeholder="搜索知识库...">
                        <button class="btn btn-primary" id="searchKnowledgeBtn">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                    </div>
                    <div class="search-results" id="knowledgeSearchResults">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>

                <!-- 管理知识标签页 -->
                <div class="tab-content" id="manageTab">
                    <div class="knowledge-stats">
                        <div class="stat-item">
                            <span class="stat-label">总知识条数:</span>
                            <span class="stat-value" id="totalKnowledge">-</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">分类数量:</span>
                            <span class="stat-value" id="totalCategories">-</span>
                        </div>
                    </div>
                    <button class="btn btn-secondary" id="refreshStatsBtn">
                        <i class="fas fa-refresh"></i> 刷新统计
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索模态框 -->
    <div class="modal" id="searchModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>搜索对话历史</h3>
                <button class="modal-close" id="searchModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="search-box">
                    <input type="text" id="historySearchInput" placeholder="搜索历史对话...">
                    <button class="btn btn-primary" id="searchHistoryBtn">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
                <div class="search-results" id="historySearchResults">
                    <!-- 搜索结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="hiddenFileInput" accept=".pdf" style="display: none;">

    <script src="script.js"></script>
</body>
</html>
